"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitySubmitController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const activity_submit_service_1 = require("./activity_submit.service");
const create_activity_submit_dto_1 = require("./dto/create-activity-submit.dto");
const update_activity_submit_dto_1 = require("./dto/update-activity-submit.dto");
const activity_submit_entity_1 = require("./entities/activity_submit.entity");
let ActivitySubmitController = class ActivitySubmitController {
    activitySubmitService;
    constructor(activitySubmitService) {
        this.activitySubmitService = activitySubmitService;
    }
    async create(createActivitySubmitDto) {
        return this.activitySubmitService.create(createActivitySubmitDto);
    }
    async findAll() {
        return this.activitySubmitService.findAll();
    }
    async findByActivity(activityId) {
        return this.activitySubmitService.findByActivity(activityId);
    }
    async findByUser(userId) {
        return this.activitySubmitService.findByUser(userId);
    }
    async checkUserSubmit(activityId, userId) {
        const submit = await this.activitySubmitService.findUserSubmitForActivity(activityId, userId);
        return {
            submitted: !!submit,
            submit: submit || undefined,
        };
    }
    async getStatistics(activityId) {
        return this.activitySubmitService.getSubmitStatistics(activityId);
    }
    async findOne(id) {
        return this.activitySubmitService.findOne(id);
    }
    async update(id, updateActivitySubmitDto) {
        return this.activitySubmitService.update(id, updateActivitySubmitDto);
    }
    async cancel(id) {
        return this.activitySubmitService.cancel(id);
    }
    async cancelByUser(activityId, userId) {
        return this.activitySubmitService.cancelByUser(activityId, userId);
    }
    async remove(id) {
        return this.activitySubmitService.remove(id);
    }
};
exports.ActivitySubmitController = ActivitySubmitController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建活动报名' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '报名成功', type: activity_submit_entity_1.ActivitySubmit }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '活动不存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_submit_dto_1.CreateActivitySubmitDto]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有活动报名记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_submit_entity_1.ActivitySubmit] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '根据活动ID获取报名记录' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_submit_entity_1.ActivitySubmit] }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "findByActivity", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取报名记录' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_submit_entity_1.ActivitySubmit] }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "findByUser", null);
__decorate([
    (0, common_1.Get)('check/:activityId/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户是否已报名某活动' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检查成功' }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "checkUserSubmit", null);
__decorate([
    (0, common_1.Get)('statistics/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '获取活动报名统计信息' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取报名记录详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '报名记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: activity_submit_entity_1.ActivitySubmit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '报名记录不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新报名记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '报名记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: activity_submit_entity_1.ActivitySubmit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '报名记录不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_activity_submit_dto_1.UpdateActivitySubmitDto]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/cancel'),
    (0, swagger_1.ApiOperation)({ summary: '取消报名' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '报名记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功', type: activity_submit_entity_1.ActivitySubmit }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '无法取消' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '报名记录不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "cancel", null);
__decorate([
    (0, common_1.Patch)('cancel/:activityId/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '用户取消活动报名' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功', type: activity_submit_entity_1.ActivitySubmit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '未找到可取消的报名记录' }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "cancelByUser", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除报名记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '报名记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitySubmitController.prototype, "remove", null);
exports.ActivitySubmitController = ActivitySubmitController = __decorate([
    (0, swagger_1.ApiTags)('活动报名管理'),
    (0, common_1.Controller)('activity-submit'),
    __metadata("design:paramtypes", [activity_submit_service_1.ActivitySubmitService])
], ActivitySubmitController);
//# sourceMappingURL=activity_submit.controller.js.map