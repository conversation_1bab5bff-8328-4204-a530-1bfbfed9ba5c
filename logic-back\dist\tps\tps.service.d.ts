import { UserInfoService } from 'src/util/database/mysql/user_info/user_info.service';
import { UserStudentService } from 'src/util/database/mysql/user_student/user_student.service';
import { UserSchoolRelationService } from 'src/web/user_school_relation/user_school_relation.service';
import { UserClassService } from 'src/util/database/mysql/user_class/user_class.service';
import { PackageInfoService } from 'src/util/database/mysql/package_info/package_info.service';
import { UserPackageService } from 'src/util/database/mysql/user_package/user_package.service';
import { UserPointsService } from 'src/util/database/mysql/user_points/user_points.service';
import { Repository } from 'typeorm';
import { UserInfo } from 'src/util/database/mysql/user_info/entities/user_info.entity';
export declare class TPSService {
    private readonly userStudentService;
    private readonly userInfoService;
    private readonly userSchoolRelationService;
    private readonly baseUserClassService;
    private readonly packageInfoService;
    private readonly userPackageService;
    private readonly userPointsService;
    private readonly userInfoRepository;
    constructor(userStudentService: UserStudentService, userInfoService: UserInfoService, userSchoolRelationService: UserSchoolRelationService, baseUserClassService: UserClassService, packageInfoService: PackageInfoService, userPackageService: UserPackageService, userPointsService: UserPointsService, userInfoRepository: Repository<UserInfo>);
    createTestStudents(classId: number, count?: number, schoolId?: number): Promise<{
        success: boolean;
        message: string;
        usersCreated: number;
        studentsCreated: number;
        userIds: number[];
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        usersCreated: number;
        studentsCreated: number;
        errors: any[];
        userIds?: undefined;
    }>;
    createCompleteTestData(schoolId: number, teacherCount?: number, studentCount?: number): Promise<{
        success: boolean;
        message: string;
        data: {
            teachersCreated: number;
            classesCreated: number;
            studentsCreated: number;
            userIds: number[];
            details: {
                teachers: any[];
                classes: any[];
                students: any[];
                errors: any[];
            };
        };
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data: {
            teachers: any[];
            classes: any[];
            students: any[];
            errors: any[];
        };
        errors: any[];
    }>;
    createTestTeachers(schoolId: number, count?: number): Promise<{
        success: boolean;
        message: string;
        usersCreated: number;
        userIds: number[];
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        usersCreated: number;
        errors: any[];
        userIds?: undefined;
    }>;
    deleteCompleteTestData(timestamp: string): Promise<{
        success: boolean;
        message: string;
        data?: undefined;
        errors?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        data: {
            deletedUsers: number;
            deletedClasses: number;
            totalFound: number;
        };
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
        errors?: undefined;
    }>;
    deleteByPrefix(prefix: string): Promise<{
        success: boolean;
        message: string;
        errors?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        errors?: undefined;
    }>;
    exportTestDataToCsv(timestamp: string): Promise<{
        success: boolean;
        message: string;
        data?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        data: {
            csvContent: string;
            filename: string;
            count: number;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    assignSpecialPackageToAllStudents(packageId: number, schoolId: number, operatorId: number, remark?: string, showInMessageCenter?: boolean): Promise<{
        success: boolean;
        message: string;
        data: {
            packageInfo: {
                id: number;
                name: string;
                points: number;
                validityDays: number;
            };
            assignedCount: number;
            totalStudents: number;
            skippedCount: number;
            errors: any[] | null;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
}
