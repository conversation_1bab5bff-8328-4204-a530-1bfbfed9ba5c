"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SwaggerService = void 0;
const swagger_1 = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
let SwaggerService = class SwaggerService {
    createBearerAuthConfig() {
        return {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: '输入JWT token',
            in: 'header',
        };
    }
    setup(app) {
        const defaultConfig = new swagger_1.DocumentBuilder()
            .setTitle('Logic后端API')
            .setDescription('所有API接口文档（通过在导航栏输入api/:str ,可以进入二级分组的api ，例如 database/数据库相关api,external/外部服务api,system/系统api）')
            .setVersion('1.0')
            .addBearerAuth(this.createBearerAuthConfig(), 'access-token')
            .build();
        const customOptions = {
            swaggerOptions: {
                persistAuthorization: true,
                docExpansion: 'none',
                tagsSorter: 'alpha',
                operationsSorter: 'alpha',
                filter: true,
            },
        };
        const dbConfig = new swagger_1.DocumentBuilder()
            .setTitle('数据库API')
            .setDescription('数据库相关接口')
            .setVersion('1.0')
            .addBearerAuth(this.createBearerAuthConfig(), 'access-token')
            .build();
        const externalConfig = new swagger_1.DocumentBuilder()
            .setTitle('外部服务API')
            .setDescription('外部服务相关接口')
            .setVersion('1.0')
            .addBearerAuth(this.createBearerAuthConfig(), 'access-token')
            .build();
        const systemConfig = new swagger_1.DocumentBuilder()
            .setTitle('系统API')
            .setDescription('系统相关接口')
            .setVersion('1.0')
            .addBearerAuth(this.createBearerAuthConfig(), 'access-token')
            .build();
        const webConfig = new swagger_1.DocumentBuilder()
            .setTitle('webAPI')
            .setDescription('web相关接口')
            .setVersion('1.0')
            .addBearerAuth(this.createBearerAuthConfig(), 'access-token')
            .build();
        const defaultDocument = swagger_1.SwaggerModule.createDocument(app, defaultConfig);
        swagger_1.SwaggerModule.setup('swagger', app, defaultDocument, customOptions);
        const dbDocument = swagger_1.SwaggerModule.createDocument(app, dbConfig, {
            include: [],
            extraModels: [],
            operationIdFactory: (controllerKey, methodKey) => `${controllerKey}_${methodKey}`,
        });
        this.filterApisByTag(dbDocument, '数据库/');
        swagger_1.SwaggerModule.setup('swagger/database', app, dbDocument, customOptions);
        const externalDocument = swagger_1.SwaggerModule.createDocument(app, externalConfig);
        this.filterApisByTag(externalDocument, '外部服务/');
        swagger_1.SwaggerModule.setup('swagger/external', app, externalDocument, customOptions);
        const systemDocument = swagger_1.SwaggerModule.createDocument(app, systemConfig);
        this.filterApisByTag(systemDocument, '系统/');
        swagger_1.SwaggerModule.setup('swagger/system', app, systemDocument, customOptions);
        const webDocument = swagger_1.SwaggerModule.createDocument(app, webConfig);
        this.filterApisByTag(webDocument, 'web/');
        swagger_1.SwaggerModule.setup('swagger/web', app, webDocument, customOptions);
    }
    filterApisByTag(document, tagPrefix) {
        if (!document || !document.paths)
            return;
        if (document.tags) {
            document.tags = document.tags.filter(tag => tag.name.startsWith(tagPrefix));
        }
        Object.keys(document.paths).forEach(path => {
            const pathItem = document.paths[path];
            if (!pathItem)
                return;
            ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].forEach(method => {
                const operation = pathItem[method];
                if (!operation)
                    return;
                if (!operation.tags || !operation.tags.some(tag => tag.startsWith(tagPrefix))) {
                    delete pathItem[method];
                }
            });
            if (Object.keys(pathItem).length === 0) {
                delete document.paths[path];
            }
        });
    }
};
exports.SwaggerService = SwaggerService;
exports.SwaggerService = SwaggerService = __decorate([
    (0, common_1.Injectable)()
], SwaggerService);
//# sourceMappingURL=swagger.service.js.map