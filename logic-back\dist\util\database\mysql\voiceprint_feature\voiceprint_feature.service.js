"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintFeatureService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const voiceprint_feature_entity_1 = require("./entities/voiceprint-feature.entity");
let VoiceprintFeatureService = class VoiceprintFeatureService {
    voiceprintFeatureRepository;
    constructor(voiceprintFeatureRepository) {
        this.voiceprintFeatureRepository = voiceprintFeatureRepository;
    }
    async create(createVoiceprintFeatureDto) {
        try {
            console.log('开始创建声纹特征记录:', createVoiceprintFeatureDto);
            try {
                const existingFeature = await this.voiceprintFeatureRepository.findOne({
                    where: {
                        groupId: createVoiceprintFeatureDto.groupId,
                        featureId: createVoiceprintFeatureDto.featureId
                    }
                });
                if (existingFeature) {
                    console.log('已存在相同的声纹特征，返回现有记录');
                    return existingFeature;
                }
            }
            catch (findError) {
                console.log('查找现有声纹特征出错:', findError);
            }
            console.log('创建新的声纹特征实体');
            const voiceprintFeature = this.voiceprintFeatureRepository.create(createVoiceprintFeatureDto);
            console.log('实体创建成功，准备保存:', voiceprintFeature);
            const savedFeature = await this.voiceprintFeatureRepository.save(voiceprintFeature);
            console.log('声纹特征保存成功:', savedFeature);
            return savedFeature;
        }
        catch (error) {
            console.error('创建声纹特征记录失败:', error);
            throw error;
        }
    }
    async findAll() {
        return await this.voiceprintFeatureRepository.find();
    }
    async findOne(id) {
        const voiceprintFeature = await this.voiceprintFeatureRepository.findOne({ where: { id } });
        if (!voiceprintFeature) {
            throw new common_1.NotFoundException(`未找到ID为${id}的声纹特征`);
        }
        return voiceprintFeature;
    }
    async findByUserId(userId) {
        return await this.voiceprintFeatureRepository.find({ where: { userId } });
    }
    async findByGroupId(groupId) {
        return await this.voiceprintFeatureRepository.find({ where: { groupId } });
    }
    async findByFeatureIdAndGroupId(featureId, groupId) {
        const voiceprintFeature = await this.voiceprintFeatureRepository.findOne({
            where: { featureId, groupId }
        });
        if (!voiceprintFeature) {
            throw new common_1.NotFoundException(`未找到指定的声纹特征`);
        }
        return voiceprintFeature;
    }
    async update(id, updateVoiceprintFeatureDto) {
        const voiceprintFeature = await this.findOne(id);
        Object.assign(voiceprintFeature, updateVoiceprintFeatureDto);
        return await this.voiceprintFeatureRepository.save(voiceprintFeature);
    }
    async remove(id) {
        const voiceprintFeature = await this.findOne(id);
        await this.voiceprintFeatureRepository.remove(voiceprintFeature);
    }
    async removeByGroupId(groupId) {
        await this.voiceprintFeatureRepository.delete({ groupId });
    }
};
exports.VoiceprintFeatureService = VoiceprintFeatureService;
exports.VoiceprintFeatureService = VoiceprintFeatureService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(voiceprint_feature_entity_1.VoiceprintFeature)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], VoiceprintFeatureService);
//# sourceMappingURL=voiceprint_feature.service.js.map