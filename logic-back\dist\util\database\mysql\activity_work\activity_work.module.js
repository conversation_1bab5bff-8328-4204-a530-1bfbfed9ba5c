"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityWorkModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const activity_work_service_1 = require("./activity_work.service");
const activity_work_controller_1 = require("./activity_work.controller");
const activity_work_entity_1 = require("./entities/activity_work.entity");
let ActivityWorkModule = class ActivityWorkModule {
};
exports.ActivityWorkModule = ActivityWorkModule;
exports.ActivityWorkModule = ActivityWorkModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([activity_work_entity_1.ActivityWork])],
        controllers: [activity_work_controller_1.ActivityWorkController],
        providers: [activity_work_service_1.ActivityWorkService],
        exports: [activity_work_service_1.ActivityWorkService],
    })
], ActivityWorkModule);
//# sourceMappingURL=activity_work.module.js.map