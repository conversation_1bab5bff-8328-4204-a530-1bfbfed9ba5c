"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecureExampleController = void 0;
const common_1 = require("@nestjs/common");
const encrypt_decorator_1 = require("./encrypt.decorator");
const encryption_service_1 = require("./encryption.service");
let SecureExampleController = class SecureExampleController {
    encryptionService;
    constructor(encryptionService) {
        this.encryptionService = encryptionService;
    }
    standardEncryption(data) {
        return {
            message: '这是使用标准会话加密的响应',
            receivedData: data,
            timestamp: new Date().toISOString(),
            sessionType: 'standard'
        };
    }
    secureEncryption(data) {
        return {
            message: '这是使用安全会话加密的响应',
            receivedData: data,
            timestamp: new Date().toISOString(),
            sessionType: 'secure',
            warning: '此会话将在请求完成后自动销毁，请求完成后需要重新创建会话'
        };
    }
    securePartialEncryption(data) {
        return {
            message: '部分字段已加密',
            publicInfo: '这是公开信息',
            sensitiveData: {
                cardNumber: '1234-5678-9012-3456',
                cvv: '123',
                expiryDate: '12/25'
            },
            user: {
                name: '张三',
                email: '<EMAIL>',
                creditCard: {
                    type: 'Visa',
                    lastFourDigits: '3456'
                }
            },
            timestamp: new Date().toISOString()
        };
    }
    getSessionStats() {
        this.encryptionService.logSessionStats();
        return {
            message: '会话统计信息已记录到日志'
        };
    }
};
exports.SecureExampleController = SecureExampleController;
__decorate([
    (0, common_1.Post)('standard'),
    (0, encrypt_decorator_1.Encrypt)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], SecureExampleController.prototype, "standardEncryption", null);
__decorate([
    (0, common_1.Post)('secure'),
    (0, encrypt_decorator_1.SecureEncrypt)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], SecureExampleController.prototype, "secureEncryption", null);
__decorate([
    (0, common_1.Post)('secure-partial'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        encryptFields: ['sensitiveData', 'user.creditCard']
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], SecureExampleController.prototype, "securePartialEncryption", null);
__decorate([
    (0, common_1.Get)('sessions/stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SecureExampleController.prototype, "getSessionStats", null);
exports.SecureExampleController = SecureExampleController = __decorate([
    (0, common_1.Controller)('api/secure-examples'),
    __metadata("design:paramtypes", [encryption_service_1.EncryptionService])
], SecureExampleController);
//# sourceMappingURL=secure-example.controller.js.map