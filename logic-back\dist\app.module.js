"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const scratch_module_1 = require("./scratch/scratch.module");
const web_module_1 = require("./web/web.module");
const util_module_1 = require("./util/util.module");
const common_services_module_1 = require("./common/services/common-services.module");
const tps_module_1 = require("./tps/tps.module");
const ai_voiceprint_recognition_module_1 = require("./scratch/ai_voiceprint_recognition/ai_voiceprint_recognition.module");
const user_role_template_task_service_1 = require("./web/user_role/user_role_template_task.service");
const user_role_module_1 = require("./web/user_role/user_role.module");
const payment_module_1 = require("./payment/payment.module");
const logger_module_1 = require("./common/logger/logger.module");
const http_logger_middleware_1 = require("./common/logger/http-logger.middleware");
let AppModule = class AppModule {
    userRoleTemplateTaskService;
    constructor(userRoleTemplateTaskService) {
        this.userRoleTemplateTaskService = userRoleTemplateTaskService;
    }
    configure(consumer) {
        consumer
            .apply(http_logger_middleware_1.HttpLoggerMiddleware)
            .forRoutes('*');
    }
    async onModuleInit() {
        console.log('App模块初始化...');
        if (this.userRoleTemplateTaskService) {
            try {
                console.log('通过App模块手动触发模板更新...');
                await this.userRoleTemplateTaskService.onModuleInit();
            }
            catch (error) {
                console.error('手动触发模板更新失败:', error);
            }
        }
        else {
            console.warn('UserRoleTemplateTaskService未注入到AppModule，无法手动调用');
        }
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            logger_module_1.LoggerModule,
            web_module_1.WebModule,
            scratch_module_1.ScratchModule,
            util_module_1.UtilModule,
            common_services_module_1.CommonServicesModule,
            tps_module_1.TPSModule,
            ai_voiceprint_recognition_module_1.AiVoiceprintRecognitionModule,
            user_role_module_1.UserRoleModule,
            payment_module_1.PaymentModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    }),
    __metadata("design:paramtypes", [user_role_template_task_service_1.UserRoleTemplateTaskService])
], AppModule);
//# sourceMappingURL=app.module.js.map