"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateActivityEventsTaskDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateActivityEventsTaskDto {
    userId;
    activityId;
    eventName;
    startTime;
    endTime;
    workId;
    workFile;
    workDescription;
    instructorName;
    schoolName;
    contactPerson;
    contactPhone;
    realName;
    idNumber;
    affiliatedSchool;
    organization;
    instructorPhone;
    competitionGroup;
    registrationFormFile;
    creatorId;
    remark;
}
exports.CreateActivityEventsTaskDto = CreateActivityEventsTaskDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    __metadata("design:type", Number)
], CreateActivityEventsTaskDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '活动ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '活动ID必须是数字' }),
    __metadata("design:type", Number)
], CreateActivityEventsTaskDto.prototype, "activityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '赛事名称' }),
    (0, class_validator_1.IsNotEmpty)({ message: '赛事名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '赛事名称必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "eventName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间' }),
    (0, class_validator_1.IsNotEmpty)({ message: '开始时间不能为空' }),
    __metadata("design:type", Date)
], CreateActivityEventsTaskDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间' }),
    (0, class_validator_1.IsNotEmpty)({ message: '结束时间不能为空' }),
    __metadata("design:type", Date)
], CreateActivityEventsTaskDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '作品ID必须是数字' }),
    __metadata("design:type", Number)
], CreateActivityEventsTaskDto.prototype, "workId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品文件路径', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '作品文件路径必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "workFile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品介绍', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '作品介绍必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "workDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '指导老师', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '指导老师姓名必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "instructorName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学校名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '学校名称必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "schoolName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '联系人', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '联系人姓名必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '联系电话', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '联系电话必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真实姓名', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '真实姓名必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "realName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '证件号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '证件号必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "idNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属学校', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '所属学校必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "affiliatedSchool", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '机构单位', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '机构单位必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '指导老师电话', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '指导老师电话必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "instructorPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '参赛组别', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '参赛组别必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "competitionGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '报名表文件URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '报名表文件URL必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "registrationFormFile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建者ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '创建者ID必须是数字' }),
    __metadata("design:type", Number)
], CreateActivityEventsTaskDto.prototype, "creatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注信息必须是字符串' }),
    __metadata("design:type", String)
], CreateActivityEventsTaskDto.prototype, "remark", void 0);
//# sourceMappingURL=create-activity-events-task.dto.js.map