import { AiProvidersConfigService } from '../config/ai_providers-config.service';
export type VoiceprintRecognitionCallback = (data: {
    success: boolean;
    result?: any;
    error?: Error;
}) => void;
export declare class XunfeiVoiceprintRecognitionService {
    private configService;
    private readonly logger;
    private appId;
    private apiKey;
    private apiSecret;
    private apiUrl;
    constructor(configService: AiProvidersConfigService);
    createGroup(groupId: string, groupName: string, groupInfo: string): Promise<any>;
    createFeature(groupId: string, featureId: string, featureInfo: string, audioBase64: string): Promise<any>;
    compareFeature(groupId: string, dstFeatureId: string, audioBase64: string): Promise<any>;
    searchFeature(groupId: string, audioBase64: string, topK?: number): Promise<any>;
    queryFeatureList(groupId: string): Promise<any>;
    updateFeature(groupId: string, featureId: string, featureInfo: string, audioBase64: string): Promise<any>;
    deleteFeature(groupId: string, featureId: string): Promise<any>;
    deleteGroup(groupId: string): Promise<any>;
    private doApiRequest;
    private buildRequestUrl;
    private parseApiResponse;
}
