# 队列配置
queue:
  # 图像处理相关队列
  image:
    enhance:
      name: image-enhance
      concurrency: 10
      qpsLimit: 10
      retryAttempts: 4
      retryDelay: 1000
      extensionId: image-processing

    segment:
      name: image-segment
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 3
      retryDelay: 1000
      extensionId: image-processing

    score:
      name: image-score
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 2
      retryDelay: 1000
      extensionId: image-processing

    generate:
      name: image-generate
      concurrency: 10
      rpmLimit: 10
      retryAttempts: 2
      retryDelay: 2000
      extensionId: image-generation

    object:
      name: image-object
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 2
      retryDelay: 2000
      extensionId: image-generation

  # 语音处理相关队列
  speech:
    synthesis:
      name: speech-synthesis
      concurrency: 20
      rpmLimit: 20
      retryAttempts: 2
      retryDelay: 1000
      extensionId: speech-processing

    recognition:
      name: speech-recognition
      concurrency: 50
      retryAttempts: 3
      retryDelay: 1000
      extensionId: speech-processing

  # 声纹识别相关队列
  voiceprint:
    xunfei:
      name: voiceprint-recognition
      concurrency: 20
      retryAttempts: 3
      retryDelay: 1000
      extensionId: voiceprint-recognition

  # 手势识别相关队列
  gesture:
    recognition:
      name: static-gesture-recognition
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 3
      retryDelay: 1000
      extensionId: gesture-recognition

  # 文本生成相关队列
  text:
    glm:
      name: text-glm
      concurrency: 200
      retryAttempts: 2
      retryDelay: 1000
      extensionId: text-generation

    qwen:
      name: text-qwen
      concurrency: 200
      retryAttempts: 2
      retryDelay: 1000
      extensionId: text-generation

    doubao:
      name: text-doubao
      concurrency: 20
      retryAttempts: 2
      retryDelay: 1000
      extensionId: text-generation

  # 视觉分析相关队列
  vision:
    zhipu:
      name: vision-zhipu
      concurrency: 10
      retryAttempts: 2
      retryDelay: 1000
      extensionId: vision-analysis

    qwen:
      name: vision-qwen
      concurrency: 20
      rpmLimit: 50
      retryAttempts: 2
      retryDelay: 1000
      extensionId: vision-analysis

    bytedance:
      name: vision-bytedance
      concurrency: 1
      retryAttempts: 2
      retryDelay: 1000
      extensionId: vision-analysis

  # 表情识别队列
  expression:
    recognition:
      name: expression-recognition
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 3
      retryDelay: 1000
      extensionId: face-analysis

  face:
    compare:
      name: face-compare
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 2
      retryDelay: 1000
      extensionId: face-analysis
    recognition:
      name: face-recognition
      concurrency: 5
      qpsLimit: 5
      retryAttempts: 3
      retryDelay: 1000
      extensionId: face-analysis

# 积分配置
points:
  # 图像处理相关队列
  image:
    generate:
      points: 15
      remark: AI图像生成
    style:
      points: 10
      remark: AI风格转换
    segment:
      points: 5
      remark: AI图像分割

    enhance:
      points: 10
      remark: AI图像增强

    score:
      points: 5
      remark: AI图像评分

    object:
      points: 5
      remark: AI物体检测

  # 语音处理相关队列
  speech:
    synthesis:
      points: 10
      remark: AI语音合成
    recognition:
      points: 5
      remark: AI语音识别

  # 声纹识别相关积分
  voiceprint:
    create:
      points: 20
      remark: 创建声纹特征库
    compare:
      points: 10
      remark: 声纹对比
    search:
      points: 10
      remark: 声纹检索

  # 手势识别相关积分
  gesture:
    recognition:
      points: 5
      remark: AI静态手势识别

  # 文本生成相关队列
  text:
    base:
      points: 1
      remark: AI对话基础功能
    context:
      points: 1
      remark: AI对话上下文功能
    websearch:
      points: 1
      remark: AI对话网络搜索功能
    deepthink:
      points: 10
      remark: AI对话深度思考功能

  # 视觉分析相关队列
  vision:
    qwen:
      points: 5
      remark: AI视觉识别

  # 表情识别队列
  expression:
    recognition:
      points: 5
      remark: AI表情识别

  face:
    recognition:
      points: 5
      remark: AI人脸识别
    compare:
      points: 5
      remark: AI人脸对比
