"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw";
exports.ids = ["vendor-chunks/@uiw"];
exports.modules = {

/***/ "(ssr)/./node_modules/@uiw/copy-to-clipboard/dist/copy-to-clipboard.esm.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/copy-to-clipboard/dist/copy-to-clipboard.esm.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ copyTextToClipboard)\n/* harmony export */ });\n/**! \n * @uiw/copy-to-clipboard v1.0.17 \n * Copy to clipboard. \n * \n * Copyright (c) 2024 Kenny Wang \n * https://github.com/uiwjs/copy-to-clipboard.git \n * \n * @website: https://uiwjs.github.io/copy-to-clipboard\n \n * Licensed under the MIT license \n */\n\n/**\n * *** This styling is an extra step which is likely not required. ***\n * https://github.com/w3c/clipboard-apis/blob/master/explainer.adoc#writing-to-the-clipboard\n * \n * Why is it here? To ensure:\n * \n * 1. the element is able to have focus and selection.\n * 2. if element was to flash render it has minimal visual impact.\n * 3. less flakyness with selection and copying which **might** occur if\n *     the textarea element is not visible.\n *\n *   The likelihood is the element won't even render, not even a flash,\n *   so some of these are just precautions. However in IE the element\n *   is visible whilst the popup box asking the user for permission for\n *   the web page to copy to the clipboard.\n *  \n *   Place in top-left corner of screen regardless of scroll position.\n *\n * @typedef CopyTextToClipboard\n * @property {(text: string, method?: (isCopy: boolean) => void) => void} void\n * @returns {void}\n * \n * @param {string} text \n * @param {CopyTextToClipboard} cb \n */\nfunction copyTextToClipboard(text, cb) {\n  if (typeof document === \"undefined\") return;\n  const el = document.createElement('textarea');\n  el.value = text;\n  el.setAttribute('readonly', '');\n  el.style = {\n    position: 'absolute',\n    left: '-9999px',\n  };\n  document.body.appendChild(el);\n  const selected = document.getSelection().rangeCount > 0 ? document.getSelection().getRangeAt(0) : false;\n  el.select();\n  let isCopy = false;\n  try {\n    const successful = document.execCommand('copy');\n    isCopy = !!successful;\n  } catch (err) {\n    isCopy = false;\n  }\n  document.body.removeChild(el);\n  if (selected && document.getSelection) {\n    document.getSelection().removeAllRanges();\n    document.getSelection().addRange(selected);\n  }\n  cb && cb(isCopy);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/copy-to-clipboard/dist/copy-to-clipboard.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/Props.js":
/*!***************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/Props.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);


/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rehype_prism_plus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rehype-prism-plus */ \"(ssr)/./node_modules/rehype-prism-plus/dist/index.es.js\");\n/* harmony import */ var rehype_rewrite__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rehype-rewrite */ \"(ssr)/./node_modules/rehype-rewrite/lib/index.js\");\n/* harmony import */ var rehype_attr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rehype-attr */ \"(ssr)/./node_modules/rehype-attr/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/./node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _preview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./preview */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/preview.js\");\n/* harmony import */ var _plugins_reservedMeta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./plugins/reservedMeta */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/reservedMeta.js\");\n/* harmony import */ var _plugins_retrieveMeta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./plugins/retrieveMeta */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/retrieveMeta.js\");\n/* harmony import */ var _rehypePlugins__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./rehypePlugins */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/rehypePlugins.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Props */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/Props.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef((props, ref) => {\n  var _props$disableCopy;\n  var rehypePlugins = [_plugins_reservedMeta__WEBPACK_IMPORTED_MODULE_4__.reservedMeta, rehype_raw__WEBPACK_IMPORTED_MODULE_9__[\"default\"], _plugins_retrieveMeta__WEBPACK_IMPORTED_MODULE_5__.retrieveMeta, ..._rehypePlugins__WEBPACK_IMPORTED_MODULE_6__.defaultRehypePlugins, [rehype_rewrite__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n    rewrite: (0,_rehypePlugins__WEBPACK_IMPORTED_MODULE_6__.rehypeRewriteHandle)((_props$disableCopy = props.disableCopy) != null ? _props$disableCopy : false, props.rehypeRewrite)\n  }], [rehype_attr__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n    properties: 'attr'\n  }], ...(props.rehypePlugins || []), [rehype_prism_plus__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    ignoreMissing: true\n  }]];\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_preview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, props, {\n    rehypePlugins: rehypePlugins,\n    ref: ref\n  }));\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/nodes/copy.js":
/*!********************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/nodes/copy.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyElement: () => (/* binding */ copyElement)\n/* harmony export */ });\nfunction copyElement(str) {\n  if (str === void 0) {\n    str = '';\n  }\n  return {\n    type: 'element',\n    tagName: 'div',\n    properties: {\n      class: 'copied',\n      'data-code': str\n    },\n    children: [{\n      type: 'element',\n      tagName: 'svg',\n      properties: {\n        className: 'octicon-copy',\n        ariaHidden: 'true',\n        viewBox: '0 0 16 16',\n        fill: 'currentColor',\n        height: 12,\n        width: 12\n      },\n      children: [{\n        type: 'element',\n        tagName: 'path',\n        properties: {\n          fillRule: 'evenodd',\n          d: 'M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 010 1.5h-1.5a.25.25 0 00-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 00.25-.25v-1.5a.75.75 0 011.5 0v1.5A1.75 1.75 0 019.25 16h-7.5A1.75 1.75 0 010 14.25v-7.5z'\n        },\n        children: []\n      }, {\n        type: 'element',\n        tagName: 'path',\n        properties: {\n          fillRule: 'evenodd',\n          d: 'M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0114.25 11h-7.5A1.75 1.75 0 015 9.25v-7.5zm1.75-.25a.25.25 0 00-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 00.25-.25v-7.5a.25.25 0 00-.25-.25h-7.5z'\n        },\n        children: []\n      }]\n    }, {\n      type: 'element',\n      tagName: 'svg',\n      properties: {\n        className: 'octicon-check',\n        ariaHidden: 'true',\n        viewBox: '0 0 16 16',\n        fill: 'currentColor',\n        height: 12,\n        width: 12\n      },\n      children: [{\n        type: 'element',\n        tagName: 'path',\n        properties: {\n          fillRule: 'evenodd',\n          d: 'M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z'\n        },\n        children: []\n      }]\n    }]\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/nodes/copy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/nodes/octiconLink.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/nodes/octiconLink.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   octiconLink: () => (/* binding */ octiconLink)\n/* harmony export */ });\nvar octiconLink = {\n  type: 'element',\n  tagName: 'svg',\n  properties: {\n    className: 'octicon octicon-link',\n    viewBox: '0 0 16 16',\n    version: '1.1',\n    width: '16',\n    height: '16',\n    ariaHidden: 'true'\n  },\n  children: [{\n    type: 'element',\n    tagName: 'path',\n    children: [],\n    properties: {\n      fillRule: 'evenodd',\n      d: 'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'\n    }\n  }]\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tYXJrZG93bi1wcmV2aWV3L2VzbS9ub2Rlcy9vY3RpY29uTGluay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1hcmtkb3duLXByZXZpZXcvZXNtL25vZGVzL29jdGljb25MaW5rLmpzPzNhNGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBvY3RpY29uTGluayA9IHtcbiAgdHlwZTogJ2VsZW1lbnQnLFxuICB0YWdOYW1lOiAnc3ZnJyxcbiAgcHJvcGVydGllczoge1xuICAgIGNsYXNzTmFtZTogJ29jdGljb24gb2N0aWNvbi1saW5rJyxcbiAgICB2aWV3Qm94OiAnMCAwIDE2IDE2JyxcbiAgICB2ZXJzaW9uOiAnMS4xJyxcbiAgICB3aWR0aDogJzE2JyxcbiAgICBoZWlnaHQ6ICcxNicsXG4gICAgYXJpYUhpZGRlbjogJ3RydWUnXG4gIH0sXG4gIGNoaWxkcmVuOiBbe1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAncGF0aCcsXG4gICAgY2hpbGRyZW46IFtdLFxuICAgIHByb3BlcnRpZXM6IHtcbiAgICAgIGZpbGxSdWxlOiAnZXZlbm9kZCcsXG4gICAgICBkOiAnTTcuNzc1IDMuMjc1YS43NS43NSAwIDAwMS4wNiAxLjA2bDEuMjUtMS4yNWEyIDIgMCAxMTIuODMgMi44M2wtMi41IDIuNWEyIDIgMCAwMS0yLjgzIDAgLjc1Ljc1IDAgMDAtMS4wNiAxLjA2IDMuNSAzLjUgMCAwMDQuOTUgMGwyLjUtMi41YTMuNSAzLjUgMCAwMC00Ljk1LTQuOTVsLTEuMjUgMS4yNXptLTQuNjkgOS42NGEyIDIgMCAwMTAtMi44M2wyLjUtMi41YTIgMiAwIDAxMi44MyAwIC43NS43NSAwIDAwMS4wNi0xLjA2IDMuNSAzLjUgMCAwMC00Ljk1IDBsLTIuNSAyLjVhMy41IDMuNSAwIDAwNC45NSA0Ljk1bDEuMjUtMS4yNWEuNzUuNzUgMCAwMC0xLjA2LTEuMDZsLTEuMjUgMS4yNWEyIDIgMCAwMS0yLjgzIDB6J1xuICAgIH1cbiAgfV1cbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/nodes/octiconLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/reservedMeta.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/plugins/reservedMeta.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reservedMeta: () => (/* binding */ reservedMeta)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n\n\nvar reservedMeta = function reservedMeta(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return tree => {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, node => {\n      if (node.type === 'element' && node.tagName === 'code' && node.data && node.data.meta) {\n        node.properties = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, node.properties, {\n          'data-meta': String(node.data.meta)\n        });\n      }\n    });\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tYXJrZG93bi1wcmV2aWV3L2VzbS9wbHVnaW5zL3Jlc2VydmVkTWV0YS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXNEO0FBQ2I7QUFDbEM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksdURBQUs7QUFDVDtBQUNBLDBCQUEwQixxRUFBUSxHQUFHO0FBQ3JDO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tYXJrZG93bi1wcmV2aWV3L2VzbS9wbHVnaW5zL3Jlc2VydmVkTWV0YS5qcz83NWM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9leHRlbmRzXCI7XG5pbXBvcnQgeyB2aXNpdCB9IGZyb20gJ3VuaXN0LXV0aWwtdmlzaXQnO1xuZXhwb3J0IHZhciByZXNlcnZlZE1ldGEgPSBmdW5jdGlvbiByZXNlcnZlZE1ldGEob3B0aW9ucykge1xuICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG4gIHJldHVybiB0cmVlID0+IHtcbiAgICB2aXNpdCh0cmVlLCBub2RlID0+IHtcbiAgICAgIGlmIChub2RlLnR5cGUgPT09ICdlbGVtZW50JyAmJiBub2RlLnRhZ05hbWUgPT09ICdjb2RlJyAmJiBub2RlLmRhdGEgJiYgbm9kZS5kYXRhLm1ldGEpIHtcbiAgICAgICAgbm9kZS5wcm9wZXJ0aWVzID0gX2V4dGVuZHMoe30sIG5vZGUucHJvcGVydGllcywge1xuICAgICAgICAgICdkYXRhLW1ldGEnOiBTdHJpbmcobm9kZS5kYXRhLm1ldGEpXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuICB9O1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/reservedMeta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/retrieveMeta.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/plugins/retrieveMeta.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   retrieveMeta: () => (/* binding */ retrieveMeta)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n\nvar retrieveMeta = function retrieveMeta(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return tree => {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, node => {\n      if (node.type === 'element' && node.tagName === 'code' && node.properties && node.properties['dataMeta']) {\n        if (!node.data) {\n          node.data = {};\n        }\n        var metaString = node.properties['dataMeta'];\n        if (typeof metaString === 'string') {\n          node.data.meta = metaString;\n        }\n        delete node.properties['dataMeta'];\n      }\n    });\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tYXJrZG93bi1wcmV2aWV3L2VzbS9wbHVnaW5zL3JldHJpZXZlTWV0YS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QztBQUNsQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSx1REFBSztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tYXJrZG93bi1wcmV2aWV3L2VzbS9wbHVnaW5zL3JldHJpZXZlTWV0YS5qcz8xZTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZpc2l0IH0gZnJvbSAndW5pc3QtdXRpbC12aXNpdCc7XG5leHBvcnQgdmFyIHJldHJpZXZlTWV0YSA9IGZ1bmN0aW9uIHJldHJpZXZlTWV0YShvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHRyZWUgPT4ge1xuICAgIHZpc2l0KHRyZWUsIG5vZGUgPT4ge1xuICAgICAgaWYgKG5vZGUudHlwZSA9PT0gJ2VsZW1lbnQnICYmIG5vZGUudGFnTmFtZSA9PT0gJ2NvZGUnICYmIG5vZGUucHJvcGVydGllcyAmJiBub2RlLnByb3BlcnRpZXNbJ2RhdGFNZXRhJ10pIHtcbiAgICAgICAgaWYgKCFub2RlLmRhdGEpIHtcbiAgICAgICAgICBub2RlLmRhdGEgPSB7fTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgbWV0YVN0cmluZyA9IG5vZGUucHJvcGVydGllc1snZGF0YU1ldGEnXTtcbiAgICAgICAgaWYgKHR5cGVvZiBtZXRhU3RyaW5nID09PSAnc3RyaW5nJykge1xuICAgICAgICAgIG5vZGUuZGF0YS5tZXRhID0gbWV0YVN0cmluZztcbiAgICAgICAgfVxuICAgICAgICBkZWxldGUgbm9kZS5wcm9wZXJ0aWVzWydkYXRhTWV0YSddO1xuICAgICAgfVxuICAgIH0pO1xuICB9O1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/retrieveMeta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/useCopied.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/plugins/useCopied.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCopied: () => (/* binding */ useCopied)\n/* harmony export */ });\n/* harmony import */ var _uiw_copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uiw/copy-to-clipboard */ \"(ssr)/./node_modules/@uiw/copy-to-clipboard/dist/copy-to-clipboard.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction getParentElement(target) {\n  if (!target) return null;\n  var dom = target;\n  if (dom.dataset.code && dom.classList.contains('copied')) {\n    return dom;\n  }\n  if (dom.parentElement) {\n    return getParentElement(dom.parentElement);\n  }\n  return null;\n}\nfunction useCopied(container) {\n  var handle = event => {\n    var target = getParentElement(event.target);\n    if (!target) return;\n    target.classList.add('active');\n    (0,_uiw_copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(target.dataset.code, function () {\n      setTimeout(() => {\n        target.classList.remove('active');\n      }, 2000);\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    var _container$current, _container$current2;\n    (_container$current = container.current) == null || _container$current.removeEventListener('click', handle, false);\n    (_container$current2 = container.current) == null || _container$current2.addEventListener('click', handle, false);\n    return () => {\n      var _container$current3;\n      (_container$current3 = container.current) == null || _container$current3.removeEventListener('click', handle, false);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [container]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/useCopied.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/preview.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/preview.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/./node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var remark_github_blockquote_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-github-blockquote-alert */ \"(ssr)/./node_modules/remark-github-blockquote-alert/lib/index.js\");\n/* harmony import */ var _plugins_useCopied__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plugins/useCopied */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/plugins/useCopied.js\");\n/* harmony import */ var _styles_markdown_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./styles/markdown.css */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/styles/markdown.css\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\n\n\nvar _excluded = [\"prefixCls\", \"className\", \"source\", \"style\", \"disableCopy\", \"skipHtml\", \"onScroll\", \"onMouseOver\", \"pluginsFilter\", \"rehypeRewrite\", \"wrapperElement\", \"warpperElement\", \"urlTransform\"];\n\n\n\n\n\n\n\n\n/**\n * https://github.com/uiwjs/react-md-editor/issues/607\n */\n\nvar defaultUrlTransform = url => url;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef((props, ref) => {\n  var {\n      prefixCls = 'wmde-markdown wmde-markdown-color',\n      className,\n      source,\n      style,\n      disableCopy = false,\n      skipHtml = true,\n      onScroll,\n      onMouseOver,\n      pluginsFilter,\n      wrapperElement = {},\n      warpperElement = {},\n      urlTransform\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(props, _excluded);\n  var mdp = react__WEBPACK_IMPORTED_MODULE_2___default().useRef(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, props, {\n    mdp\n  }), [mdp, props]);\n  var cls = (prefixCls || '') + \" \" + (className || '');\n  (0,_plugins_useCopied__WEBPACK_IMPORTED_MODULE_3__.useCopied)(mdp);\n  var rehypePlugins = [...(other.rehypePlugins || [])];\n  var customProps = {\n    allowElement: (element, index, parent) => {\n      if (other.allowElement) {\n        return other.allowElement(element, index, parent);\n      }\n      return /^[A-Za-z0-9]+$/.test(element.tagName);\n    }\n  };\n  if (skipHtml) {\n    rehypePlugins.push(rehype_raw__WEBPACK_IMPORTED_MODULE_6__[\"default\"]);\n  }\n  var remarkPlugins = [remark_github_blockquote_alert__WEBPACK_IMPORTED_MODULE_7__.remarkAlert, ...(other.remarkPlugins || []), remark_gfm__WEBPACK_IMPORTED_MODULE_8__[\"default\"]];\n  var wrapperProps = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, warpperElement, wrapperElement);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n    ref: mdp,\n    onScroll: onScroll,\n    onMouseOver: onMouseOver\n  }, wrapperProps, {\n    className: cls,\n    style: style,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, customProps, other, {\n      skipHtml: skipHtml,\n      urlTransform: urlTransform || defaultUrlTransform,\n      rehypePlugins: pluginsFilter ? pluginsFilter('rehype', rehypePlugins) : rehypePlugins,\n      remarkPlugins: pluginsFilter ? pluginsFilter('remark', remarkPlugins) : remarkPlugins,\n      children: source || ''\n    }))\n  }));\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/preview.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/rehypePlugins.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/rehypePlugins.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultRehypePlugins: () => (/* binding */ defaultRehypePlugins),\n/* harmony export */   rehypeRewriteHandle: () => (/* binding */ rehypeRewriteHandle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var rehype_slug__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-slug */ \"(ssr)/./node_modules/rehype-slug/lib/index.js\");\n/* harmony import */ var rehype_autolink_headings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rehype-autolink-headings */ \"(ssr)/./node_modules/rehype-autolink-headings/lib/index.js\");\n/* harmony import */ var rehype_ignore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rehype-ignore */ \"(ssr)/./node_modules/rehype-ignore/lib/index.js\");\n/* harmony import */ var rehype_rewrite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rehype-rewrite */ \"(ssr)/./node_modules/rehype-rewrite/lib/index.js\");\n/* harmony import */ var _nodes_octiconLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nodes/octiconLink */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/nodes/octiconLink.js\");\n/* harmony import */ var _nodes_copy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./nodes/copy */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/nodes/copy.js\");\n\n\n\n\n\n\n\nvar rehypeRewriteHandle = (disableCopy, rewrite) => (node, index, parent) => {\n  if (node.type === 'element' && parent && parent.type === 'root' && /h(1|2|3|4|5|6)/.test(node.tagName)) {\n    var child = node.children && node.children[0];\n    if (child && child.properties && child.properties.ariaHidden === 'true') {\n      child.properties = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n        class: 'anchor'\n      }, child.properties);\n      child.children = [_nodes_octiconLink__WEBPACK_IMPORTED_MODULE_1__.octiconLink];\n    }\n  }\n  if (node.type === 'element' && node.tagName === 'pre' && !disableCopy) {\n    var code = (0,rehype_rewrite__WEBPACK_IMPORTED_MODULE_3__.getCodeString)(node.children);\n    node.children.push((0,_nodes_copy__WEBPACK_IMPORTED_MODULE_2__.copyElement)(code));\n  }\n  rewrite && rewrite(node, index === null ? undefined : index, parent === null ? undefined : parent);\n};\nvar defaultRehypePlugins = [rehype_slug__WEBPACK_IMPORTED_MODULE_4__[\"default\"], rehype_autolink_headings__WEBPACK_IMPORTED_MODULE_5__[\"default\"], rehype_ignore__WEBPACK_IMPORTED_MODULE_6__[\"default\"]];//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/rehypePlugins.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js":
/*!**********************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/Context.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorContext: () => (/* binding */ EditorContext),\n/* harmony export */   reducer: () => (/* binding */ reducer)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction reducer(state, action) {\n  return _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, state, action);\n}\nvar EditorContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createContext({\n  markdown: ''\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL0NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNEO0FBQzVCO0FBQ25CO0FBQ1AsU0FBUyxxRUFBUSxHQUFHO0FBQ3BCO0FBQ08saUNBQWlDLDBEQUFtQjtBQUMzRDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL0NvbnRleHQuanM/NDdiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXh0ZW5kc1wiO1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBmdW5jdGlvbiByZWR1Y2VyKHN0YXRlLCBhY3Rpb24pIHtcbiAgcmV0dXJuIF9leHRlbmRzKHt9LCBzdGF0ZSwgYWN0aW9uKTtcbn1cbmV4cG9ydCB2YXIgRWRpdG9yQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KHtcbiAgbWFya2Rvd246ICcnXG59KTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/Editor.js":
/*!*********************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/Editor.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @uiw/react-markdown-preview */ \"(ssr)/./node_modules/@uiw/react-markdown-preview/esm/index.js\");\n/* harmony import */ var _components_TextArea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/TextArea */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.js\");\n/* harmony import */ var _components_Toolbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Toolbar */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.js\");\n/* harmony import */ var _components_DragBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/DragBar */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./commands */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js\");\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__);\n\n\nvar _excluded = [\"prefixCls\", \"className\", \"value\", \"commands\", \"commandsFilter\", \"direction\", \"extraCommands\", \"height\", \"enableScroll\", \"visibleDragbar\", \"highlightEnable\", \"preview\", \"fullscreen\", \"overflow\", \"previewOptions\", \"textareaProps\", \"maxHeight\", \"minHeight\", \"autoFocus\", \"tabSize\", \"defaultTabEnable\", \"onChange\", \"onStatistics\", \"onHeightChange\", \"hideToolbar\", \"toolbarBottom\", \"components\", \"renderTextarea\"];\n\n\n\n\n\n\n\n\nfunction setGroupPopFalse(data) {\n  if (data === void 0) {\n    data = {};\n  }\n  Object.keys(data).forEach(keyname => {\n    data[keyname] = false;\n  });\n  return data;\n}\nvar InternalMDEditor = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef((props, ref) => {\n  var _ref = props || {},\n    {\n      prefixCls = 'w-md-editor',\n      className,\n      value: propsValue,\n      commands = (0,_commands__WEBPACK_IMPORTED_MODULE_7__.getCommands)(),\n      commandsFilter,\n      direction,\n      extraCommands = (0,_commands__WEBPACK_IMPORTED_MODULE_7__.getExtraCommands)(),\n      height = 200,\n      enableScroll = true,\n      visibleDragbar = typeof props.visiableDragbar === 'boolean' ? props.visiableDragbar : true,\n      highlightEnable = true,\n      preview: previewType = 'live',\n      fullscreen = false,\n      overflow = true,\n      previewOptions = {},\n      textareaProps,\n      maxHeight = 1200,\n      minHeight = 100,\n      autoFocus,\n      tabSize = 2,\n      defaultTabEnable = false,\n      onChange,\n      onStatistics,\n      onHeightChange,\n      hideToolbar,\n      toolbarBottom = false,\n      components,\n      renderTextarea\n    } = _ref,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(_ref, _excluded);\n  var cmds = commands.map(item => commandsFilter ? commandsFilter(item, false) : item).filter(Boolean);\n  var extraCmds = extraCommands.map(item => commandsFilter ? commandsFilter(item, true) : item).filter(Boolean);\n  var [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useReducer)(_Context__WEBPACK_IMPORTED_MODULE_8__.reducer, {\n    markdown: propsValue,\n    preview: previewType,\n    components,\n    height,\n    minHeight,\n    highlightEnable,\n    tabSize,\n    defaultTabEnable,\n    scrollTop: 0,\n    scrollTopPreview: 0,\n    commands: cmds,\n    extraCommands: extraCmds,\n    fullscreen,\n    barPopup: {}\n  });\n  var container = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  var previewRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  var enableScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(enableScroll);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, state, {\n    container: container.current,\n    dispatch\n  }));\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => enableScrollRef.current = enableScroll, [enableScroll]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    var stateInit = {};\n    if (container.current) {\n      stateInit.container = container.current || undefined;\n    }\n    stateInit.markdown = propsValue || '';\n    stateInit.barPopup = {};\n    if (dispatch) {\n      dispatch(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, state, stateInit));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var cls = [className, 'wmde-markdown-var', direction ? prefixCls + \"-\" + direction : null, prefixCls, state.preview ? prefixCls + \"-show-\" + state.preview : null, state.fullscreen ? prefixCls + \"-fullscreen\" : null].filter(Boolean).join(' ').trim();\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => propsValue !== state.markdown && dispatch({\n    markdown: propsValue || ''\n  }), [propsValue, state.markdown]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => previewType !== state.preview && dispatch({\n    preview: previewType\n  }), [previewType]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => tabSize !== state.tabSize && dispatch({\n    tabSize\n  }), [tabSize]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => highlightEnable !== state.highlightEnable && dispatch({\n    highlightEnable\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [highlightEnable]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => autoFocus !== state.autoFocus && dispatch({\n    autoFocus: autoFocus\n  }), [autoFocus]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => fullscreen !== state.fullscreen && dispatch({\n    fullscreen: fullscreen\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [fullscreen]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => height !== state.height && dispatch({\n    height: height\n  }), [height]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => height !== state.height && onHeightChange && onHeightChange(state.height, height, state), [height, onHeightChange, state]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => commands !== state.commands && dispatch({\n    commands: cmds\n  }), [props.commands]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => extraCommands !== state.extraCommands && dispatch({\n    extraCommands: extraCmds\n  }), [props.extraCommands]);\n  var textareaDomRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  var active = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('preview');\n  var initScroll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => {\n    textareaDomRef.current = state.textareaWarp;\n    if (state.textareaWarp) {\n      state.textareaWarp.addEventListener('mouseover', () => {\n        active.current = 'text';\n      });\n      state.textareaWarp.addEventListener('mouseleave', () => {\n        active.current = 'preview';\n      });\n    }\n  }, [state.textareaWarp]);\n  var handleScroll = (e, type) => {\n    if (!enableScrollRef.current) return;\n    var textareaDom = textareaDomRef.current;\n    var previewDom = previewRef.current ? previewRef.current : undefined;\n    if (!initScroll.current) {\n      active.current = type;\n      initScroll.current = true;\n    }\n    if (textareaDom && previewDom) {\n      var scale = (textareaDom.scrollHeight - textareaDom.offsetHeight) / (previewDom.scrollHeight - previewDom.offsetHeight);\n      if (e.target === textareaDom && active.current === 'text') {\n        previewDom.scrollTop = textareaDom.scrollTop / scale;\n      }\n      if (e.target === previewDom && active.current === 'preview') {\n        textareaDom.scrollTop = previewDom.scrollTop * scale;\n      }\n      var scrollTop = 0;\n      if (active.current === 'text') {\n        scrollTop = textareaDom.scrollTop || 0;\n      } else if (active.current === 'preview') {\n        scrollTop = previewDom.scrollTop || 0;\n      }\n      dispatch({\n        scrollTop\n      });\n    }\n  };\n  var previewClassName = prefixCls + \"-preview \" + (previewOptions.className || '');\n  var handlePreviewScroll = e => handleScroll(e, 'preview');\n  var mdPreview = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"div\", {\n    ref: previewRef,\n    className: previewClassName,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, previewOptions, {\n      onScroll: handlePreviewScroll,\n      source: state.markdown || ''\n    }))\n  }), [previewClassName, previewOptions, state.markdown]);\n  var preview = (components == null ? void 0 : components.preview) && (components == null ? void 0 : components.preview(state.markdown || '', state, dispatch));\n  if (preview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().isValidElement(preview)) {\n    mdPreview = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"div\", {\n      className: previewClassName,\n      ref: previewRef,\n      onScroll: handlePreviewScroll,\n      children: preview\n    });\n  }\n  var containerStyle = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, other.style, {\n    height: state.height || '100%'\n  });\n  var containerClick = () => dispatch({\n    barPopup: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, setGroupPopFalse(state.barPopup))\n  });\n  var dragBarChange = newHeight => dispatch({\n    height: newHeight\n  });\n  var changeHandle = evn => {\n    onChange && onChange(evn.target.value, evn, state);\n    if (textareaProps && textareaProps.onChange) {\n      textareaProps.onChange(evn);\n    }\n    if (state.textarea && state.textarea instanceof HTMLTextAreaElement && onStatistics) {\n      var obj = new _commands__WEBPACK_IMPORTED_MODULE_7__.TextAreaCommandOrchestrator(state.textarea);\n      var objState = obj.getState() || {};\n      onStatistics(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, objState, {\n        lineCount: evn.target.value.split('\\n').length,\n        length: evn.target.value.length\n      }));\n    }\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_Context__WEBPACK_IMPORTED_MODULE_8__.EditorContext.Provider, {\n    value: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, state, {\n      dispatch\n    }),\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      ref: container,\n      className: cls\n    }, other, {\n      onClick: containerClick,\n      style: containerStyle,\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_Toolbar__WEBPACK_IMPORTED_MODULE_5__.ToolbarVisibility, {\n        hideToolbar: hideToolbar,\n        toolbarBottom: toolbarBottom,\n        prefixCls: prefixCls,\n        overflow: overflow,\n        placement: \"top\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(\"div\", {\n        className: prefixCls + \"-content\",\n        children: [/(edit|live)/.test(state.preview || '') && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_TextArea__WEBPACK_IMPORTED_MODULE_4__[\"default\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n          className: prefixCls + \"-input\",\n          prefixCls: prefixCls,\n          autoFocus: autoFocus\n        }, textareaProps, {\n          onChange: changeHandle,\n          renderTextarea: (components == null ? void 0 : components.textarea) || renderTextarea,\n          onScroll: e => handleScroll(e, 'text')\n        })), /(live|preview)/.test(state.preview || '') && mdPreview]\n      }), visibleDragbar && !state.fullscreen && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_DragBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        prefixCls: prefixCls,\n        height: state.height,\n        maxHeight: maxHeight,\n        minHeight: minHeight,\n        onChange: dragBarChange\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_Toolbar__WEBPACK_IMPORTED_MODULE_5__.ToolbarVisibility, {\n        hideToolbar: hideToolbar,\n        toolbarBottom: toolbarBottom,\n        prefixCls: prefixCls,\n        overflow: overflow,\n        placement: \"bottom\"\n      })]\n    }))\n  });\n});\nvar Editor = InternalMDEditor;\nEditor.Markdown = _uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Editor);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/Editor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/Types.js":
/*!********************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/Types.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);


/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/bold.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/bold.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bold: () => (/* binding */ bold)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar bold = {\n  name: 'bold',\n  keyCommand: 'bold',\n  shortcuts: 'ctrlcmd+b',\n  prefix: '**',\n  buttonProps: {\n    'aria-label': 'Add bold text (ctrl + b)',\n    title: 'Add bold text (ctrl + b)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    role: \"img\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 384 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M304.793 243.891c33.639-18.537 53.657-54.16 53.657-95.693 0-48.236-26.25-87.626-68.626-104.179C265.138 34.01 240.849 32 209.661 32H24c-8.837 0-16 7.163-16 16v33.049c0 8.837 7.163 16 16 16h33.113v318.53H24c-8.837 0-16 7.163-16 16V464c0 8.837 7.163 16 16 16h195.69c24.203 0 44.834-1.289 66.866-7.584C337.52 457.193 376 410.647 376 350.014c0-52.168-26.573-91.684-71.207-106.123zM142.217 100.809h67.444c16.294 0 27.536 2.019 37.525 6.717 15.828 8.479 24.906 26.502 24.906 49.446 0 35.029-20.32 56.79-53.029 56.79h-76.846V100.809zm112.642 305.475c-10.14 4.056-22.677 4.907-31.409 4.907h-81.233V281.943h84.367c39.645 0 63.057 25.38 63.057 63.057.001 28.425-13.66 52.483-34.782 61.284z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/bold.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code),\n/* harmony export */   codeBlock: () => (/* binding */ codeBlock)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar codeBlock = {\n  name: 'codeBlock',\n  keyCommand: 'codeBlock',\n  shortcuts: 'ctrlcmd+shift+j',\n  prefix: '```',\n  buttonProps: {\n    'aria-label': 'Insert Code Block (ctrl + shift + j)',\n    title: 'Insert Code Block (ctrl + shift +j)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    width: \"13\",\n    height: \"13\",\n    role: \"img\",\n    viewBox: \"0 0 156 156\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M110.85 120.575 43.7 120.483333 43.7083334 110.091667 110.85 110.191667 110.841667 120.583333 110.85 120.575ZM85.1333334 87.1916666 43.625 86.7083332 43.7083334 76.3166666 85.2083334 76.7916666 85.1333334 87.1916666 85.1333334 87.1916666ZM110.841667 53.4166666 43.7 53.3166666 43.7083334 42.925 110.85 43.025 110.841667 53.4166666ZM36 138C27.2916666 138 20.75 136.216667 16.4 132.666667 12.1333334 129.2 10 124.308333 10 118L10 95.3333332C10 91.0666666 9.25 88.1333332 7.7333334 86.5333332 6.3166668 84.8416666 3.7333334 84 0 84L0 72C3.7333334 72 6.3083334 71.2 7.7333334 69.6 9.2416668 67.9083334 10 64.9333334 10 60.6666666L10 38C10 31.775 12.1333334 26.8833334 16.4 23.3333332 20.7583334 19.7749998 27.2916666 18 36 18L40.6666668 18 40.6666668 30 36 30C34.0212222 29.9719277 32.1263151 30.7979128 30.8 32.2666666 29.3605875 33.8216362 28.5938182 35.8823287 28.6666668 38L28.6666668 60.6666666C28.6666668 67.5083332 26.6666668 72.4 22.6666668 75.3333332 20.9317416 76.7274684 18.8640675 77.6464347 16.6666668 78 18.8916668 78.35 20.8916668 79.2416666 22.6666668 80.6666666 26.6666668 83.95 28.6666668 88.8416666 28.6666668 95.3333332L28.6666668 118C28.6666668 120.308333 29.3750002 122.216667 30.8 123.733333 32.2166666 125.241667 33.9583334 126 36 126L40.6666668 126 40.6666668 138 36 138 36 138ZM114.116667 126 118.783333 126C120.833333 126 122.566667 125.241667 123.983333 123.733333 125.422746 122.178364 126.189515 120.117671 126.116667 118L126.116667 95.3333332C126.116667 88.8333332 128.116667 83.9499998 132.116667 80.6666666 133.9 79.2416666 135.9 78.35 138.116667 78 135.919156 77.6468047 133.851391 76.7277979 132.116667 75.3333332 128.116667 72.3999998 126.116667 67.5 126.116667 60.6666666L126.116667 38C126.189515 35.8823287 125.422746 33.8216361 123.983333 32.2666666 122.657018 30.7979128 120.762111 29.9719277 118.783333 30L114.116667 30 114.116667 18 118.783333 18C127.5 18 133.983333 19.775 138.25 23.3333332 142.608333 26.8833332 144.783333 31.7749998 144.783333 38L144.783333 60.6666666C144.783333 64.9333332 145.5 67.9083332 146.916667 69.6 148.433333 71.2 151.05 72 154.783333 72L154.783333 84C151.05 84 148.433333 84.8333334 146.916667 86.5333332 145.5 88.1333332 144.783333 91.0666666 144.783333 95.3333332L144.783333 118C144.783333 124.308333 142.616667 129.2 138.25 132.666667 133.983333 136.216667 127.5 138 118.783333 138L114.116667 138 114.116667 126 114.116667 126Z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: '```\\n',\n      suffix: '\\n```'\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n\n    // Based on context determine if new line is needed or not\n    var prefix = '\\n```\\n';\n    var suffix = '\\n```\\n';\n    if (state1.selectedText.length >= prefix.length + suffix.length - 2 && state1.selectedText.startsWith(prefix) && state1.selectedText.endsWith(suffix)) {\n      // Remove code block\n      prefix = '```\\n';\n      suffix = '\\n```';\n    } else {\n      // Add code block\n      if (state1.selection.start >= 1 && state.text.slice(state1.selection.start - 1, state1.selection.start) === '\\n' || state1.selection.start === 0) {\n        prefix = '```\\n';\n      }\n      if (state1.selection.end <= state.text.length - 1 && state.text.slice(state1.selection.end, state1.selection.end + 1) === '\\n' || state1.selection.end === state.text.length) {\n        suffix = '\\n```';\n      }\n    }\n    var newSelectionRange2 = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix,\n      suffix\n    });\n    var state2 = api.setSelectionRange(newSelectionRange2);\n    (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n      api,\n      selectedText: state2.selectedText,\n      selection: state.selection,\n      prefix,\n      suffix\n    });\n  }\n};\nvar code = {\n  name: 'code',\n  keyCommand: 'code',\n  shortcuts: 'ctrlcmd+j',\n  prefix: '`',\n  buttonProps: {\n    'aria-label': 'Insert code (ctrl + j)',\n    title: 'Insert code (ctrl + j)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    width: \"14\",\n    height: \"14\",\n    role: \"img\",\n    viewBox: \"0 0 640 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M278.9 511.5l-61-17.7c-6.4-1.8-10-8.5-8.2-14.9L346.2 8.7c1.8-6.4 8.5-10 14.9-8.2l61 17.7c6.4 1.8 10 8.5 8.2 14.9L293.8 503.3c-1.9 6.4-8.5 10.1-14.9 8.2zm-114-112.2l43.5-46.4c4.6-4.9 4.3-12.7-.8-17.2L117 256l90.6-79.7c5.1-4.5 5.5-12.3.8-17.2l-43.5-46.4c-4.5-4.8-12.1-5.1-17-.5L3.8 247.2c-5.1 4.7-5.1 12.8 0 17.5l144.1 135.1c4.9 4.6 12.5 4.4 17-.5zm327.2.6l144.1-135.1c5.1-4.7 5.1-12.8 0-17.5L492.1 112.1c-4.8-4.5-12.4-4.3-17 .5L431.6 159c-4.6 4.9-4.3 12.7.8 17.2L523 256l-90.6 79.7c-5.1 4.5-5.5 12.3-.8 17.2l43.5 46.4c4.5 4.9 12.1 5.1 17 .6z\"\n    })\n  }),\n  execute: (state, api) => {\n    if (state.selectedText.indexOf('\\n') === -1) {\n      var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n        text: state.text,\n        selection: state.selection,\n        prefix: state.command.prefix\n      });\n      var state1 = api.setSelectionRange(newSelectionRange);\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix\n      });\n    } else {\n      codeBlock.execute(state, api);\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/comment.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/comment.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar comment = {\n  name: 'comment',\n  keyCommand: 'comment',\n  shortcuts: 'ctrlcmd+/',\n  prefix: '<!-- ',\n  suffix: ' -->',\n  buttonProps: {\n    'aria-label': 'Insert comment (ctrl + /)',\n    title: 'Insert comment (ctrl + /)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    height: \"1em\",\n    width: \"1em\",\n    viewBox: \"0 0 25 25\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(\"g\", {\n      fill: \"none\",\n      fillRule: \"evenodd\",\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"polygon\", {\n        points: \".769 .727 24.981 .727 24.981 24.727 .769 24.727\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n        stroke: \"currentColor\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"3\",\n        d: \"M12.625,23.8787879 L8.125,19.6969697 L5.125,19.6969697 C2.63971863,19.6969697 0.625,17.8247059 0.625,15.5151515 L0.625,7.15151515 C0.625,4.84196074 2.63971863,2.96969697 5.125,2.96969697 L20.125,2.96969697 C22.6102814,2.96969697 24.625,4.84196074 24.625,7.15151515 L24.625,15.5151515 C24.625,17.8247059 22.6102814,19.6969697 20.125,19.6969697 L17.125,19.6969697 L12.625,23.8787879\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n        stroke: \"currentColor\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"3\",\n        d: \"M10.625,8.54545455 L7.25,11.3333333 L10.625,14.1212121 M15.6875,8.54545455 L19.0625,11.3333333 L15.6875,14.1212121\"\n      })]\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/comment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/divider.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/divider.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   divider: () => (/* binding */ divider)\n/* harmony export */ });\nvar divider = {\n  keyCommand: 'divider'\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL2RpdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvZGl2aWRlci5qcz85MDVlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgZGl2aWRlciA9IHtcbiAga2V5Q29tbWFuZDogJ2RpdmlkZXInXG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/divider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/fullscreen.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/fullscreen.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fullscreen: () => (/* binding */ fullscreen)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar fullscreen = {\n  name: 'fullscreen',\n  keyCommand: 'fullscreen',\n  shortcuts: 'ctrlcmd+0',\n  value: 'fullscreen',\n  buttonProps: {\n    'aria-label': 'Toggle fullscreen (ctrl + 0)',\n    title: 'Toggle fullscreen (ctrl+ 0)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M118 171.133334L118 342.200271C118 353.766938 126.675 365.333605 141.133333 365.333605L382.634614 365.333605C394.201281 365.333605 405.767948 356.658605 405.767948 342.200271L405.767948 171.133334C405.767948 159.566667 397.092948 148 382.634614 148L141.133333 148C126.674999 148 117.999999 156.675 118 171.133334zM465.353591 413.444444L370 413.444444 370 471.222222 474.0221 471.222222C500.027624 471.222222 520.254143 451 520.254143 425L520.254143 321 462.464089 321 462.464089 413.444444 465.353591 413.444444zM471.0221 43L367 43 367 100.777778 462.353591 100.777778 462.353591 196.111111 520.143647 196.111111 520.143647 89.2222219C517.254144 63.2222219 497.027624 43 471.0221 43zM57.7900547 100.777778L153.143646 100.777778 153.143646 43 46.2320439 43C20.2265191 43 0 63.2222219 0 89.2222219L0 193.222222 57.7900547 193.222222 57.7900547 100.777778zM57.7900547 321L0 321 0 425C0 451 20.2265191 471.222222 46.2320439 471.222223L150.254143 471.222223 150.254143 413.444445 57.7900547 413.444445 57.7900547 321z\"\n    })\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        fullscreen: !executeCommandState.fullscreen\n      });\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/fullscreen.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/group.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/group.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   group: () => (/* binding */ group)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/objectDestructuringEmpty */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectDestructuringEmpty.js\");\n/* harmony import */ var _babel_runtime_helpers_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar group = (arr, options) => {\n  var data = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1___default()({\n    children: arr,\n    icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"svg\", {\n      width: \"12\",\n      height: \"12\",\n      viewBox: \"0 0 520 520\",\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"path\", {\n        fill: \"currentColor\",\n        d: \"M15.7083333,468 C7.03242448,468 0,462.030833 0,454.666667 L0,421.333333 C0,413.969167 7.03242448,408 15.7083333,408 L361.291667,408 C369.967576,408 377,413.969167 377,421.333333 L377,454.666667 C377,462.030833 369.967576,468 361.291667,468 L15.7083333,468 Z M21.6666667,366 C9.69989583,366 0,359.831861 0,352.222222 L0,317.777778 C0,310.168139 9.69989583,304 21.6666667,304 L498.333333,304 C510.300104,304 520,310.168139 520,317.777778 L520,352.222222 C520,359.831861 510.300104,366 498.333333,366 L21.6666667,366 Z M136.835938,64 L136.835937,126 L107.25,126 L107.25,251 L40.75,251 L40.75,126 L-5.68434189e-14,126 L-5.68434189e-14,64 L136.835938,64 Z M212,64 L212,251 L161.648438,251 L161.648438,64 L212,64 Z M378,64 L378,126 L343.25,126 L343.25,251 L281.75,251 L281.75,126 L238,126 L238,64 L378,64 Z M449.047619,189.550781 L520,189.550781 L520,251 L405,251 L405,64 L449.047619,64 L449.047619,189.550781 Z\"\n      })\n    }),\n    execute: () => {}\n  }, options, {\n    keyCommand: 'group'\n  });\n  if (Array.isArray(data.children)) {\n    data.children = data.children.map(_ref => {\n      var item = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1___default()({}, (_babel_runtime_helpers_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_0___default()(_ref), _ref));\n      item.parent = data;\n      return _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1___default()({}, item);\n    });\n  }\n  return data;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/group.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/help.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/help.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   help: () => (/* binding */ help)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar help = {\n  name: 'help',\n  keyCommand: 'help',\n  buttonProps: {\n    'aria-label': 'Open help',\n    title: 'Open help'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"svg\", {\n    viewBox: \"0 0 16 16\",\n    width: \"12px\",\n    height: \"12px\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"path\", {\n      d: \"M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8Zm.9 13H7v-1.8h1.9V13Zm-.1-3.6v.5H7.1v-.6c.2-2.1 2-1.9 1.9-3.2.1-.7-.3-1.1-1-1.1-.8 0-1.2.7-1.2 1.6H5c0-1.7 1.2-3 2.9-3 2.3 0 3 1.4 3 2.3.1 2.3-1.9 2-2.1 3.5Z\",\n      fill: \"currentColor\"\n    })\n  }),\n  execute: () => {\n    window.open('https://www.markdownguide.org/basic-syntax/', '_blank', 'noreferrer');\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL2hlbHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEI7QUFDc0I7QUFDekM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHFCQUFxQixzREFBSTtBQUN6QjtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsc0RBQUk7QUFDL0I7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL2hlbHAuanM/NDhmNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmV4cG9ydCB2YXIgaGVscCA9IHtcbiAgbmFtZTogJ2hlbHAnLFxuICBrZXlDb21tYW5kOiAnaGVscCcsXG4gIGJ1dHRvblByb3BzOiB7XG4gICAgJ2FyaWEtbGFiZWwnOiAnT3BlbiBoZWxwJyxcbiAgICB0aXRsZTogJ09wZW4gaGVscCdcbiAgfSxcbiAgaWNvbjogLyojX19QVVJFX18qL19qc3goXCJzdmdcIiwge1xuICAgIHZpZXdCb3g6IFwiMCAwIDE2IDE2XCIsXG4gICAgd2lkdGg6IFwiMTJweFwiLFxuICAgIGhlaWdodDogXCIxMnB4XCIsXG4gICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi9fanN4KFwicGF0aFwiLCB7XG4gICAgICBkOiBcIk04IDBDMy42IDAgMCAzLjYgMCA4czMuNiA4IDggOCA4LTMuNiA4LTgtMy42LTgtOC04Wm0uOSAxM0g3di0xLjhoMS45VjEzWm0tLjEtMy42di41SDcuMXYtLjZjLjItMi4xIDItMS45IDEuOS0zLjIuMS0uNy0uMy0xLjEtMS0xLjEtLjggMC0xLjIuNy0xLjIgMS42SDVjMC0xLjcgMS4yLTMgMi45LTMgMi4zIDAgMyAxLjQgMyAyLjMuMSAyLjMtMS45IDItMi4xIDMuNVpcIixcbiAgICAgIGZpbGw6IFwiY3VycmVudENvbG9yXCJcbiAgICB9KVxuICB9KSxcbiAgZXhlY3V0ZTogKCkgPT4ge1xuICAgIHdpbmRvdy5vcGVuKCdodHRwczovL3d3dy5tYXJrZG93bmd1aWRlLm9yZy9iYXNpYy1zeW50YXgvJywgJ19ibGFuaycsICdub3JlZmVycmVyJyk7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/help.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/hr.js":
/*!**************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/hr.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hr: () => (/* binding */ hr)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar hr = {\n  name: 'hr',\n  keyCommand: 'hr',\n  shortcuts: 'ctrlcmd+h',\n  prefix: '\\n\\n---\\n',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert HR (ctrl + h)',\n    title: 'Insert HR (ctrl + h)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 175 175\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M0,129 L175,129 L175,154 L0,154 L0,129 Z M3,9 L28.2158203,9 L28.2158203,47.9824219 L55.7695313,47.9824219 L55.7695313,9 L81.0966797,9 L81.0966797,107.185547 L55.7695313,107.185547 L55.7695313,68.0214844 L28.2158203,68.0214844 L28.2158203,107.185547 L3,107.185547 L3,9 Z M93.1855469,100.603516 L93.1855469,19 L135.211914,19 C143.004922,19 148.960917,19.6679621 153.080078,21.0039063 C157.199239,22.3398504 160.520495,24.8168764 163.043945,28.4350586 C165.567395,32.0532407 166.829102,36.459935 166.829102,41.6552734 C166.829102,46.1826398 165.864267,50.0883625 163.93457,53.3725586 C162.004873,56.6567547 159.351579,59.3193257 155.974609,61.3603516 C153.822255,62.6591862 150.872089,63.7353473 147.124023,64.5888672 C150.129898,65.5908253 152.319329,66.5927684 153.692383,67.5947266 C154.620122,68.2626987 155.965323,69.6913953 157.728027,71.8808594 C159.490731,74.0703234 160.668942,75.7587831 161.262695,76.9462891 L173,100.603516 L144.953125,100.603516 L131.482422,75.6660156 C129.775382,72.4374839 128.253913,70.3408251 126.917969,69.3759766 C125.0996,68.1142515 123.040051,67.4833984 120.739258,67.4833984 L118.512695,67.4833984 L118.512695,100.603516 L93.1855469,100.603516 Z M118.512695,52.0644531 L129.144531,52.0644531 C130.294928,52.0644531 132.521468,51.6933631 135.824219,50.9511719 C137.494149,50.6171858 138.857905,49.7636787 139.915527,48.390625 C140.97315,47.0175713 141.501953,45.4404386 141.501953,43.6591797 C141.501953,41.0244009 140.667001,39.0019602 138.99707,37.5917969 C137.32714,36.1816336 134.191429,35.4765625 129.589844,35.4765625 L117.512695,35.4765625 L118.512695,52.0644531 Z\",\n      transform: \"translate(0 9)\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.length >= state.command.prefix.length && state1.selectedText.startsWith(state.command.prefix)) {\n      // Remove\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    } else {\n      // Add\n      state1 = api.setSelectionRange({\n        start: state.selection.start,\n        end: state.selection.start\n      });\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/hr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar image = {\n  name: 'image',\n  keyCommand: 'image',\n  shortcuts: 'ctrlcmd+k',\n  prefix: '![image](',\n  suffix: ')',\n  buttonProps: {\n    'aria-label': 'Add image (ctrl + k)',\n    title: 'Add image (ctrl + k)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    width: \"13\",\n    height: \"13\",\n    viewBox: \"0 0 20 20\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M15 9c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm4-7H1c-.55 0-1 .45-1 1v14c0 .55.45 1 1 1h18c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zm-1 13l-6-5-2 2-4-5-4 8V4h16v11z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.includes('http') || state1.selectedText.includes('www')) {\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    } else {\n      newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n        text: state.text,\n        selection: state.selection,\n        prefix: '![',\n        suffix: ']()'\n      });\n      state1 = api.setSelectionRange(newSelectionRange);\n      if (state1.selectedText.length === 0) {\n        (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: '![image',\n          suffix: '](url)'\n        });\n      } else {\n        (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: '![',\n          suffix: ']()'\n        });\n      }\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextAreaCommandOrchestrator: () => (/* binding */ TextAreaCommandOrchestrator),\n/* harmony export */   TextAreaTextApi: () => (/* binding */ TextAreaTextApi),\n/* harmony export */   bold: () => (/* reexport safe */ _bold__WEBPACK_IMPORTED_MODULE_2__.bold),\n/* harmony export */   checkedListCommand: () => (/* reexport safe */ _list__WEBPACK_IMPORTED_MODULE_12__.checkedListCommand),\n/* harmony export */   code: () => (/* reexport safe */ _code__WEBPACK_IMPORTED_MODULE_3__.code),\n/* harmony export */   codeBlock: () => (/* reexport safe */ _code__WEBPACK_IMPORTED_MODULE_3__.codeBlock),\n/* harmony export */   codeEdit: () => (/* reexport safe */ _preview__WEBPACK_IMPORTED_MODULE_13__.codeEdit),\n/* harmony export */   codeLive: () => (/* reexport safe */ _preview__WEBPACK_IMPORTED_MODULE_13__.codeLive),\n/* harmony export */   codePreview: () => (/* reexport safe */ _preview__WEBPACK_IMPORTED_MODULE_13__.codePreview),\n/* harmony export */   comment: () => (/* reexport safe */ _comment__WEBPACK_IMPORTED_MODULE_4__.comment),\n/* harmony export */   divider: () => (/* reexport safe */ _divider__WEBPACK_IMPORTED_MODULE_5__.divider),\n/* harmony export */   fullscreen: () => (/* reexport safe */ _fullscreen__WEBPACK_IMPORTED_MODULE_6__.fullscreen),\n/* harmony export */   getCommands: () => (/* binding */ getCommands),\n/* harmony export */   getExtraCommands: () => (/* binding */ getExtraCommands),\n/* harmony export */   getStateFromTextArea: () => (/* binding */ getStateFromTextArea),\n/* harmony export */   group: () => (/* reexport safe */ _group__WEBPACK_IMPORTED_MODULE_7__.group),\n/* harmony export */   help: () => (/* reexport safe */ _help__WEBPACK_IMPORTED_MODULE_25__.help),\n/* harmony export */   hr: () => (/* reexport safe */ _hr__WEBPACK_IMPORTED_MODULE_8__.hr),\n/* harmony export */   image: () => (/* reexport safe */ _image__WEBPACK_IMPORTED_MODULE_9__.image),\n/* harmony export */   issue: () => (/* reexport safe */ _issue__WEBPACK_IMPORTED_MODULE_24__.issue),\n/* harmony export */   italic: () => (/* reexport safe */ _italic__WEBPACK_IMPORTED_MODULE_10__.italic),\n/* harmony export */   link: () => (/* reexport safe */ _link__WEBPACK_IMPORTED_MODULE_11__.link),\n/* harmony export */   orderedListCommand: () => (/* reexport safe */ _list__WEBPACK_IMPORTED_MODULE_12__.orderedListCommand),\n/* harmony export */   quote: () => (/* reexport safe */ _quote__WEBPACK_IMPORTED_MODULE_14__.quote),\n/* harmony export */   strikethrough: () => (/* reexport safe */ _strikeThrough__WEBPACK_IMPORTED_MODULE_15__.strikethrough),\n/* harmony export */   table: () => (/* reexport safe */ _table__WEBPACK_IMPORTED_MODULE_23__.table),\n/* harmony export */   title: () => (/* reexport safe */ _title__WEBPACK_IMPORTED_MODULE_16__.title),\n/* harmony export */   title1: () => (/* reexport safe */ _title1__WEBPACK_IMPORTED_MODULE_17__.title1),\n/* harmony export */   title2: () => (/* reexport safe */ _title2__WEBPACK_IMPORTED_MODULE_18__.title2),\n/* harmony export */   title3: () => (/* reexport safe */ _title3__WEBPACK_IMPORTED_MODULE_19__.title3),\n/* harmony export */   title4: () => (/* reexport safe */ _title4__WEBPACK_IMPORTED_MODULE_20__.title4),\n/* harmony export */   title5: () => (/* reexport safe */ _title5__WEBPACK_IMPORTED_MODULE_21__.title5),\n/* harmony export */   title6: () => (/* reexport safe */ _title6__WEBPACK_IMPORTED_MODULE_22__.title6),\n/* harmony export */   unorderedListCommand: () => (/* reexport safe */ _list__WEBPACK_IMPORTED_MODULE_12__.unorderedListCommand)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/InsertTextAtPosition */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/InsertTextAtPosition.js\");\n/* harmony import */ var _bold__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bold */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/bold.js\");\n/* harmony import */ var _code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./code */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/code.js\");\n/* harmony import */ var _comment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./comment */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/comment.js\");\n/* harmony import */ var _divider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./divider */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/divider.js\");\n/* harmony import */ var _fullscreen__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./fullscreen */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/fullscreen.js\");\n/* harmony import */ var _group__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./group */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/group.js\");\n/* harmony import */ var _hr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./hr */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/hr.js\");\n/* harmony import */ var _image__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./image */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/image.js\");\n/* harmony import */ var _italic__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./italic */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/italic.js\");\n/* harmony import */ var _link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/link.js\");\n/* harmony import */ var _list__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/list.js\");\n/* harmony import */ var _preview__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./preview */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/preview.js\");\n/* harmony import */ var _quote__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./quote */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/quote.js\");\n/* harmony import */ var _strikeThrough__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./strikeThrough */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/strikeThrough.js\");\n/* harmony import */ var _title__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var _title1__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./title1 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title1.js\");\n/* harmony import */ var _title2__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./title2 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title2.js\");\n/* harmony import */ var _title3__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./title3 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title3.js\");\n/* harmony import */ var _title4__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./title4 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title4.js\");\n/* harmony import */ var _title5__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./title5 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title5.js\");\n/* harmony import */ var _title6__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./title6 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title6.js\");\n/* harmony import */ var _table__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./table */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/table.js\");\n/* harmony import */ var _issue__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./issue */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/issue.js\");\n/* harmony import */ var _help__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./help */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/help.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar getCommands = () => [_bold__WEBPACK_IMPORTED_MODULE_2__.bold, _italic__WEBPACK_IMPORTED_MODULE_10__.italic, _strikeThrough__WEBPACK_IMPORTED_MODULE_15__.strikethrough, _hr__WEBPACK_IMPORTED_MODULE_8__.hr, (0,_group__WEBPACK_IMPORTED_MODULE_7__.group)([_title1__WEBPACK_IMPORTED_MODULE_17__.title1, _title2__WEBPACK_IMPORTED_MODULE_18__.title2, _title3__WEBPACK_IMPORTED_MODULE_19__.title3, _title4__WEBPACK_IMPORTED_MODULE_20__.title4, _title5__WEBPACK_IMPORTED_MODULE_21__.title5, _title6__WEBPACK_IMPORTED_MODULE_22__.title6], {\n  name: 'title',\n  groupName: 'title',\n  buttonProps: {\n    'aria-label': 'Insert title',\n    title: 'Insert title'\n  }\n}), _divider__WEBPACK_IMPORTED_MODULE_5__.divider, _link__WEBPACK_IMPORTED_MODULE_11__.link, _quote__WEBPACK_IMPORTED_MODULE_14__.quote, _code__WEBPACK_IMPORTED_MODULE_3__.code, _code__WEBPACK_IMPORTED_MODULE_3__.codeBlock, _comment__WEBPACK_IMPORTED_MODULE_4__.comment, _image__WEBPACK_IMPORTED_MODULE_9__.image, _table__WEBPACK_IMPORTED_MODULE_23__.table, _divider__WEBPACK_IMPORTED_MODULE_5__.divider, _list__WEBPACK_IMPORTED_MODULE_12__.unorderedListCommand, _list__WEBPACK_IMPORTED_MODULE_12__.orderedListCommand, _list__WEBPACK_IMPORTED_MODULE_12__.checkedListCommand, _divider__WEBPACK_IMPORTED_MODULE_5__.divider, _help__WEBPACK_IMPORTED_MODULE_25__.help];\nvar getExtraCommands = () => [_preview__WEBPACK_IMPORTED_MODULE_13__.codeEdit, _preview__WEBPACK_IMPORTED_MODULE_13__.codeLive, _preview__WEBPACK_IMPORTED_MODULE_13__.codePreview, _divider__WEBPACK_IMPORTED_MODULE_5__.divider, _fullscreen__WEBPACK_IMPORTED_MODULE_6__.fullscreen];\nfunction getStateFromTextArea(textArea) {\n  var _textArea$value;\n  return {\n    selection: {\n      start: textArea.selectionStart,\n      end: textArea.selectionEnd\n    },\n    text: textArea.value,\n    selectedText: (_textArea$value = textArea.value) == null ? void 0 : _textArea$value.slice(textArea.selectionStart, textArea.selectionEnd)\n  };\n}\nclass TextAreaTextApi {\n  constructor(textArea) {\n    this.textArea = void 0;\n    this.textArea = textArea;\n  }\n\n  /**\n   * Replaces the current selection with the new text. This will make the new selectedText to be empty, the\n   * selection start and selection end will be the same and will both point to the end\n   * @param text Text that should replace the current selection\n   */\n  replaceSelection(text) {\n    (0,_utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_1__.insertTextAtPosition)(this.textArea, text);\n    return getStateFromTextArea(this.textArea);\n  }\n\n  /**\n   * Selects the specified text range\n   * @param selection\n   */\n  setSelectionRange(selection) {\n    this.textArea.focus();\n    this.textArea.selectionStart = selection.start;\n    this.textArea.selectionEnd = selection.end;\n    return getStateFromTextArea(this.textArea);\n  }\n}\nclass TextAreaCommandOrchestrator {\n  constructor(textArea) {\n    this.textArea = void 0;\n    this.textApi = void 0;\n    this.textArea = textArea;\n    this.textApi = new TextAreaTextApi(textArea);\n  }\n  getState() {\n    if (!this.textArea) return false;\n    return getStateFromTextArea(this.textArea);\n  }\n  executeCommand(command, dispatch, state, shortcuts) {\n    command.execute && command.execute(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      command\n    }, getStateFromTextArea(this.textArea)), this.textApi, dispatch, state, shortcuts);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/issue.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/issue.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   issue: () => (/* binding */ issue)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar issue = {\n  name: 'issue',\n  keyCommand: 'issue',\n  prefix: '#',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Add issue',\n    title: 'Add issue'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    role: \"img\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 448 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M181.3 32.4c17.4 2.9 29.2 19.4 26.3 36.8L197.8 128l95.1 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3s29.2 19.4 26.3 36.8L357.8 128l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0L325.8 320l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8l9.8-58.7-95.1 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8L90.2 384 32 384c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 21.3-128L64 192c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3zM187.1 192L165.8 320l95.1 0 21.3-128-95.1 0z\"\n      //Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/issue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/italic.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/italic.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   italic: () => (/* binding */ italic)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar italic = {\n  name: 'italic',\n  keyCommand: 'italic',\n  shortcuts: 'ctrlcmd+i',\n  prefix: '*',\n  buttonProps: {\n    'aria-label': 'Add italic text (ctrl + i)',\n    title: 'Add italic text (ctrl + i)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    \"data-name\": \"italic\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 320 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M204.758 416h-33.849l62.092-320h40.725a16 16 0 0 0 15.704-12.937l6.242-32C297.599 41.184 290.034 32 279.968 32H120.235a16 16 0 0 0-15.704 12.937l-6.242 32C96.362 86.816 103.927 96 113.993 96h33.846l-62.09 320H46.278a16 16 0 0 0-15.704 12.935l-6.245 32C22.402 470.815 29.967 480 40.034 480h158.479a16 16 0 0 0 15.704-12.935l6.245-32c1.927-9.88-5.638-19.065-15.704-19.065z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/italic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar link = {\n  name: 'link',\n  keyCommand: 'link',\n  shortcuts: 'ctrlcmd+l',\n  prefix: '[',\n  suffix: '](url)',\n  buttonProps: {\n    'aria-label': 'Add a link (ctrl + l)',\n    title: 'Add a link (ctrl + l)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    \"data-name\": \"italic\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M331.751196,182.121107 C392.438214,241.974735 391.605313,337.935283 332.11686,396.871226 C332.005129,396.991316 331.873084,397.121413 331.751196,397.241503 L263.493918,464.491645 C203.291404,523.80587 105.345257,523.797864 45.151885,464.491645 C-15.0506283,405.187427 -15.0506283,308.675467 45.151885,249.371249 L82.8416853,212.237562 C92.836501,202.39022 110.049118,208.9351 110.56511,222.851476 C111.223305,240.5867 114.451306,258.404985 120.407566,275.611815 C122.424812,281.438159 120.983487,287.882964 116.565047,292.23621 L103.272145,305.332975 C74.8052033,333.379887 73.9123737,379.047937 102.098973,407.369054 C130.563883,435.969378 177.350591,436.139505 206.033884,407.879434 L274.291163,340.6393 C302.9257,312.427264 302.805844,266.827265 274.291163,238.733318 C270.531934,235.036561 266.74528,232.16442 263.787465,230.157924 C259.544542,227.2873 256.928256,222.609848 256.731165,217.542518 C256.328935,206.967633 260.13184,196.070508 268.613213,187.714278 L289.998463,166.643567 C295.606326,161.118448 304.403592,160.439942 310.906317,164.911276 C318.353355,170.034591 325.328531,175.793397 331.751196,182.121107 Z M240.704978,55.4828366 L172.447607,122.733236 C172.325719,122.853326 172.193674,122.983423 172.081943,123.103513 C117.703294,179.334654 129.953294,261.569283 185.365841,328.828764 C191.044403,335.721376 198.762988,340.914712 206.209732,346.037661 C212.712465,350.509012 221.510759,349.829503 227.117615,344.305363 L248.502893,323.234572 C256.984277,314.87831 260.787188,303.981143 260.384957,293.406218 C260.187865,288.338869 257.571576,283.661398 253.328648,280.790763 C250.370829,278.78426 246.58417,275.912107 242.824936,272.215337 C214.310216,244.121282 206.209732,204.825874 229.906702,179.334654 L298.164073,112.094263 C326.847404,83.8340838 373.633159,84.0042113 402.099123,112.604645 C430.285761,140.92587 429.393946,186.594095 400.92595,214.641114 L387.63303,227.737929 C383.214584,232.091191 381.773257,238.536021 383.790506,244.362388 C389.746774,261.569283 392.974779,279.387637 393.632975,297.122928 C394.149984,311.039357 411.361608,317.584262 421.356437,307.736882 L459.046288,270.603053 C519.249898,211.29961 519.249898,114.787281 459.047304,55.4828366 C398.853851,-3.82360914 300.907572,-3.83161514 240.704978,55.4828366 Z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.includes('http') || state1.selectedText.includes('www')) {\n      newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n        text: state.text,\n        selection: state.selection,\n        prefix: '[](',\n        suffix: ')'\n      });\n      state1 = api.setSelectionRange(newSelectionRange);\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: '[](',\n        suffix: ')'\n      });\n    } else {\n      if (state1.selectedText.length === 0) {\n        (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: '[title',\n          suffix: '](url)'\n        });\n      } else {\n        (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: state.command.prefix,\n          suffix: state.command.suffix\n        });\n      }\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkedListCommand: () => (/* binding */ checkedListCommand),\n/* harmony export */   makeList: () => (/* binding */ makeList),\n/* harmony export */   orderedListCommand: () => (/* binding */ orderedListCommand),\n/* harmony export */   unorderedListCommand: () => (/* binding */ unorderedListCommand)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar makeList = (state, api, insertBefore) => {\n  var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n    text: state.text,\n    selection: state.selection,\n    prefix: state.command.prefix\n  });\n  var state1 = api.setSelectionRange(newSelectionRange);\n  var breaksBeforeCount = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.getBreaksNeededForEmptyLineBefore)(state1.text, state1.selection.start);\n  var breaksBefore = Array(breaksBeforeCount + 1).join('\\n');\n  var breaksAfterCount = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.getBreaksNeededForEmptyLineAfter)(state1.text, state1.selection.end);\n  var breaksAfter = Array(breaksAfterCount + 1).join('\\n');\n  var {\n    modifiedText,\n    insertionLength\n  } = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.insertBeforeEachLine)(state1.selectedText, insertBefore);\n  if (insertionLength < 0) {\n    // Remove\n    var selectionStart = state1.selection.start;\n    var selectionEnd = state1.selection.end;\n    if (state1.selection.start > 0 && state.text.slice(state1.selection.start - 1, state1.selection.start) === '\\n') {\n      selectionStart -= 1;\n    }\n    if (state1.selection.end < state.text.length - 1 && state.text.slice(state1.selection.end, state1.selection.end + 1) === '\\n') {\n      selectionEnd += 1;\n    }\n    api.setSelectionRange({\n      start: selectionStart,\n      end: selectionEnd\n    });\n    api.replaceSelection(\"\" + modifiedText);\n    api.setSelectionRange({\n      start: selectionStart,\n      end: selectionStart + modifiedText.length\n    });\n  } else {\n    // Add\n    api.replaceSelection(\"\" + breaksBefore + modifiedText + breaksAfter);\n    var _selectionStart = state1.selection.start + breaksBeforeCount;\n    var _selectionEnd = _selectionStart + modifiedText.length;\n    api.setSelectionRange({\n      start: _selectionStart,\n      end: _selectionEnd\n    });\n  }\n};\nvar unorderedListCommand = {\n  name: 'unordered-list',\n  keyCommand: 'list',\n  shortcuts: 'ctrl+shift+u',\n  prefix: '- ',\n  buttonProps: {\n    'aria-label': 'Add unordered list (ctrl + shift + u)',\n    title: 'Add unordered list (ctrl + shift + u)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    \"data-name\": \"unordered-list\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M96 96c0 26.51-21.49 48-48 48S0 122.51 0 96s21.49-48 48-48 48 21.49 48 48zM48 208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm0 160c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm96-236h352c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"\n    })\n  }),\n  execute: (state, api) => {\n    makeList(state, api, '- ');\n  }\n};\nvar orderedListCommand = {\n  name: 'ordered-list',\n  keyCommand: 'list',\n  shortcuts: 'ctrl+shift+o',\n  prefix: '1. ',\n  buttonProps: {\n    'aria-label': 'Add ordered list (ctrl + shift + o)',\n    title: 'Add ordered list (ctrl + shift + o)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    \"data-name\": \"ordered-list\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M3.263 139.527c0-7.477 3.917-11.572 11.573-11.572h15.131V88.078c0-5.163.534-10.503.534-10.503h-.356s-1.779 2.67-2.848 3.738c-4.451 4.273-10.504 4.451-15.666-1.068l-5.518-6.231c-5.342-5.341-4.984-11.216.534-16.379l21.72-19.938C32.815 33.602 36.732 32 42.785 32H54.89c7.656 0 11.749 3.916 11.749 11.572v84.384h15.488c7.655 0 11.572 4.094 11.572 11.572v8.901c0 7.477-3.917 11.572-11.572 11.572H14.836c-7.656 0-11.573-4.095-11.573-11.572v-8.902zM2.211 304.591c0-47.278 50.955-56.383 50.955-69.165 0-7.18-5.954-8.755-9.28-8.755-3.153 0-6.479 1.051-9.455 3.852-5.079 4.903-10.507 7.004-16.111 2.451l-8.579-6.829c-5.779-4.553-7.18-9.805-2.803-15.409C13.592 201.981 26.025 192 47.387 192c19.437 0 44.476 10.506 44.476 39.573 0 38.347-46.753 46.402-48.679 56.909h39.049c7.529 0 11.557 4.027 11.557 11.382v8.755c0 7.354-4.028 11.382-11.557 11.382h-67.94c-7.005 0-12.083-4.028-12.083-11.382v-4.028zM5.654 454.61l5.603-9.28c3.853-6.654 9.105-7.004 15.584-3.152 4.903 2.101 9.63 3.152 14.359 3.152 10.155 0 14.358-3.502 14.358-8.23 0-6.654-5.604-9.106-15.934-9.106h-4.728c-5.954 0-9.28-2.101-12.258-7.88l-1.05-1.926c-2.451-4.728-1.226-9.806 2.801-14.884l5.604-7.004c6.829-8.405 12.257-13.483 12.257-13.483v-.35s-4.203 1.051-12.608 1.051H16.685c-7.53 0-11.383-4.028-11.383-11.382v-8.755c0-7.53 3.853-11.382 11.383-11.382h58.484c7.529 0 11.382 4.027 11.382 11.382v3.327c0 5.778-1.401 9.806-5.079 14.183l-17.509 20.137c19.611 5.078 28.716 20.487 28.716 34.845 0 21.363-14.358 44.126-48.503 44.126-16.636 0-28.192-4.728-35.896-9.455-5.779-4.202-6.304-9.805-2.626-15.934zM144 132h352c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"\n    })\n  }),\n  execute: (state, api) => {\n    makeList(state, api, (item, index) => index + 1 + \". \");\n  }\n};\nvar checkedListCommand = {\n  name: 'checked-list',\n  keyCommand: 'list',\n  shortcuts: 'ctrl+shift+c',\n  prefix: '- [ ] ',\n  buttonProps: {\n    'aria-label': 'Add checked list (ctrl + shift + c)',\n    title: 'Add checked list (ctrl + shift + c)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    \"data-name\": \"checked-list\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M208 132h288c8.8 0 16-7.2 16-16V76c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zm0 160h288c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zm0 160h288c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zM64 368c-26.5 0-48.6 21.5-48.6 48s22.1 48 48.6 48 48-21.5 48-48-21.5-48-48-48zm92.5-299l-72.2 72.2-15.6 15.6c-4.7 4.7-12.9 4.7-17.6 0L3.5 109.4c-4.7-4.7-4.7-12.3 0-17l15.7-15.7c4.7-4.7 12.3-4.7 17 0l22.7 22.1 63.7-63.3c4.7-4.7 12.3-4.7 17 0l17 16.5c4.6 4.7 4.6 12.3-.1 17zm0 159.6l-72.2 72.2-15.7 15.7c-4.7 4.7-12.9 4.7-17.6 0L3.5 269c-4.7-4.7-4.7-12.3 0-17l15.7-15.7c4.7-4.7 12.3-4.7 17 0l22.7 22.1 63.7-63.7c4.7-4.7 12.3-4.7 17 0l17 17c4.6 4.6 4.6 12.2-.1 16.9z\"\n    })\n  }),\n  execute: (state, api) => {\n    makeList(state, api, (item, index) => \"- [ ] \");\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/preview.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/preview.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeEdit: () => (/* binding */ codeEdit),\n/* harmony export */   codeLive: () => (/* binding */ codeLive),\n/* harmony export */   codePreview: () => (/* binding */ codePreview)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar codePreview = {\n  name: 'preview',\n  keyCommand: 'preview',\n  value: 'preview',\n  shortcuts: 'ctrlcmd+9',\n  buttonProps: {\n    'aria-label': 'Preview code (ctrl + 9)',\n    title: 'Preview code (ctrl + 9)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"0 71.293 0 122 38.023 123 38.023 398 0 397 0 449.707 91.023 450.413 91.023 72.293\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"148.023 72.293 520 71.293 520 122 200.023 124 200.023 397 520 396 520 449.707 148.023 450.413\"\n    })]\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        preview: 'preview'\n      });\n    }\n  }\n};\nvar codeEdit = {\n  name: 'edit',\n  keyCommand: 'preview',\n  value: 'edit',\n  shortcuts: 'ctrlcmd+7',\n  buttonProps: {\n    'aria-label': 'Edit code (ctrl + 7)',\n    title: 'Edit code (ctrl + 7)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"0 71.293 0 122 319 122 319 397 0 397 0 449.707 372 449.413 372 71.293\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"429 71.293 520 71.293 520 122 481 123 481 396 520 396 520 449.707 429 449.413\"\n    })]\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        preview: 'edit'\n      });\n    }\n  }\n};\nvar codeLive = {\n  name: 'live',\n  keyCommand: 'preview',\n  value: 'live',\n  shortcuts: 'ctrlcmd+8',\n  buttonProps: {\n    'aria-label': 'Live code (ctrl + 8)',\n    title: 'Live code (ctrl + 8)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"0 71.293 0 122 179 122 179 397 0 397 0 449.707 232 449.413 232 71.293\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"289 71.293 520 71.293 520 122 341 123 341 396 520 396 520 449.707 289 449.413\"\n    })]\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        preview: 'live'\n      });\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/preview.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/quote.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/quote.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quote: () => (/* binding */ quote)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar quote = {\n  name: 'quote',\n  keyCommand: 'quote',\n  shortcuts: 'ctrlcmd+q',\n  prefix: '> ',\n  buttonProps: {\n    'aria-label': 'Insert a quote (ctrl + q)',\n    title: 'Insert a quote (ctrl + q)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M520,95.75 L520,225.75 C520,364.908906 457.127578,437.050625 325.040469,472.443125 C309.577578,476.586875 294.396016,464.889922 294.396016,448.881641 L294.396016,414.457031 C294.396016,404.242891 300.721328,395.025078 310.328125,391.554687 C377.356328,367.342187 414.375,349.711094 414.375,274.5 L341.25,274.5 C314.325781,274.5 292.5,252.674219 292.5,225.75 L292.5,95.75 C292.5,68.8257812 314.325781,47 341.25,47 L471.25,47 C498.174219,47 520,68.8257812 520,95.75 Z M178.75,47 L48.75,47 C21.8257813,47 0,68.8257812 0,95.75 L0,225.75 C0,252.674219 21.8257813,274.5 48.75,274.5 L121.875,274.5 C121.875,349.711094 84.8563281,367.342187 17.828125,391.554687 C8.22132813,395.025078 1.89601563,404.242891 1.89601563,414.457031 L1.89601563,448.881641 C1.89601563,464.889922 17.0775781,476.586875 32.5404687,472.443125 C164.627578,437.050625 227.5,364.908906 227.5,225.75 L227.5,95.75 C227.5,68.8257812 205.674219,47 178.75,47 Z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    var breaksBeforeCount = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.getBreaksNeededForEmptyLineBefore)(state1.text, state1.selection.start);\n    var breaksBefore = Array(breaksBeforeCount + 1).join('\\n');\n    var breaksAfterCount = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.getBreaksNeededForEmptyLineAfter)(state1.text, state1.selection.end);\n    var breaksAfter = Array(breaksAfterCount + 1).join('\\n');\n    var modifiedText = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.insertBeforeEachLine)(state1.selectedText, state.command.prefix);\n    api.replaceSelection(\"\" + breaksBefore + modifiedText.modifiedText + breaksAfter);\n    var selectionStart = state1.selection.start + breaksBeforeCount;\n    var selectionEnd = selectionStart + modifiedText.modifiedText.length;\n    api.setSelectionRange({\n      start: selectionStart,\n      end: selectionEnd\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/strikeThrough.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/strikeThrough.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar strikethrough = {\n  name: 'strikethrough',\n  keyCommand: 'strikethrough',\n  shortcuts: 'ctrl+shift+x',\n  buttonProps: {\n    'aria-label': 'Add strikethrough text (ctrl + shift + x)',\n    title: 'Add strikethrough text (ctrl + shift + x)'\n  },\n  prefix: '~~',\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    \"data-name\": \"strikethrough\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M496 288H16c-8.837 0-16-7.163-16-16v-32c0-8.837 7.163-16 16-16h480c8.837 0 16 7.163 16 16v32c0 8.837-7.163 16-16 16zm-214.666 16c27.258 12.937 46.524 28.683 46.524 56.243 0 33.108-28.977 53.676-75.621 53.676-32.325 0-76.874-12.08-76.874-44.271V368c0-8.837-7.164-16-16-16H113.75c-8.836 0-16 7.163-16 16v19.204c0 66.845 77.717 101.82 154.487 101.82 88.578 0 162.013-45.438 162.013-134.424 0-19.815-3.618-36.417-10.143-50.6H281.334zm-30.952-96c-32.422-13.505-56.836-28.946-56.836-59.683 0-33.92 30.901-47.406 64.962-47.406 42.647 0 64.962 16.593 64.962 32.985V136c0 8.837 7.164 16 16 16h45.613c8.836 0 16-7.163 16-16v-30.318c0-52.438-71.725-79.875-142.575-79.875-85.203 0-150.726 40.972-150.726 125.646 0 22.71 4.665 41.176 12.777 56.547h129.823z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3N0cmlrZVRocm91Z2guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBQzBDO0FBQ3BCO0FBQ3pDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsc0RBQUk7QUFDL0I7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQSw0QkFBNEIsZ0VBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsSUFBSSxvRUFBYztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL0B1aXcvcmVhY3QtbWQtZWRpdG9yL2VzbS9jb21tYW5kcy9zdHJpa2VUaHJvdWdoLmpzPzc5ODciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHNlbGVjdFdvcmQsIGV4ZWN1dGVDb21tYW5kIH0gZnJvbSAnLi4vdXRpbHMvbWFya2Rvd25VdGlscyc7XG5pbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZXhwb3J0IHZhciBzdHJpa2V0aHJvdWdoID0ge1xuICBuYW1lOiAnc3RyaWtldGhyb3VnaCcsXG4gIGtleUNvbW1hbmQ6ICdzdHJpa2V0aHJvdWdoJyxcbiAgc2hvcnRjdXRzOiAnY3RybCtzaGlmdCt4JyxcbiAgYnV0dG9uUHJvcHM6IHtcbiAgICAnYXJpYS1sYWJlbCc6ICdBZGQgc3RyaWtldGhyb3VnaCB0ZXh0IChjdHJsICsgc2hpZnQgKyB4KScsXG4gICAgdGl0bGU6ICdBZGQgc3RyaWtldGhyb3VnaCB0ZXh0IChjdHJsICsgc2hpZnQgKyB4KSdcbiAgfSxcbiAgcHJlZml4OiAnfn4nLFxuICBpY29uOiAvKiNfX1BVUkVfXyovX2pzeChcInN2Z1wiLCB7XG4gICAgXCJkYXRhLW5hbWVcIjogXCJzdHJpa2V0aHJvdWdoXCIsXG4gICAgd2lkdGg6IFwiMTJcIixcbiAgICBoZWlnaHQ6IFwiMTJcIixcbiAgICByb2xlOiBcImltZ1wiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDUxMiA1MTJcIixcbiAgICBjaGlsZHJlbjogLyojX19QVVJFX18qL19qc3goXCJwYXRoXCIsIHtcbiAgICAgIGZpbGw6IFwiY3VycmVudENvbG9yXCIsXG4gICAgICBkOiBcIk00OTYgMjg4SDE2Yy04LjgzNyAwLTE2LTcuMTYzLTE2LTE2di0zMmMwLTguODM3IDcuMTYzLTE2IDE2LTE2aDQ4MGM4LjgzNyAwIDE2IDcuMTYzIDE2IDE2djMyYzAgOC44MzctNy4xNjMgMTYtMTYgMTZ6bS0yMTQuNjY2IDE2YzI3LjI1OCAxMi45MzcgNDYuNTI0IDI4LjY4MyA0Ni41MjQgNTYuMjQzIDAgMzMuMTA4LTI4Ljk3NyA1My42NzYtNzUuNjIxIDUzLjY3Ni0zMi4zMjUgMC03Ni44NzQtMTIuMDgtNzYuODc0LTQ0LjI3MVYzNjhjMC04LjgzNy03LjE2NC0xNi0xNi0xNkgxMTMuNzVjLTguODM2IDAtMTYgNy4xNjMtMTYgMTZ2MTkuMjA0YzAgNjYuODQ1IDc3LjcxNyAxMDEuODIgMTU0LjQ4NyAxMDEuODIgODguNTc4IDAgMTYyLjAxMy00NS40MzggMTYyLjAxMy0xMzQuNDI0IDAtMTkuODE1LTMuNjE4LTM2LjQxNy0xMC4xNDMtNTAuNkgyODEuMzM0em0tMzAuOTUyLTk2Yy0zMi40MjItMTMuNTA1LTU2LjgzNi0yOC45NDYtNTYuODM2LTU5LjY4MyAwLTMzLjkyIDMwLjkwMS00Ny40MDYgNjQuOTYyLTQ3LjQwNiA0Mi42NDcgMCA2NC45NjIgMTYuNTkzIDY0Ljk2MiAzMi45ODVWMTM2YzAgOC44MzcgNy4xNjQgMTYgMTYgMTZoNDUuNjEzYzguODM2IDAgMTYtNy4xNjMgMTYtMTZ2LTMwLjMxOGMwLTUyLjQzOC03MS43MjUtNzkuODc1LTE0Mi41NzUtNzkuODc1LTg1LjIwMyAwLTE1MC43MjYgNDAuOTcyLTE1MC43MjYgMTI1LjY0NiAwIDIyLjcxIDQuNjY1IDQxLjE3NiAxMi43NzcgNTYuNTQ3aDEyOS44MjN6XCJcbiAgICB9KVxuICB9KSxcbiAgZXhlY3V0ZTogKHN0YXRlLCBhcGkpID0+IHtcbiAgICB2YXIgbmV3U2VsZWN0aW9uUmFuZ2UgPSBzZWxlY3RXb3JkKHtcbiAgICAgIHRleHQ6IHN0YXRlLnRleHQsXG4gICAgICBzZWxlY3Rpb246IHN0YXRlLnNlbGVjdGlvbixcbiAgICAgIHByZWZpeDogc3RhdGUuY29tbWFuZC5wcmVmaXhcbiAgICB9KTtcbiAgICB2YXIgc3RhdGUxID0gYXBpLnNldFNlbGVjdGlvblJhbmdlKG5ld1NlbGVjdGlvblJhbmdlKTtcbiAgICBleGVjdXRlQ29tbWFuZCh7XG4gICAgICBhcGksXG4gICAgICBzZWxlY3RlZFRleHQ6IHN0YXRlMS5zZWxlY3RlZFRleHQsXG4gICAgICBzZWxlY3Rpb246IHN0YXRlLnNlbGVjdGlvbixcbiAgICAgIHByZWZpeDogc3RhdGUuY29tbWFuZC5wcmVmaXhcbiAgICB9KTtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/strikeThrough.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/table.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/table.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar table = {\n  name: 'table',\n  keyCommand: 'table',\n  prefix: '\\n| Header | Header |\\n|--------|--------|\\n| Cell | Cell |\\n| Cell | Cell |\\n| Cell | Cell |\\n\\n',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Add table',\n    title: 'Add table'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    role: \"img\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M64 256V160H224v96H64zm0 64H224v96H64V320zm224 96V320H448v96H288zM448 256H288V160H448v96zM64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z\"\n      //Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectWord)({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.length >= state.command.prefix.length + state.command.suffix.length && state1.selectedText.startsWith(state.command.prefix)) {\n      // Remove\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    } else {\n      // Add\n      state1 = api.setSelectionRange({\n        start: state.selection.start,\n        end: state.selection.start\n      });\n      (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.executeCommand)({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title: () => (/* binding */ title),\n/* harmony export */   titleExecute: () => (/* binding */ titleExecute)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _title1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./title1 */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title1.js\");\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nfunction titleExecute(_ref) {\n  var {\n    state,\n    api,\n    prefix,\n    suffix = prefix\n  } = _ref;\n  var newSelectionRange = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_3__.selectLine)({\n    text: state.text,\n    selection: state.selection\n  });\n  var state1 = api.setSelectionRange(newSelectionRange);\n  (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_3__.executeCommand)({\n    api,\n    selectedText: state1.selectedText,\n    selection: state.selection,\n    prefix,\n    suffix\n  });\n}\nvar title = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, _title1__WEBPACK_IMPORTED_MODULE_2__.title1, {\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M15.7083333,468 C7.03242448,468 0,462.030833 0,454.666667 L0,421.333333 C0,413.969167 7.03242448,408 15.7083333,408 L361.291667,408 C369.967576,408 377,413.969167 377,421.333333 L377,454.666667 C377,462.030833 369.967576,468 361.291667,468 L15.7083333,468 Z M21.6666667,366 C9.69989583,366 0,359.831861 0,352.222222 L0,317.777778 C0,310.168139 9.69989583,304 21.6666667,304 L498.333333,304 C510.300104,304 520,310.168139 520,317.777778 L520,352.222222 C520,359.831861 510.300104,366 498.333333,366 L21.6666667,366 Z M136.835938,64 L136.835937,126 L107.25,126 L107.25,251 L40.75,251 L40.75,126 L-5.68434189e-14,126 L-5.68434189e-14,64 L136.835938,64 Z M212,64 L212,251 L161.648438,251 L161.648438,64 L212,64 Z M378,64 L378,126 L343.25,126 L343.25,251 L281.75,251 L281.75,126 L238,126 L238,64 L378,64 Z M449.047619,189.550781 L520,189.550781 L520,251 L405,251 L405,64 L449.047619,64 L449.047619,189.550781 Z\"\n    })\n  })\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title1.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title1.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title1: () => (/* binding */ title1)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _commands_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../commands/title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar title1 = {\n  name: 'title1',\n  keyCommand: 'title1',\n  shortcuts: 'ctrlcmd+1',\n  prefix: '# ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title1 (ctrl + 1)',\n    title: 'Insert title1 (ctrl + 1)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    style: {\n      fontSize: 18,\n      textAlign: 'left'\n    },\n    children: \"Title 1\"\n  }),\n  execute: (state, api) => {\n    (0,_commands_title__WEBPACK_IMPORTED_MODULE_1__.titleExecute)({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3RpdGxlMS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDdUI7QUFDRDtBQUN6QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksNkRBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvdGl0bGUxLmpzPzMxZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHRpdGxlRXhlY3V0ZSB9IGZyb20gJy4uL2NvbW1hbmRzL3RpdGxlJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgdmFyIHRpdGxlMSA9IHtcbiAgbmFtZTogJ3RpdGxlMScsXG4gIGtleUNvbW1hbmQ6ICd0aXRsZTEnLFxuICBzaG9ydGN1dHM6ICdjdHJsY21kKzEnLFxuICBwcmVmaXg6ICcjICcsXG4gIHN1ZmZpeDogJycsXG4gIGJ1dHRvblByb3BzOiB7XG4gICAgJ2FyaWEtbGFiZWwnOiAnSW5zZXJ0IHRpdGxlMSAoY3RybCArIDEpJyxcbiAgICB0aXRsZTogJ0luc2VydCB0aXRsZTEgKGN0cmwgKyAxKSdcbiAgfSxcbiAgaWNvbjogLyojX19QVVJFX18qL19qc3goXCJkaXZcIiwge1xuICAgIHN0eWxlOiB7XG4gICAgICBmb250U2l6ZTogMTgsXG4gICAgICB0ZXh0QWxpZ246ICdsZWZ0J1xuICAgIH0sXG4gICAgY2hpbGRyZW46IFwiVGl0bGUgMVwiXG4gIH0pLFxuICBleGVjdXRlOiAoc3RhdGUsIGFwaSkgPT4ge1xuICAgIHRpdGxlRXhlY3V0ZSh7XG4gICAgICBzdGF0ZSxcbiAgICAgIGFwaSxcbiAgICAgIHByZWZpeDogc3RhdGUuY29tbWFuZC5wcmVmaXgsXG4gICAgICBzdWZmaXg6IHN0YXRlLmNvbW1hbmQuc3VmZml4XG4gICAgfSk7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title2.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title2.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title2: () => (/* binding */ title2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _commands_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../commands/title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar title2 = {\n  name: 'title2',\n  keyCommand: 'title2',\n  shortcuts: 'ctrlcmd+2',\n  prefix: '## ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title2 (ctrl + 2)',\n    title: 'Insert title2 (ctrl + 2)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    style: {\n      fontSize: 16,\n      textAlign: 'left'\n    },\n    children: \"Title 2\"\n  }),\n  execute: (state, api) => {\n    (0,_commands_title__WEBPACK_IMPORTED_MODULE_1__.titleExecute)({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3RpdGxlMi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDdUI7QUFDRDtBQUN6QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksNkRBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvdGl0bGUyLmpzPzExMTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHRpdGxlRXhlY3V0ZSB9IGZyb20gJy4uL2NvbW1hbmRzL3RpdGxlJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgdmFyIHRpdGxlMiA9IHtcbiAgbmFtZTogJ3RpdGxlMicsXG4gIGtleUNvbW1hbmQ6ICd0aXRsZTInLFxuICBzaG9ydGN1dHM6ICdjdHJsY21kKzInLFxuICBwcmVmaXg6ICcjIyAnLFxuICBzdWZmaXg6ICcnLFxuICBidXR0b25Qcm9wczoge1xuICAgICdhcmlhLWxhYmVsJzogJ0luc2VydCB0aXRsZTIgKGN0cmwgKyAyKScsXG4gICAgdGl0bGU6ICdJbnNlcnQgdGl0bGUyIChjdHJsICsgMiknXG4gIH0sXG4gIGljb246IC8qI19fUFVSRV9fKi9fanN4KFwiZGl2XCIsIHtcbiAgICBzdHlsZToge1xuICAgICAgZm9udFNpemU6IDE2LFxuICAgICAgdGV4dEFsaWduOiAnbGVmdCdcbiAgICB9LFxuICAgIGNoaWxkcmVuOiBcIlRpdGxlIDJcIlxuICB9KSxcbiAgZXhlY3V0ZTogKHN0YXRlLCBhcGkpID0+IHtcbiAgICB0aXRsZUV4ZWN1dGUoe1xuICAgICAgc3RhdGUsXG4gICAgICBhcGksXG4gICAgICBwcmVmaXg6IHN0YXRlLmNvbW1hbmQucHJlZml4LFxuICAgICAgc3VmZml4OiBzdGF0ZS5jb21tYW5kLnN1ZmZpeFxuICAgIH0pO1xuICB9XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title3.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title3.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title3: () => (/* binding */ title3)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _commands_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../commands/title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar title3 = {\n  name: 'title3',\n  keyCommand: 'title3',\n  shortcuts: 'ctrlcmd+3',\n  prefix: '### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title3 (ctrl + 3)',\n    title: 'Insert title3 (ctrl + 3)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    style: {\n      fontSize: 15,\n      textAlign: 'left'\n    },\n    children: \"Title 3\"\n  }),\n  execute: (state, api) => {\n    (0,_commands_title__WEBPACK_IMPORTED_MODULE_1__.titleExecute)({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3RpdGxlMy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDdUI7QUFDRDtBQUN6QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksNkRBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvdGl0bGUzLmpzPzE1ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHRpdGxlRXhlY3V0ZSB9IGZyb20gJy4uL2NvbW1hbmRzL3RpdGxlJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgdmFyIHRpdGxlMyA9IHtcbiAgbmFtZTogJ3RpdGxlMycsXG4gIGtleUNvbW1hbmQ6ICd0aXRsZTMnLFxuICBzaG9ydGN1dHM6ICdjdHJsY21kKzMnLFxuICBwcmVmaXg6ICcjIyMgJyxcbiAgc3VmZml4OiAnJyxcbiAgYnV0dG9uUHJvcHM6IHtcbiAgICAnYXJpYS1sYWJlbCc6ICdJbnNlcnQgdGl0bGUzIChjdHJsICsgMyknLFxuICAgIHRpdGxlOiAnSW5zZXJ0IHRpdGxlMyAoY3RybCArIDMpJ1xuICB9LFxuICBpY29uOiAvKiNfX1BVUkVfXyovX2pzeChcImRpdlwiLCB7XG4gICAgc3R5bGU6IHtcbiAgICAgIGZvbnRTaXplOiAxNSxcbiAgICAgIHRleHRBbGlnbjogJ2xlZnQnXG4gICAgfSxcbiAgICBjaGlsZHJlbjogXCJUaXRsZSAzXCJcbiAgfSksXG4gIGV4ZWN1dGU6IChzdGF0ZSwgYXBpKSA9PiB7XG4gICAgdGl0bGVFeGVjdXRlKHtcbiAgICAgIHN0YXRlLFxuICAgICAgYXBpLFxuICAgICAgcHJlZml4OiBzdGF0ZS5jb21tYW5kLnByZWZpeCxcbiAgICAgIHN1ZmZpeDogc3RhdGUuY29tbWFuZC5zdWZmaXhcbiAgICB9KTtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title4.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title4.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title4: () => (/* binding */ title4)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _commands_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../commands/title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar title4 = {\n  name: 'title4',\n  keyCommand: 'title4',\n  shortcuts: 'ctrlcmd+4',\n  prefix: '#### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title4 (ctrl + 4)',\n    title: 'Insert title4 (ctrl + 4)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    style: {\n      fontSize: 14,\n      textAlign: 'left'\n    },\n    children: \"Title 4\"\n  }),\n  execute: (state, api) => {\n    (0,_commands_title__WEBPACK_IMPORTED_MODULE_1__.titleExecute)({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3RpdGxlNC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDdUI7QUFDRDtBQUN6QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksNkRBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvdGl0bGU0LmpzPzNlYzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHRpdGxlRXhlY3V0ZSB9IGZyb20gJy4uL2NvbW1hbmRzL3RpdGxlJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgdmFyIHRpdGxlNCA9IHtcbiAgbmFtZTogJ3RpdGxlNCcsXG4gIGtleUNvbW1hbmQ6ICd0aXRsZTQnLFxuICBzaG9ydGN1dHM6ICdjdHJsY21kKzQnLFxuICBwcmVmaXg6ICcjIyMjICcsXG4gIHN1ZmZpeDogJycsXG4gIGJ1dHRvblByb3BzOiB7XG4gICAgJ2FyaWEtbGFiZWwnOiAnSW5zZXJ0IHRpdGxlNCAoY3RybCArIDQpJyxcbiAgICB0aXRsZTogJ0luc2VydCB0aXRsZTQgKGN0cmwgKyA0KSdcbiAgfSxcbiAgaWNvbjogLyojX19QVVJFX18qL19qc3goXCJkaXZcIiwge1xuICAgIHN0eWxlOiB7XG4gICAgICBmb250U2l6ZTogMTQsXG4gICAgICB0ZXh0QWxpZ246ICdsZWZ0J1xuICAgIH0sXG4gICAgY2hpbGRyZW46IFwiVGl0bGUgNFwiXG4gIH0pLFxuICBleGVjdXRlOiAoc3RhdGUsIGFwaSkgPT4ge1xuICAgIHRpdGxlRXhlY3V0ZSh7XG4gICAgICBzdGF0ZSxcbiAgICAgIGFwaSxcbiAgICAgIHByZWZpeDogc3RhdGUuY29tbWFuZC5wcmVmaXgsXG4gICAgICBzdWZmaXg6IHN0YXRlLmNvbW1hbmQuc3VmZml4XG4gICAgfSk7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title5.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title5.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title5: () => (/* binding */ title5)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _commands_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../commands/title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar title5 = {\n  name: 'title5',\n  keyCommand: 'title5',\n  shortcuts: 'ctrlcmd+5',\n  prefix: '##### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title5 (ctrl + 5)',\n    title: 'Insert title5 (ctrl + 5)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    style: {\n      fontSize: 12,\n      textAlign: 'left'\n    },\n    children: \"Title 5\"\n  }),\n  execute: (state, api) => {\n    (0,_commands_title__WEBPACK_IMPORTED_MODULE_1__.titleExecute)({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3RpdGxlNS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDdUI7QUFDRDtBQUN6QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksNkRBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvdGl0bGU1LmpzPzJiMDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHRpdGxlRXhlY3V0ZSB9IGZyb20gJy4uL2NvbW1hbmRzL3RpdGxlJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgdmFyIHRpdGxlNSA9IHtcbiAgbmFtZTogJ3RpdGxlNScsXG4gIGtleUNvbW1hbmQ6ICd0aXRsZTUnLFxuICBzaG9ydGN1dHM6ICdjdHJsY21kKzUnLFxuICBwcmVmaXg6ICcjIyMjIyAnLFxuICBzdWZmaXg6ICcnLFxuICBidXR0b25Qcm9wczoge1xuICAgICdhcmlhLWxhYmVsJzogJ0luc2VydCB0aXRsZTUgKGN0cmwgKyA1KScsXG4gICAgdGl0bGU6ICdJbnNlcnQgdGl0bGU1IChjdHJsICsgNSknXG4gIH0sXG4gIGljb246IC8qI19fUFVSRV9fKi9fanN4KFwiZGl2XCIsIHtcbiAgICBzdHlsZToge1xuICAgICAgZm9udFNpemU6IDEyLFxuICAgICAgdGV4dEFsaWduOiAnbGVmdCdcbiAgICB9LFxuICAgIGNoaWxkcmVuOiBcIlRpdGxlIDVcIlxuICB9KSxcbiAgZXhlY3V0ZTogKHN0YXRlLCBhcGkpID0+IHtcbiAgICB0aXRsZUV4ZWN1dGUoe1xuICAgICAgc3RhdGUsXG4gICAgICBhcGksXG4gICAgICBwcmVmaXg6IHN0YXRlLmNvbW1hbmQucHJlZml4LFxuICAgICAgc3VmZml4OiBzdGF0ZS5jb21tYW5kLnN1ZmZpeFxuICAgIH0pO1xuICB9XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title6.js":
/*!******************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/commands/title6.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   title6: () => (/* binding */ title6)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _commands_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../commands/title */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar title6 = {\n  name: 'title6',\n  keyCommand: 'title6',\n  shortcuts: 'ctrlcmd+6',\n  prefix: '###### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title6 (ctrl + 6)',\n    title: 'Insert title6 (ctrl + 6)'\n  },\n  icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    style: {\n      fontSize: 12,\n      textAlign: 'left'\n    },\n    children: \"Title 6\"\n  }),\n  execute: (state, api) => {\n    (0,_commands_title__WEBPACK_IMPORTED_MODULE_1__.titleExecute)({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbW1hbmRzL3RpdGxlNi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDdUI7QUFDRDtBQUN6QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLHNEQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksNkRBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tbWFuZHMvdGl0bGU2LmpzPzE5MzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHRpdGxlRXhlY3V0ZSB9IGZyb20gJy4uL2NvbW1hbmRzL3RpdGxlJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgdmFyIHRpdGxlNiA9IHtcbiAgbmFtZTogJ3RpdGxlNicsXG4gIGtleUNvbW1hbmQ6ICd0aXRsZTYnLFxuICBzaG9ydGN1dHM6ICdjdHJsY21kKzYnLFxuICBwcmVmaXg6ICcjIyMjIyMgJyxcbiAgc3VmZml4OiAnJyxcbiAgYnV0dG9uUHJvcHM6IHtcbiAgICAnYXJpYS1sYWJlbCc6ICdJbnNlcnQgdGl0bGU2IChjdHJsICsgNiknLFxuICAgIHRpdGxlOiAnSW5zZXJ0IHRpdGxlNiAoY3RybCArIDYpJ1xuICB9LFxuICBpY29uOiAvKiNfX1BVUkVfXyovX2pzeChcImRpdlwiLCB7XG4gICAgc3R5bGU6IHtcbiAgICAgIGZvbnRTaXplOiAxMixcbiAgICAgIHRleHRBbGlnbjogJ2xlZnQnXG4gICAgfSxcbiAgICBjaGlsZHJlbjogXCJUaXRsZSA2XCJcbiAgfSksXG4gIGV4ZWN1dGU6IChzdGF0ZSwgYXBpKSA9PiB7XG4gICAgdGl0bGVFeGVjdXRlKHtcbiAgICAgIHN0YXRlLFxuICAgICAgYXBpLFxuICAgICAgcHJlZml4OiBzdGF0ZS5jb21tYW5kLnByZWZpeCxcbiAgICAgIHN1ZmZpeDogc3RhdGUuY29tbWFuZC5zdWZmaXhcbiAgICB9KTtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/title6.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.css */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.css\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar DragBar = props => {\n  var {\n    prefixCls,\n    onChange\n  } = props || {};\n  var $dom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var dragRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  var heightRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props.height);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (heightRef.current !== props.height) {\n      heightRef.current = props.height;\n    }\n  }, [props.height]);\n  function handleMouseMove(event) {\n    if (dragRef.current) {\n      var _changedTouches$;\n      var clientY = event.clientY || ((_changedTouches$ = event.changedTouches[0]) == null ? void 0 : _changedTouches$.clientY);\n      var newHeight = dragRef.current.height + clientY - dragRef.current.dragY;\n      if (newHeight >= props.minHeight && newHeight <= props.maxHeight) {\n        onChange && onChange(dragRef.current.height + (clientY - dragRef.current.dragY));\n      }\n    }\n  }\n  function handleMouseUp() {\n    var _$dom$current, _$dom$current2;\n    dragRef.current = undefined;\n    document.removeEventListener('mousemove', handleMouseMove);\n    document.removeEventListener('mouseup', handleMouseUp);\n    (_$dom$current = $dom.current) == null || _$dom$current.removeEventListener('touchmove', handleMouseMove);\n    (_$dom$current2 = $dom.current) == null || _$dom$current2.removeEventListener('touchend', handleMouseUp);\n  }\n  function handleMouseDown(event) {\n    var _changedTouches$2, _$dom$current3, _$dom$current4;\n    event.preventDefault();\n    var clientY = event.clientY || ((_changedTouches$2 = event.changedTouches[0]) == null ? void 0 : _changedTouches$2.clientY);\n    dragRef.current = {\n      height: heightRef.current,\n      dragY: clientY\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    (_$dom$current3 = $dom.current) == null || _$dom$current3.addEventListener('touchmove', handleMouseMove, {\n      passive: false\n    });\n    (_$dom$current4 = $dom.current) == null || _$dom$current4.addEventListener('touchend', handleMouseUp, {\n      passive: false\n    });\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (document) {\n      var _$dom$current5, _$dom$current6;\n      (_$dom$current5 = $dom.current) == null || _$dom$current5.addEventListener('touchstart', handleMouseDown, {\n        passive: false\n      });\n      (_$dom$current6 = $dom.current) == null || _$dom$current6.addEventListener('mousedown', handleMouseDown);\n    }\n    return () => {\n      if (document) {\n        var _$dom$current7;\n        (_$dom$current7 = $dom.current) == null || _$dom$current7.removeEventListener('touchstart', handleMouseDown);\n        document.removeEventListener('mousemove', handleMouseMove);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var svg = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"svg\", {\n    viewBox: \"0 0 512 512\",\n    height: \"100%\",\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"path\", {\n      fill: \"currentColor\",\n      d: \"M304 256c0 26.5-21.5 48-48 48s-48-21.5-48-48 21.5-48 48-48 48 21.5 48 48zm120-48c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48zm-336 0c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48z\"\n    })\n  }), []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    className: prefixCls + \"-bar\",\n    ref: $dom,\n    children: svg\n  });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DragBar);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/Markdown.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/TextArea/Markdown.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Markdown)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteralLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/taggedTemplateLiteralLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/taggedTemplateLiteralLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteralLoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_taggedTemplateLiteralLoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rehype__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rehype */ \"(ssr)/./node_modules/rehype/index.js\");\n/* harmony import */ var rehype_prism_plus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rehype-prism-plus */ \"(ssr)/./node_modules/rehype-prism-plus/dist/index.es.js\");\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _templateObject;\n\n\n\n\n\nfunction html2Escape(sHtml) {\n  return sHtml\n  // .replace(/```(\\w+)?([\\s\\S]*?)(\\s.+)?```/g, (str: string) => {\n  //   return str.replace(\n  //     /[<&\"]/g,\n  //     (c: string) => (({ '<': '&lt;', '>': '&gt;', '&': '&amp;', '\"': '&quot;' } as Record<string, string>)[c]),\n  //   );\n  // })\n  .replace(/[<&\"]/g, c => ({\n    '<': '&lt;',\n    '>': '&gt;',\n    '&': '&amp;',\n    '\"': '&quot;'\n  })[c]);\n}\nfunction Markdown(props) {\n  var {\n    prefixCls\n  } = props;\n  var {\n    markdown = '',\n    highlightEnable,\n    dispatch\n  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Context__WEBPACK_IMPORTED_MODULE_3__.EditorContext);\n  var preRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createRef();\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (preRef.current && dispatch) {\n      dispatch({\n        textareaPre: preRef.current\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  if (!markdown) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"pre\", {\n      ref: preRef,\n      className: prefixCls + \"-text-pre wmde-markdown-color\"\n    });\n  }\n  var mdStr = \"<pre class=\\\"language-markdown \" + prefixCls + \"-text-pre wmde-markdown-color\\\"><code class=\\\"language-markdown\\\">\" + html2Escape(String.raw(_templateObject || (_templateObject = _babel_runtime_helpers_taggedTemplateLiteralLoose__WEBPACK_IMPORTED_MODULE_0___default()([\"\", \"\"])), markdown)) + \"\\n</code></pre>\";\n  if (highlightEnable) {\n    try {\n      mdStr = (0,rehype__WEBPACK_IMPORTED_MODULE_5__.rehype)().data('settings', {\n        fragment: true\n      })\n      // https://github.com/uiwjs/react-md-editor/issues/593\n      // @ts-ignore\n      .use(rehype_prism_plus__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        ignoreMissing: true\n      }).processSync(mdStr).toString();\n    } catch (error) {}\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement('div', {\n    className: 'wmde-markdown-color',\n    dangerouslySetInnerHTML: {\n      __html: mdStr || ''\n    }\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVGV4dEFyZWEvTWFya2Rvd24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE0RjtBQUM1RjtBQUNxRDtBQUNyQjtBQUNZO0FBQ0U7QUFDRTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFVBQVUsYUFBYSxjQUFjLGVBQWUsSUFBSTtBQUNwRjtBQUNBLE1BQU07QUFDTjtBQUNBLGNBQWM7QUFDZCxjQUFjO0FBQ2QsZUFBZTtBQUNmLGdCQUFnQjtBQUNoQixHQUFHO0FBQ0g7QUFDZTtBQUNmO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLEVBQUUsaURBQVUsQ0FBQyxtREFBYTtBQUM5Qiw0QkFBNEIsc0RBQWU7QUFDM0MsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0Esd0JBQXdCLHNEQUFJO0FBQzVCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxrTUFBa00sd0ZBQTJCO0FBQzdOO0FBQ0E7QUFDQSxjQUFjLDhDQUFNO0FBQ3BCO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxXQUFXLHlEQUFXO0FBQ3RCO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVGV4dEFyZWEvTWFya2Rvd24uanM/OTM1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX3RhZ2dlZFRlbXBsYXRlTGl0ZXJhbExvb3NlIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL3RhZ2dlZFRlbXBsYXRlTGl0ZXJhbExvb3NlXCI7XG52YXIgX3RlbXBsYXRlT2JqZWN0O1xuaW1wb3J0IFJlYWN0LCB7IHVzZUNvbnRleHQsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHJlaHlwZSB9IGZyb20gJ3JlaHlwZSc7XG5pbXBvcnQgcmVoeXBlUHJpc20gZnJvbSAncmVoeXBlLXByaXNtLXBsdXMnO1xuaW1wb3J0IHsgRWRpdG9yQ29udGV4dCB9IGZyb20gJy4uLy4uL0NvbnRleHQnO1xuaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmZ1bmN0aW9uIGh0bWwyRXNjYXBlKHNIdG1sKSB7XG4gIHJldHVybiBzSHRtbFxuICAvLyAucmVwbGFjZSgvYGBgKFxcdyspPyhbXFxzXFxTXSo/KShcXHMuKyk/YGBgL2csIChzdHI6IHN0cmluZykgPT4ge1xuICAvLyAgIHJldHVybiBzdHIucmVwbGFjZShcbiAgLy8gICAgIC9bPCZcIl0vZyxcbiAgLy8gICAgIChjOiBzdHJpbmcpID0+ICgoeyAnPCc6ICcmbHQ7JywgJz4nOiAnJmd0OycsICcmJzogJyZhbXA7JywgJ1wiJzogJyZxdW90OycgfSBhcyBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+KVtjXSksXG4gIC8vICAgKTtcbiAgLy8gfSlcbiAgLnJlcGxhY2UoL1s8JlwiXS9nLCBjID0+ICh7XG4gICAgJzwnOiAnJmx0OycsXG4gICAgJz4nOiAnJmd0OycsXG4gICAgJyYnOiAnJmFtcDsnLFxuICAgICdcIic6ICcmcXVvdDsnXG4gIH0pW2NdKTtcbn1cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1hcmtkb3duKHByb3BzKSB7XG4gIHZhciB7XG4gICAgcHJlZml4Q2xzXG4gIH0gPSBwcm9wcztcbiAgdmFyIHtcbiAgICBtYXJrZG93biA9ICcnLFxuICAgIGhpZ2hsaWdodEVuYWJsZSxcbiAgICBkaXNwYXRjaFxuICB9ID0gdXNlQ29udGV4dChFZGl0b3JDb250ZXh0KTtcbiAgdmFyIHByZVJlZiA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVSZWYoKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocHJlUmVmLmN1cnJlbnQgJiYgZGlzcGF0Y2gpIHtcbiAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgdGV4dGFyZWFQcmU6IHByZVJlZi5jdXJyZW50XG4gICAgICB9KTtcbiAgICB9XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICB9LCBbXSk7XG4gIGlmICghbWFya2Rvd24pIHtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL19qc3goXCJwcmVcIiwge1xuICAgICAgcmVmOiBwcmVSZWYsXG4gICAgICBjbGFzc05hbWU6IHByZWZpeENscyArIFwiLXRleHQtcHJlIHdtZGUtbWFya2Rvd24tY29sb3JcIlxuICAgIH0pO1xuICB9XG4gIHZhciBtZFN0ciA9IFwiPHByZSBjbGFzcz1cXFwibGFuZ3VhZ2UtbWFya2Rvd24gXCIgKyBwcmVmaXhDbHMgKyBcIi10ZXh0LXByZSB3bWRlLW1hcmtkb3duLWNvbG9yXFxcIj48Y29kZSBjbGFzcz1cXFwibGFuZ3VhZ2UtbWFya2Rvd25cXFwiPlwiICsgaHRtbDJFc2NhcGUoU3RyaW5nLnJhdyhfdGVtcGxhdGVPYmplY3QgfHwgKF90ZW1wbGF0ZU9iamVjdCA9IF90YWdnZWRUZW1wbGF0ZUxpdGVyYWxMb29zZShbXCJcIiwgXCJcIl0pKSwgbWFya2Rvd24pKSArIFwiXFxuPC9jb2RlPjwvcHJlPlwiO1xuICBpZiAoaGlnaGxpZ2h0RW5hYmxlKSB7XG4gICAgdHJ5IHtcbiAgICAgIG1kU3RyID0gcmVoeXBlKCkuZGF0YSgnc2V0dGluZ3MnLCB7XG4gICAgICAgIGZyYWdtZW50OiB0cnVlXG4gICAgICB9KVxuICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3Vpd2pzL3JlYWN0LW1kLWVkaXRvci9pc3N1ZXMvNTkzXG4gICAgICAvLyBAdHMtaWdub3JlXG4gICAgICAudXNlKHJlaHlwZVByaXNtLCB7XG4gICAgICAgIGlnbm9yZU1pc3Npbmc6IHRydWVcbiAgICAgIH0pLnByb2Nlc3NTeW5jKG1kU3RyKS50b1N0cmluZygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7fVxuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudCgnZGl2Jywge1xuICAgIGNsYXNzTmFtZTogJ3dtZGUtbWFya2Rvd24tY29sb3InLFxuICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICBfX2h0bWw6IG1kU3RyIHx8ICcnXG4gICAgfVxuICB9KTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/Markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/Textarea.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/TextArea/Textarea.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../commands */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js\");\n/* harmony import */ var _handleKeyDown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./handleKeyDown */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/handleKeyDown.js\");\n/* harmony import */ var _shortcuts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shortcuts */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/shortcuts.js\");\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./index.css */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__);\n\n\nvar _excluded = [\"prefixCls\", \"onChange\"],\n  _excluded2 = [\"markdown\", \"commands\", \"fullscreen\", \"preview\", \"highlightEnable\", \"extraCommands\", \"tabSize\", \"defaultTabEnable\", \"dispatch\"];\n\n\n\n\n\n\n\nfunction Textarea(props) {\n  var {\n      prefixCls,\n      onChange: _onChange\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(props, _excluded);\n  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_Context__WEBPACK_IMPORTED_MODULE_3__.EditorContext),\n    {\n      markdown,\n      commands,\n      fullscreen,\n      preview,\n      highlightEnable,\n      extraCommands,\n      tabSize,\n      defaultTabEnable,\n      dispatch\n    } = _useContext,\n    otherStore = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(_useContext, _excluded2);\n  var textRef = react__WEBPACK_IMPORTED_MODULE_2___default().useRef(null);\n  var executeRef = react__WEBPACK_IMPORTED_MODULE_2___default().useRef();\n  var statesRef = react__WEBPACK_IMPORTED_MODULE_2___default().useRef({\n    fullscreen,\n    preview\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    statesRef.current = {\n      fullscreen,\n      preview,\n      highlightEnable\n    };\n  }, [fullscreen, preview, highlightEnable]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    if (textRef.current && dispatch) {\n      var commandOrchestrator = new _commands__WEBPACK_IMPORTED_MODULE_4__.TextAreaCommandOrchestrator(textRef.current);\n      executeRef.current = commandOrchestrator;\n      dispatch({\n        textarea: textRef.current,\n        commandOrchestrator\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var onKeyDown = e => {\n    (0,_handleKeyDown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(e, tabSize, defaultTabEnable);\n    (0,_shortcuts__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(e, [...(commands || []), ...(extraCommands || [])], executeRef.current, dispatch, statesRef.current);\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    if (textRef.current) {\n      textRef.current.addEventListener('keydown', onKeyDown);\n    }\n    return () => {\n      if (textRef.current) {\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        textRef.current.removeEventListener('keydown', onKeyDown);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(\"textarea\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n    autoComplete: \"off\",\n    autoCorrect: \"off\",\n    autoCapitalize: \"off\",\n    spellCheck: false\n  }, other, {\n    ref: textRef,\n    className: prefixCls + \"-text-input \" + (other.className ? other.className : ''),\n    value: markdown,\n    onChange: e => {\n      dispatch && dispatch({\n        markdown: e.target.value\n      });\n      _onChange && _onChange(e);\n    }\n  }));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/Textarea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/handleKeyDown.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/TextArea/handleKeyDown.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handleKeyDown)\n/* harmony export */ });\n/* harmony import */ var _utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/InsertTextAtPosition */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/InsertTextAtPosition.js\");\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../commands */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js\");\n\n\n\n\n/**\n * - `13` - `Enter`\n * - `9` - `Tab`\n */\nfunction stopPropagation(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}\nfunction handleLineMove(e, direction) {\n  stopPropagation(e);\n  var target = e.target;\n  var textArea = new _commands__WEBPACK_IMPORTED_MODULE_2__.TextAreaTextApi(target);\n  var selection = {\n    start: target.selectionStart,\n    end: target.selectionEnd\n  };\n  selection = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectLine)({\n    text: target.value,\n    selection\n  });\n  if (direction < 0 && selection.start <= 0 || direction > 0 && selection.end >= target.value.length) {\n    return;\n  }\n  var blockText = target.value.slice(selection.start, selection.end);\n  if (direction < 0) {\n    var prevLineSelection = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectLine)({\n      text: target.value,\n      selection: {\n        start: selection.start - 1,\n        end: selection.start - 1\n      }\n    });\n    var prevLineText = target.value.slice(prevLineSelection.start, prevLineSelection.end);\n    textArea.setSelectionRange({\n      start: prevLineSelection.start,\n      end: selection.end\n    });\n    (0,_utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_0__.insertTextAtPosition)(target, blockText + \"\\n\" + prevLineText);\n    textArea.setSelectionRange({\n      start: prevLineSelection.start,\n      end: prevLineSelection.start + blockText.length\n    });\n  } else {\n    var nextLineSelection = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectLine)({\n      text: target.value,\n      selection: {\n        start: selection.end + 1,\n        end: selection.end + 1\n      }\n    });\n    var nextLineText = target.value.slice(nextLineSelection.start, nextLineSelection.end);\n    textArea.setSelectionRange({\n      start: selection.start,\n      end: nextLineSelection.end\n    });\n    (0,_utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_0__.insertTextAtPosition)(target, nextLineText + \"\\n\" + blockText);\n    textArea.setSelectionRange({\n      start: nextLineSelection.end - blockText.length,\n      end: nextLineSelection.end\n    });\n  }\n}\nfunction handleKeyDown(e, tabSize, defaultTabEnable) {\n  if (tabSize === void 0) {\n    tabSize = 2;\n  }\n  if (defaultTabEnable === void 0) {\n    defaultTabEnable = false;\n  }\n  var target = e.target;\n  var starVal = target.value.substr(0, target.selectionStart);\n  var valArr = starVal.split('\\n');\n  var currentLineStr = valArr[valArr.length - 1];\n  var textArea = new _commands__WEBPACK_IMPORTED_MODULE_2__.TextAreaTextApi(target);\n\n  /**\n   * `9` - `Tab`\n   */\n  if (!defaultTabEnable && e.code && e.code.toLowerCase() === 'tab') {\n    stopPropagation(e);\n    var space = new Array(tabSize + 1).join('  ');\n    if (target.selectionStart !== target.selectionEnd) {\n      var _star = target.value.substring(0, target.selectionStart).split('\\n');\n      var _end = target.value.substring(0, target.selectionEnd).split('\\n');\n      var modifiedTextLine = [];\n      _end.forEach((item, idx) => {\n        if (item !== _star[idx]) {\n          modifiedTextLine.push(item);\n        }\n      });\n      var modifiedText = modifiedTextLine.join('\\n');\n      var oldSelectText = target.value.substring(target.selectionStart, target.selectionEnd);\n      var newStarNum = target.value.substring(0, target.selectionStart).length;\n      textArea.setSelectionRange({\n        start: target.value.indexOf(modifiedText),\n        end: target.selectionEnd\n      });\n      var modifiedTextObj = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.insertBeforeEachLine)(modifiedText, e.shiftKey ? '' : space);\n      var text = modifiedTextObj.modifiedText;\n      if (e.shiftKey) {\n        text = text.split('\\n').map(item => item.replace(new RegExp(\"^\" + space), '')).join('\\n');\n      }\n      textArea.replaceSelection(text);\n      var startTabSize = e.shiftKey ? -tabSize : tabSize;\n      var endTabSize = e.shiftKey ? -modifiedTextLine.length * tabSize : modifiedTextLine.length * tabSize;\n      textArea.setSelectionRange({\n        start: newStarNum + startTabSize,\n        end: newStarNum + oldSelectText.length + endTabSize\n      });\n    } else {\n      return (0,_utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_0__.insertTextAtPosition)(target, space);\n    }\n  } else if (e.keyCode === 13 && e.code.toLowerCase() === 'enter' && (/^(-|\\*)\\s/.test(currentLineStr) || /^\\d+.\\s/.test(currentLineStr)) && !e.shiftKey) {\n    /**\n     * `13` - `Enter`\n     */\n    stopPropagation(e);\n    var startStr = '\\n- ';\n    if (currentLineStr.startsWith('*')) {\n      startStr = '\\n* ';\n    }\n    if (currentLineStr.startsWith('- [ ]') || currentLineStr.startsWith('- [X]') || currentLineStr.startsWith('- [x]')) {\n      startStr = '\\n- [ ] ';\n    }\n    if (/^\\d+.\\s/.test(currentLineStr)) {\n      startStr = \"\\n\" + (parseInt(currentLineStr) + 1) + \". \";\n    }\n    return (0,_utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_0__.insertTextAtPosition)(target, startStr);\n  } else if (e.code && e.code.toLowerCase() === 'keyd' && e.ctrlKey) {\n    // Duplicate lines\n    stopPropagation(e);\n    var selection = {\n      start: target.selectionStart,\n      end: target.selectionEnd\n    };\n    var savedSelection = selection;\n    selection = (0,_utils_markdownUtils__WEBPACK_IMPORTED_MODULE_1__.selectLine)({\n      text: target.value,\n      selection\n    });\n    var textToDuplicate = target.value.slice(selection.start, selection.end);\n    textArea.setSelectionRange({\n      start: selection.end,\n      end: selection.end\n    });\n    (0,_utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_0__.insertTextAtPosition)(target, \"\\n\" + textToDuplicate);\n    textArea.setSelectionRange({\n      start: savedSelection.start,\n      end: savedSelection.end\n    });\n  } else if (e.code && e.code.toLowerCase() === 'arrowup' && e.altKey) {\n    handleLineMove(e, -1);\n  } else if (e.code && e.code.toLowerCase() === 'arrowdown' && e.altKey) {\n    handleLineMove(e, 1);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/handleKeyDown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextArea)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var _shortcuts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shortcuts */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/shortcuts.js\");\n/* harmony import */ var _Markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Markdown */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/Markdown.js\");\n/* harmony import */ var _Textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Textarea */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/Textarea.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../commands */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js\");\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./index.css */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__);\n\n\nvar _excluded = [\"prefixCls\", \"className\", \"onScroll\", \"renderTextarea\"];\n\n\n\n\n\n\n\n\nfunction TextArea(props) {\n  var _ref = props || {},\n    {\n      prefixCls,\n      className,\n      onScroll,\n      renderTextarea\n    } = _ref,\n    otherProps = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(_ref, _excluded);\n  var {\n    markdown,\n    scrollTop,\n    commands,\n    minHeight,\n    highlightEnable,\n    extraCommands,\n    dispatch\n  } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_Context__WEBPACK_IMPORTED_MODULE_3__.EditorContext);\n  var textRef = react__WEBPACK_IMPORTED_MODULE_2___default().useRef(null);\n  var executeRef = react__WEBPACK_IMPORTED_MODULE_2___default().useRef();\n  var warp = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createRef();\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    var state = {};\n    if (warp.current) {\n      state.textareaWarp = warp.current || undefined;\n      warp.current.scrollTop = scrollTop || 0;\n    }\n    if (dispatch) {\n      dispatch(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, state));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    if (textRef.current && dispatch) {\n      var commandOrchestrator = new _commands__WEBPACK_IMPORTED_MODULE_7__.TextAreaCommandOrchestrator(textRef.current);\n      executeRef.current = commandOrchestrator;\n      dispatch({\n        textarea: textRef.current,\n        commandOrchestrator\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var textStyle = highlightEnable ? {} : {\n    WebkitTextFillColor: 'initial',\n    overflow: 'auto'\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"div\", {\n    ref: warp,\n    className: prefixCls + \"-area \" + (className || ''),\n    onScroll: onScroll,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"div\", {\n      className: prefixCls + \"-text\",\n      style: {\n        minHeight\n      },\n      children: renderTextarea ? (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().cloneElement(renderTextarea(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, otherProps, {\n        value: markdown,\n        autoComplete: 'off',\n        autoCorrect: 'off',\n        spellCheck: 'false',\n        autoCapitalize: 'off',\n        className: prefixCls + \"-text-input\",\n        style: {\n          WebkitTextFillColor: 'inherit',\n          overflow: 'auto'\n        }\n      }), {\n        dispatch,\n        onChange: otherProps.onChange,\n        shortcuts: _shortcuts__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        useContext: {\n          commands,\n          extraCommands,\n          commandOrchestrator: executeRef.current\n        }\n      }), {\n        ref: textRef\n      })) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [highlightEnable && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_Markdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n          prefixCls: prefixCls\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_Textarea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n          prefixCls: prefixCls\n        }, otherProps, {\n          style: textStyle\n        }))]\n      })\n    })\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/shortcuts.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/TextArea/shortcuts.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ shortcutsHandle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction getCommands(data, resulte) {\n  if (data === void 0) {\n    data = [];\n  }\n  if (resulte === void 0) {\n    resulte = {};\n  }\n  data.forEach(item => {\n    if (item.children && Array.isArray(item.children)) {\n      resulte = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, resulte, getCommands(item.children || []));\n    } else if (item.keyCommand && item.shortcuts && item.execute) {\n      resulte[item.shortcuts.toLocaleLowerCase()] = item;\n    }\n  });\n  return resulte;\n}\nfunction shortcutsHandle(e, commands, commandOrchestrator, dispatch, state) {\n  if (commands === void 0) {\n    commands = [];\n  }\n  var data = getCommands(commands || []);\n  var shortcuts = [];\n  if (e.altKey) {\n    shortcuts.push('alt');\n  }\n  if (e.shiftKey) {\n    shortcuts.push('shift');\n  }\n  if (e.metaKey) {\n    shortcuts.push('cmd');\n  }\n  if (e.ctrlKey) {\n    shortcuts.push('ctrl');\n  }\n  if (shortcuts.length > 0 && !/(control|alt|meta|shift)/.test(e.key.toLocaleLowerCase())) {\n    shortcuts.push(e.key.toLocaleLowerCase());\n  }\n  if (/escape/.test(e.key.toLocaleLowerCase())) {\n    shortcuts.push('escape');\n  }\n  if (shortcuts.length < 1) {\n    return;\n  }\n  var equal = !!data[shortcuts.join('+')];\n  var command = equal ? data[shortcuts.join('+')] : undefined;\n  Object.keys(data).forEach(item => {\n    var isequal = item.split('+').every(v => {\n      if (/ctrlcmd/.test(v)) {\n        return shortcuts.includes('ctrl') || shortcuts.includes('cmd');\n      }\n      return shortcuts.includes(v);\n    });\n    if (isequal) {\n      command = data[item];\n    }\n  });\n  if (command && commandOrchestrator) {\n    e.stopPropagation();\n    e.preventDefault();\n    commandOrchestrator.executeCommand(command, dispatch, state, shortcuts);\n    return;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/shortcuts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Child)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Child_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Child.css */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css\");\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.js\");\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nfunction Child(props) {\n  var {\n    prefixCls,\n    groupName,\n    commands,\n    children\n  } = props || {};\n  var {\n    barPopup = {}\n  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Context__WEBPACK_IMPORTED_MODULE_4__.EditorContext);\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"div\", {\n    className: prefixCls + \"-toolbar-child \" + (groupName && barPopup[groupName] ? 'active' : ''),\n    onClick: e => e.stopPropagation(),\n    children: Array.isArray(commands) ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(___WEBPACK_IMPORTED_MODULE_3__[\"default\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      commands: commands\n    }, props, {\n      isChild: true\n    })) : children\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [commands, barPopup, groupName, prefixCls]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVG9vbGJhci9DaGlsZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXNEO0FBQ0g7QUFDOUI7QUFDSTtBQUNxQjtBQUNFO0FBQ2pDO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSSxFQUFFLGlEQUFVLENBQUMsbURBQWE7QUFDOUIsU0FBUyw4Q0FBTyxvQkFBb0Isc0RBQUk7QUFDeEM7QUFDQTtBQUNBLHFEQUFxRCxzREFBSSxDQUFDLHlDQUFPLEVBQUUscUVBQVE7QUFDM0U7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vY29tcG9uZW50cy9Ub29sYmFyL0NoaWxkLmpzPzBhNDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2V4dGVuZHNcIjtcbmltcG9ydCBSZWFjdCwgeyB1c2VDb250ZXh0LCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IFwiLi9DaGlsZC5jc3NcIjtcbmltcG9ydCBUb29sYmFyIGZyb20gJy4vJztcbmltcG9ydCB7IEVkaXRvckNvbnRleHQgfSBmcm9tICcuLi8uLi9Db250ZXh0JztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGlsZChwcm9wcykge1xuICB2YXIge1xuICAgIHByZWZpeENscyxcbiAgICBncm91cE5hbWUsXG4gICAgY29tbWFuZHMsXG4gICAgY2hpbGRyZW5cbiAgfSA9IHByb3BzIHx8IHt9O1xuICB2YXIge1xuICAgIGJhclBvcHVwID0ge31cbiAgfSA9IHVzZUNvbnRleHQoRWRpdG9yQ29udGV4dCk7XG4gIHJldHVybiB1c2VNZW1vKCgpID0+IC8qI19fUFVSRV9fKi9fanN4KFwiZGl2XCIsIHtcbiAgICBjbGFzc05hbWU6IHByZWZpeENscyArIFwiLXRvb2xiYXItY2hpbGQgXCIgKyAoZ3JvdXBOYW1lICYmIGJhclBvcHVwW2dyb3VwTmFtZV0gPyAnYWN0aXZlJyA6ICcnKSxcbiAgICBvbkNsaWNrOiBlID0+IGUuc3RvcFByb3BhZ2F0aW9uKCksXG4gICAgY2hpbGRyZW46IEFycmF5LmlzQXJyYXkoY29tbWFuZHMpID8gLyojX19QVVJFX18qL19qc3goVG9vbGJhciwgX2V4dGVuZHMoe1xuICAgICAgY29tbWFuZHM6IGNvbW1hbmRzXG4gICAgfSwgcHJvcHMsIHtcbiAgICAgIGlzQ2hpbGQ6IHRydWVcbiAgICB9KSkgOiBjaGlsZHJlblxuICB9KSxcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICBbY29tbWFuZHMsIGJhclBvcHVwLCBncm91cE5hbWUsIHByZWZpeENsc10pO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolbarItems: () => (/* binding */ ToolbarItems),\n/* harmony export */   ToolbarVisibility: () => (/* binding */ ToolbarVisibility),\n/* harmony export */   \"default\": () => (/* binding */ Toolbar)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var _Child__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Child */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.js\");\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.css */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.css\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nfunction ToolbarItems(props) {\n  var {\n    prefixCls,\n    overflow\n  } = props;\n  var {\n    fullscreen,\n    preview,\n    barPopup = {},\n    components,\n    commandOrchestrator,\n    dispatch\n  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Context__WEBPACK_IMPORTED_MODULE_2__.EditorContext);\n  var originalOverflow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n  function handleClick(command, name) {\n    if (!dispatch) return;\n    var state = {\n      barPopup: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, barPopup)\n    };\n    if (command.keyCommand === 'preview') {\n      state.preview = command.value;\n    }\n    if (command.keyCommand === 'fullscreen') {\n      state.fullscreen = !fullscreen;\n    }\n    if (props.commands && command.keyCommand === 'group') {\n      props.commands.forEach(item => {\n        if (name === item.groupName) {\n          state.barPopup[name] = true;\n        } else if (item.keyCommand) {\n          state.barPopup[item.groupName] = false;\n        }\n      });\n    } else if (name || command.parent) {\n      Object.keys(state.barPopup || {}).forEach(keyName => {\n        state.barPopup[keyName] = false;\n      });\n    }\n    if (Object.keys(state).length) {\n      dispatch(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, state));\n    }\n    commandOrchestrator && commandOrchestrator.executeCommand(command);\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (document && overflow) {\n      if (fullscreen) {\n        // prevent scroll on fullscreen\n        document.body.style.overflow = 'hidden';\n      } else {\n        // get the original overflow only the first time\n        if (!originalOverflow.current) {\n          originalOverflow.current = window.getComputedStyle(document.body, null).overflow;\n        }\n        // reset to the original overflow\n        document.body.style.overflow = originalOverflow.current;\n      }\n    }\n  }, [fullscreen, originalOverflow, overflow]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"ul\", {\n    children: (props.commands || []).map((item, idx) => {\n      if (item.keyCommand === 'divider') {\n        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"li\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, item.liProps, {\n          className: prefixCls + \"-toolbar-divider\"\n        }), idx);\n      }\n      if (!item.keyCommand) return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, idx);\n      var activeBtn = fullscreen && item.keyCommand === 'fullscreen' || item.keyCommand === 'preview' && preview === item.value;\n      var childNode = item.children && typeof item.children === 'function' ? item.children({\n        getState: () => commandOrchestrator.getState(),\n        textApi: commandOrchestrator ? commandOrchestrator.textApi : undefined,\n        close: () => handleClick({}, item.groupName),\n        execute: () => handleClick({\n          execute: item.execute\n        }),\n        dispatch\n      }) : undefined;\n      var disabled = barPopup && preview && preview === 'preview' && !/(preview|fullscreen)/.test(item.keyCommand);\n      var render = (components == null ? void 0 : components.toolbar) || item.render;\n      var com = render && typeof render === 'function' ? render(item, !!disabled, handleClick, idx) : null;\n      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"li\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, item.liProps, {\n        className: activeBtn ? \"active\" : '',\n        children: [com && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(com) && com, !com && !item.buttonProps && item.icon, !com && item.buttonProps && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement('button', _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n          type: 'button',\n          key: idx,\n          disabled,\n          'data-name': item.name\n        }, item.buttonProps, {\n          onClick: evn => {\n            evn.stopPropagation();\n            handleClick(item, item.groupName);\n          }\n        }), item.icon), item.children && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Child__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n          overflow: overflow,\n          groupName: item.groupName,\n          prefixCls: prefixCls,\n          children: childNode,\n          commands: Array.isArray(item.children) ? item.children : undefined\n        })]\n      }), idx);\n    })\n  });\n}\nfunction Toolbar(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var {\n    prefixCls,\n    isChild,\n    className\n  } = props;\n  var {\n    commands,\n    extraCommands\n  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Context__WEBPACK_IMPORTED_MODULE_2__.EditorContext);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"div\", {\n    className: prefixCls + \"-toolbar \" + className,\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ToolbarItems, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, props, {\n      commands: props.commands || commands || []\n    })), !isChild && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ToolbarItems, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, props, {\n      commands: extraCommands || []\n    }))]\n  });\n}\nfunction ToolbarVisibility(props) {\n  var {\n    hideToolbar,\n    toolbarBottom,\n    placement,\n    overflow,\n    prefixCls\n  } = props;\n  if (hideToolbar || placement === 'bottom' && !toolbarBottom || placement === 'top' && toolbarBottom) {\n    return null;\n  }\n  var cls = toolbarBottom ? 'bottom' : '';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Toolbar, {\n    prefixCls: prefixCls,\n    overflow: overflow,\n    className: cls\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorContext: () => (/* reexport safe */ _Context__WEBPACK_IMPORTED_MODULE_6__.EditorContext),\n/* harmony export */   MarkdownUtil: () => (/* reexport module object */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   TextAreaCommandOrchestrator: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.TextAreaCommandOrchestrator),\n/* harmony export */   TextAreaTextApi: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.TextAreaTextApi),\n/* harmony export */   bold: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.bold),\n/* harmony export */   checkedListCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.checkedListCommand),\n/* harmony export */   code: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.code),\n/* harmony export */   codeBlock: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.codeBlock),\n/* harmony export */   codeEdit: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.codeEdit),\n/* harmony export */   codeLive: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.codeLive),\n/* harmony export */   codePreview: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.codePreview),\n/* harmony export */   commands: () => (/* reexport module object */ _commands__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   comment: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.comment),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   divider: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.divider),\n/* harmony export */   executeCommand: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.executeCommand),\n/* harmony export */   fullscreen: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.fullscreen),\n/* harmony export */   getBreaksNeededForEmptyLineAfter: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.getBreaksNeededForEmptyLineAfter),\n/* harmony export */   getBreaksNeededForEmptyLineBefore: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.getBreaksNeededForEmptyLineBefore),\n/* harmony export */   getCommands: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.getCommands),\n/* harmony export */   getExtraCommands: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.getExtraCommands),\n/* harmony export */   getStateFromTextArea: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.getStateFromTextArea),\n/* harmony export */   getSurroundingWord: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.getSurroundingWord),\n/* harmony export */   group: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.group),\n/* harmony export */   help: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.help),\n/* harmony export */   hr: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.hr),\n/* harmony export */   image: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.image),\n/* harmony export */   insertBeforeEachLine: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.insertBeforeEachLine),\n/* harmony export */   insertTextAtPosition: () => (/* reexport safe */ _utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_5__.insertTextAtPosition),\n/* harmony export */   issue: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.issue),\n/* harmony export */   italic: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.italic),\n/* harmony export */   link: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.link),\n/* harmony export */   orderedListCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.orderedListCommand),\n/* harmony export */   quote: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.quote),\n/* harmony export */   reducer: () => (/* reexport safe */ _Context__WEBPACK_IMPORTED_MODULE_6__.reducer),\n/* harmony export */   selectLine: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.selectLine),\n/* harmony export */   selectWord: () => (/* reexport safe */ _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__.selectWord),\n/* harmony export */   strikethrough: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.strikethrough),\n/* harmony export */   table: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.table),\n/* harmony export */   title: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title),\n/* harmony export */   title1: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title1),\n/* harmony export */   title2: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title2),\n/* harmony export */   title3: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title3),\n/* harmony export */   title4: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title4),\n/* harmony export */   title5: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title5),\n/* harmony export */   title6: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.title6),\n/* harmony export */   unorderedListCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_1__.unorderedListCommand)\n/* harmony export */ });\n/* harmony import */ var _Editor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Editor */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Editor.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/index.js\");\n/* harmony import */ var _utils_markdownUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/markdownUtils */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\");\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.css */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/index.css\");\n/* harmony import */ var _commands_group__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./commands/group */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/commands/group.js\");\n/* harmony import */ var _utils_InsertTextAtPosition__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/InsertTextAtPosition */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/InsertTextAtPosition.js\");\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Context */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Context.js\");\n/* harmony import */ var _Types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Types */ \"(ssr)/./node_modules/@uiw/react-md-editor/esm/Types.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Editor__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDTztBQUNlO0FBQ2pDO0FBQ007QUFDTTtBQUNLO0FBQ087QUFDcEI7QUFDQztBQUNGO0FBQ1U7QUFDbEMsaUVBQWUsK0NBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2luZGV4LmpzP2ExOWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE1ERWRpdG9yIGZyb20gJy4vRWRpdG9yJztcbmltcG9ydCAqIGFzIGNvbW1hbmRzIGZyb20gJy4vY29tbWFuZHMnO1xuaW1wb3J0ICogYXMgTWFya2Rvd25VdGlsIGZyb20gJy4vdXRpbHMvbWFya2Rvd25VdGlscyc7XG5pbXBvcnQgXCIuL2luZGV4LmNzc1wiO1xuZXhwb3J0ICogZnJvbSAnLi9jb21tYW5kcyc7XG5leHBvcnQgKiBmcm9tICcuL2NvbW1hbmRzL2dyb3VwJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMvbWFya2Rvd25VdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3V0aWxzL0luc2VydFRleHRBdFBvc2l0aW9uJztcbmV4cG9ydCAqIGZyb20gJy4vRWRpdG9yJztcbmV4cG9ydCAqIGZyb20gJy4vQ29udGV4dCc7XG5leHBvcnQgKiBmcm9tICcuL1R5cGVzJztcbmV4cG9ydCB7IE1hcmtkb3duVXRpbCwgY29tbWFuZHMgfTtcbmV4cG9ydCBkZWZhdWx0IE1ERWRpdG9yOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/InsertTextAtPosition.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/utils/InsertTextAtPosition.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   insertTextAtPosition: () => (/* binding */ insertTextAtPosition)\n/* harmony export */ });\n/**\n * The MIT License\n * Copyright (c) 2018 Dmitriy Kubyshkin\n * Copied from https://github.com/grassator/insert-text-at-cursor\n */\n\nvar browserSupportsTextareaTextNodes;\n\n/**\n * @param {HTMLElement} input\n * @return {boolean}\n */\nfunction canManipulateViaTextNodes(input) {\n  if (input.nodeName !== 'TEXTAREA') {\n    return false;\n  }\n  if (typeof browserSupportsTextareaTextNodes === 'undefined') {\n    var textarea = document.createElement('textarea');\n    textarea.value = '1';\n    browserSupportsTextareaTextNodes = !!textarea.firstChild;\n  }\n  return browserSupportsTextareaTextNodes;\n}\n\n/**\n * @param {HTMLTextAreaElement|HTMLInputElement} input\n * @param {string} text\n * @returns {void}\n */\nfunction insertTextAtPosition(input, text) {\n  // Most of the used APIs only work with the field selected\n  input.focus();\n\n  // IE 8-10\n  if (document.selection) {\n    var ieRange = document.selection.createRange();\n    ieRange.text = text;\n\n    // Move cursor after the inserted text\n    ieRange.collapse(false /* to the end */);\n    ieRange.select();\n    return;\n  }\n\n  // Webkit + Edge\n  var isSuccess = false;\n  if (text !== '') {\n    isSuccess = document.execCommand && document.execCommand('insertText', false, text);\n  } else {\n    isSuccess = document.execCommand && document.execCommand('delete', false);\n  }\n  if (!isSuccess) {\n    var start = input.selectionStart;\n    var end = input.selectionEnd;\n    // Firefox (non-standard method)\n    if (typeof input.setRangeText === 'function') {\n      input.setRangeText(text);\n    } else {\n      // To make a change we just need a Range, not a Selection\n      var range = document.createRange();\n      var textNode = document.createTextNode(text);\n      if (canManipulateViaTextNodes(input)) {\n        var node = input.firstChild;\n\n        // If textarea is empty, just insert the text\n        if (!node) {\n          input.appendChild(textNode);\n        } else {\n          // Otherwise we need to find a nodes for start and end\n          var offset = 0;\n          var startNode = null;\n          var endNode = null;\n          while (node && (startNode === null || endNode === null)) {\n            var nodeLength = node.nodeValue.length;\n\n            // if start of the selection falls into current node\n            if (start >= offset && start <= offset + nodeLength) {\n              range.setStart(startNode = node, start - offset);\n            }\n\n            // if end of the selection falls into current node\n            if (end >= offset && end <= offset + nodeLength) {\n              range.setEnd(endNode = node, end - offset);\n            }\n            offset += nodeLength;\n            node = node.nextSibling;\n          }\n\n          // If there is some text selected, remove it as we should replace it\n          if (start !== end) {\n            range.deleteContents();\n          }\n        }\n      }\n\n      // If the node is a textarea and the range doesn't span outside the element\n      //\n      // Get the commonAncestorContainer of the selected range and test its type\n      // If the node is of type `#text` it means that we're still working with text nodes within our textarea element\n      // otherwise, if it's of type `#document` for example it means our selection spans outside the textarea.\n      if (canManipulateViaTextNodes(input) && range.commonAncestorContainer.nodeName === '#text') {\n        // Finally insert a new node. The browser will automatically split start and end nodes into two if necessary\n        range.insertNode(textNode);\n      } else {\n        // If the node is not a textarea or the range spans outside a textarea the only way is to replace the whole value\n        var value = input.value;\n        input.value = value.slice(0, start) + text + value.slice(end);\n      }\n    }\n\n    // Correct the cursor position to be at the end of the insertion\n    input.setSelectionRange(start + text.length, start + text.length);\n\n    // Notify any possible listeners of the change\n    var e = document.createEvent('UIEvent');\n    e.initEvent('input', true, false);\n    input.dispatchEvent(e);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/InsertTextAtPosition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeCommand: () => (/* binding */ executeCommand),\n/* harmony export */   getBreaksNeededForEmptyLineAfter: () => (/* binding */ getBreaksNeededForEmptyLineAfter),\n/* harmony export */   getBreaksNeededForEmptyLineBefore: () => (/* binding */ getBreaksNeededForEmptyLineBefore),\n/* harmony export */   getSurroundingWord: () => (/* binding */ getSurroundingWord),\n/* harmony export */   insertBeforeEachLine: () => (/* binding */ insertBeforeEachLine),\n/* harmony export */   selectLine: () => (/* binding */ selectLine),\n/* harmony export */   selectWord: () => (/* binding */ selectWord)\n/* harmony export */ });\nfunction selectWord(_ref) {\n  var {\n    text,\n    selection,\n    prefix,\n    suffix = prefix\n  } = _ref;\n  var result = selection;\n  if (text && text.length && selection.start === selection.end) {\n    result = getSurroundingWord(text, selection.start);\n  }\n  if (result.start >= prefix.length && result.end <= text.length - suffix.length) {\n    var selectedTextContext = text.slice(result.start - prefix.length, result.end + suffix.length);\n    if (selectedTextContext.startsWith(prefix) && selectedTextContext.endsWith(suffix)) {\n      return {\n        start: result.start - prefix.length,\n        end: result.end + suffix.length\n      };\n    }\n  }\n  return result;\n}\nfunction selectLine(_ref2) {\n  var {\n    text,\n    selection\n  } = _ref2;\n  var start = text.slice(0, selection.start).lastIndexOf('\\n') + 1;\n  var end = text.slice(selection.end).indexOf('\\n') + selection.end;\n  if (end === selection.end - 1) {\n    end = text.length;\n  }\n  return {\n    start,\n    end\n  };\n}\n\n/**\n *  Gets the number of line-breaks that would have to be inserted before the given 'startPosition'\n *  to make sure there's an empty line between 'startPosition' and the previous text\n */\nfunction getBreaksNeededForEmptyLineBefore(text, startPosition) {\n  if (text === void 0) {\n    text = '';\n  }\n  if (startPosition === 0) return 0;\n\n  // rules:\n  // - If we're in the first line, no breaks are needed\n  // - Otherwise there must be 2 breaks before the previous character. Depending on how many breaks exist already, we\n  //      may need to insert 0, 1 or 2 breaks\n\n  var neededBreaks = 2;\n  var isInFirstLine = true;\n  for (var i = startPosition - 1; i >= 0 && neededBreaks >= 0; i--) {\n    switch (text.charCodeAt(i)) {\n      case 32:\n        // blank space\n        continue;\n      case 10:\n        // line break\n        neededBreaks--;\n        isInFirstLine = false;\n        break;\n      default:\n        return neededBreaks;\n    }\n  }\n  return isInFirstLine ? 0 : neededBreaks;\n}\n\n/**\n *  Gets the number of line-breaks that would have to be inserted after the given 'startPosition'\n *  to make sure there's an empty line between 'startPosition' and the next text\n */\nfunction getBreaksNeededForEmptyLineAfter(text, startPosition) {\n  if (text === void 0) {\n    text = '';\n  }\n  if (startPosition === text.length - 1) return 0;\n\n  // rules:\n  // - If we're in the first line, no breaks are needed\n  // - Otherwise there must be 2 breaks before the previous character. Depending on how many breaks exist already, we\n  //      may need to insert 0, 1 or 2 breaks\n\n  var neededBreaks = 2;\n  var isInLastLine = true;\n  for (var i = startPosition; i < text.length && neededBreaks >= 0; i++) {\n    switch (text.charCodeAt(i)) {\n      case 32:\n        continue;\n      case 10:\n        {\n          neededBreaks--;\n          isInLastLine = false;\n          break;\n        }\n      default:\n        return neededBreaks;\n    }\n  }\n  return isInLastLine ? 0 : neededBreaks;\n}\nfunction getSurroundingWord(text, position) {\n  if (!text) throw Error(\"Argument 'text' should be truthy\");\n  var isWordDelimiter = c => c === ' ' || c.charCodeAt(0) === 10;\n\n  // leftIndex is initialized to 0 because if selection is 0, it won't even enter the iteration\n  var start = 0;\n  // rightIndex is initialized to text.length because if selection is equal to text.length it won't even enter the interation\n  var end = text.length;\n\n  // iterate to the left\n  for (var i = position; i - 1 > -1; i--) {\n    if (isWordDelimiter(text[i - 1])) {\n      start = i;\n      break;\n    }\n  }\n\n  // iterate to the right\n  for (var _i = position; _i < text.length; _i++) {\n    if (isWordDelimiter(text[_i])) {\n      end = _i;\n      break;\n    }\n  }\n  return {\n    start,\n    end\n  };\n}\nfunction executeCommand(_ref3) {\n  var {\n    api,\n    selectedText,\n    selection,\n    prefix,\n    suffix = prefix\n  } = _ref3;\n  if (selectedText.length >= prefix.length + suffix.length && selectedText.startsWith(prefix) && selectedText.endsWith(suffix)) {\n    api.replaceSelection(selectedText.slice(prefix.length, suffix.length ? -suffix.length : undefined));\n    api.setSelectionRange({\n      start: selection.start - prefix.length,\n      end: selection.end - prefix.length\n    });\n  } else {\n    api.replaceSelection(\"\" + prefix + selectedText + suffix);\n    api.setSelectionRange({\n      start: selection.start + prefix.length,\n      end: selection.end + prefix.length\n    });\n  }\n}\n/**\n * Inserts insertionString before each line\n */\nfunction insertBeforeEachLine(selectedText, insertBefore) {\n  var lines = selectedText.split(/\\n/);\n  var insertionLength = 0;\n  var modifiedText = lines.map((item, index) => {\n    if (typeof insertBefore === 'string') {\n      if (item.startsWith(insertBefore)) {\n        insertionLength -= insertBefore.length;\n        return item.slice(insertBefore.length);\n      }\n      insertionLength += insertBefore.length;\n      return insertBefore + item;\n    }\n    if (typeof insertBefore === 'function') {\n      if (item.startsWith(insertBefore(item, index))) {\n        insertionLength -= insertBefore(item, index).length;\n        return item.slice(insertBefore(item, index).length);\n      }\n      var insertionResult = insertBefore(item, index);\n      insertionLength += insertionResult.length;\n      return insertBefore(item, index) + item;\n    }\n    throw Error('insertion is expected to be either a string or a function');\n  }).join('\\n');\n  return {\n    modifiedText,\n    insertionLength\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/utils/markdownUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-markdown-preview/esm/styles/markdown.css":
/*!**************************************************************************!*\
  !*** ./node_modules/@uiw/react-markdown-preview/esm/styles/markdown.css ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"596318594b5d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tYXJrZG93bi1wcmV2aWV3L2VzbS9zdHlsZXMvbWFya2Rvd24uY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL0B1aXcvcmVhY3QtbWFya2Rvd24tcHJldmlldy9lc20vc3R5bGVzL21hcmtkb3duLmNzcz81ZWNiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTk2MzE4NTk0YjVkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-markdown-preview/esm/styles/markdown.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.css":
/*!****************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.css ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"afdf4f1c6785\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvRHJhZ0Jhci9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvRHJhZ0Jhci9pbmRleC5jc3M/YWVlMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFmZGY0ZjFjNjc4NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/DragBar/index.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css":
/*!*****************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dc38b404b913\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVGV4dEFyZWEvaW5kZXguY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL0B1aXcvcmVhY3QtbWQtZWRpdG9yL2VzbS9jb21wb25lbnRzL1RleHRBcmVhL2luZGV4LmNzcz81ODA5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGMzOGI0MDRiOTEzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css":
/*!****************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3366e29f3fdb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVG9vbGJhci9DaGlsZC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVG9vbGJhci9DaGlsZC5jc3M/NWY3MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzNjZlMjlmM2ZkYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.css":
/*!****************************************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.css ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"35306b7de932\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVG9vbGJhci9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2NvbXBvbmVudHMvVG9vbGJhci9pbmRleC5jc3M/ZDlmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM1MzA2YjdkZTkzMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-md-editor/esm/index.css":
/*!*********************************************************!*\
  !*** ./node_modules/@uiw/react-md-editor/esm/index.css ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8acd97f27a2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1tZC1lZGl0b3IvZXNtL2luZGV4LmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LW1kLWVkaXRvci9lc20vaW5kZXguY3NzPzE1NGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlOGFjZDk3ZjI3YTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-md-editor/esm/index.css\n");

/***/ })

};
;