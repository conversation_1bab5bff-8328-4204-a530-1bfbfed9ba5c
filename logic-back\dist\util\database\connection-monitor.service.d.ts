import { OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class ConnectionMonitorService implements OnModuleInit {
    private readonly dataSource;
    private readonly logger;
    constructor(dataSource: DataSource);
    onModuleInit(): Promise<void>;
    checkConnectionStatus(): Promise<void>;
    cleanupIdleConnections(): Promise<void>;
    getConnectionPoolStatus(): Promise<{
        totalConnections: any;
        freeConnections: any;
        acquiringConnections: any;
        connectionLimit: any;
        queueLimit: any;
        isConnected: boolean;
    }>;
    forceReleaseIdleConnections(): Promise<{
        releasedConnections: any;
    }>;
}
