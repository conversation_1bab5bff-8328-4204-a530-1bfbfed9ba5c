import { VoiceprintFeatureService } from './voiceprint_feature.service';
import { CreateVoiceprintFeatureDto } from './dto/create-voiceprint-feature.dto';
import { UpdateVoiceprintFeatureDto } from './dto/update-voiceprint-feature.dto';
export declare class VoiceprintFeatureController {
    private readonly voiceprintFeatureService;
    constructor(voiceprintFeatureService: VoiceprintFeatureService);
    create(createVoiceprintFeatureDto: CreateVoiceprintFeatureDto): Promise<import("./entities/voiceprint-feature.entity").VoiceprintFeature>;
    findAll(): Promise<import("./entities/voiceprint-feature.entity").VoiceprintFeature[]>;
    findByUserId(userId: string): Promise<import("./entities/voiceprint-feature.entity").VoiceprintFeature[]>;
    findByGroupId(groupId: string): Promise<import("./entities/voiceprint-feature.entity").VoiceprintFeature[]>;
    findOne(id: string): Promise<import("./entities/voiceprint-feature.entity").VoiceprintFeature>;
    update(id: string, updateVoiceprintFeatureDto: UpdateVoiceprintFeatureDto): Promise<import("./entities/voiceprint-feature.entity").VoiceprintFeature>;
    remove(id: string): Promise<void>;
}
