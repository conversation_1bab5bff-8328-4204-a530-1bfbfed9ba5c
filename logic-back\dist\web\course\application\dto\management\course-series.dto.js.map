{"version": 3, "file": "course-series.dto.js", "sourceRoot": "", "sources": ["../../../../../../src/web/course/application/dto/management/course-series.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAAmE;AACnE,qDAAgG;AAEhG,MAAa,qBAAqB;IAOhC,KAAK,CAAS;IAQd,WAAW,CAAU;IAQrB,UAAU,CAAU;IAWpB,QAAQ,CAAU;IAQlB,cAAc,CAAU;IAQxB,MAAM,CAAY;CACnB;AAnDD,sDAmDC;AA5CC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,eAAe;KACzB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;oDACtB;AAQd;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0DACQ;AAQrB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,WAAW;QACxB,OAAO,EAAE,sCAAsC;KAChD,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;yDACO;AAWpB;IATC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,4BAAU,GAAE;;uDACK;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,aAAa;KACvB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6DACW;AAQxB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACnB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;qDACK;AAGpB,MAAa,qBAAqB;IAOhC,KAAK,CAAU;IAQf,WAAW,CAAU;IAQrB,UAAU,CAAU;IAUpB,QAAQ,CAAU;IAQlB,cAAc,CAAU;IAQxB,MAAM,CAAY;CACnB;AAlDD,sDAkDC;AA3CC;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,iBAAiB;KAC3B,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;oDACE;AAQf;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,gCAAgC;KAC1C,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0DACQ;AAQrB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,WAAW;QACxB,OAAO,EAAE,+CAA+C;KACzD,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;yDACO;AAUpB;IARC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,4BAAU,GAAE;;uDACK;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,iBAAiB;KAC3B,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6DACW;AAQxB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACnB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;qDACK;AAGpB,MAAa,uBAAuB;IAKlC,EAAE,CAAS;IAMX,KAAK,CAAS;IAOd,WAAW,CAAS;IAOpB,UAAU,CAAS;IAMnB,QAAQ,CAAS;IAMjB,MAAM,CAAS;IAOf,cAAc,CAAS;IAMvB,YAAY,CAAS;IAMrB,aAAa,CAAS;IAOtB,SAAS,CAAS;IAMlB,SAAS,CAAO;IAMhB,SAAS,CAAO;CACjB;AA5ED,0DA4EC;AAvEC;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,CAAC;KACX,CAAC;;mDACS;AAMX;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,eAAe;KACzB,CAAC;;sDACY;AAOd;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,sBAAsB;KAChC,CAAC;;4DACkB;AAOpB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,sCAAsC;KAChD,CAAC;;2DACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,CAAC;KACX,CAAC;;yDACe;AAMjB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE,CAAC;KACX,CAAC;;uDACa;AAOf;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,aAAa;KACvB,CAAC;;+DACqB;AAMvB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC;KACX,CAAC;;6DACmB;AAMrB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,OAAO;QACpB,OAAO,EAAE,CAAC;KACX,CAAC;;8DACoB;AAOtB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,OAAO;QACpB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,GAAG;KACb,CAAC;;0DACgB;AAMlB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,sBAAsB;KAChC,CAAC;8BACS,IAAI;0DAAC;AAMhB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,sBAAsB;KAChC,CAAC;8BACS,IAAI;0DAAC"}