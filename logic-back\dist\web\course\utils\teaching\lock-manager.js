"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LockManager_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LockManager = void 0;
const common_1 = require("@nestjs/common");
const teaching_exceptions_1 = require("../../domain/exceptions/teaching/teaching.exceptions");
let LockManager = LockManager_1 = class LockManager {
    logger = new common_1.Logger(LockManager_1.name);
    locks = new Map();
    cleanupInterval;
    constructor() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredLocks();
        }, 30000);
    }
    async acquireLock(lockKey, ttl = 60000) {
        const now = Date.now();
        const existingLock = this.locks.get(lockKey);
        if (existingLock && (now - existingLock.timestamp) < existingLock.ttl) {
            this.logger.warn(`锁已被占用: ${lockKey}`);
            return false;
        }
        this.locks.set(lockKey, { timestamp: now, ttl });
        this.logger.debug(`成功获取锁: ${lockKey}, TTL: ${ttl}ms`);
        return true;
    }
    async releaseLock(lockKey) {
        const deleted = this.locks.delete(lockKey);
        if (deleted) {
            this.logger.debug(`成功释放锁: ${lockKey}`);
        }
        else {
            this.logger.warn(`尝试释放不存在的锁: ${lockKey}`);
        }
    }
    async withDistributedLock(lockKey, operation, ttl = 60000, maxRetries = 3, retryDelay = 1000) {
        let retries = 0;
        while (retries < maxRetries) {
            const lockAcquired = await this.acquireLock(lockKey, ttl);
            if (lockAcquired) {
                try {
                    this.logger.debug(`开始执行锁保护的操作: ${lockKey}`);
                    const result = await operation();
                    this.logger.debug(`锁保护的操作执行完成: ${lockKey}`);
                    return result;
                }
                finally {
                    await this.releaseLock(lockKey);
                }
            }
            retries++;
            if (retries < maxRetries) {
                this.logger.warn(`获取锁失败，${retryDelay}ms后重试 (${retries}/${maxRetries}): ${lockKey}`);
                await this.sleep(retryDelay);
            }
        }
        const [, courseId, classId] = lockKey.split(':');
        throw new teaching_exceptions_1.ConcurrencyConflictException(parseInt(courseId, 10), parseInt(classId, 10), lockKey);
    }
    cleanupExpiredLocks() {
        const now = Date.now();
        let cleanedCount = 0;
        for (const [key, lock] of this.locks.entries()) {
            if ((now - lock.timestamp) >= lock.ttl) {
                this.locks.delete(key);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            this.logger.debug(`清理了 ${cleanedCount} 个过期锁`);
        }
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    getLockStatus() {
        const now = Date.now();
        const status = {};
        for (const [key, lock] of this.locks.entries()) {
            status[key] = {
                age: now - lock.timestamp,
                ttl: lock.ttl
            };
        }
        return status;
    }
    onModuleDestroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.locks.clear();
        this.logger.debug('锁管理器已销毁');
    }
};
exports.LockManager = LockManager;
exports.LockManager = LockManager = LockManager_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], LockManager);
//# sourceMappingURL=lock-manager.js.map