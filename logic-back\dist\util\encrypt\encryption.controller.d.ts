import { HttpStatus } from '@nestjs/common';
import { EncryptionService } from './encryption.service';
import { RedisSessionService } from './session/redis-session.service';
import { Request } from 'express';
import { KeyManagementService } from './key-management/key-management.service';
interface SessionRequest {
    encryptedKey: string;
    keyId?: string;
}
export declare class EncryptionController {
    private readonly encryptionService;
    private readonly redisSessionService;
    private readonly keyManagementService;
    private readonly sessionRenewals;
    private readonly minRenewInterval;
    private readonly logger;
    constructor(encryptionService: EncryptionService, redisSessionService: RedisSessionService, keyManagementService: KeyManagementService);
    getPublicKey(): Promise<{
        success: boolean;
        keyId: string;
        publicKey: string;
        message?: undefined;
    } | {
        success: boolean;
        message: string;
        keyId?: undefined;
        publicKey?: undefined;
    }>;
    getKeyStatus(): Promise<{
        success: boolean;
        activeKey: {
            keyId: string;
            createdAt: Date;
            expiresAt: Date;
            fingerprint: string;
            algorithm: string;
            remainingDays: number;
        } | null;
        availableKeys: {
            keyId: string;
            status: string;
            createdAt: Date;
            expiresAt: Date;
            fingerprint: string;
            isActive: boolean;
        }[];
        statistics: {
            totalKeys: number;
            activeKeys: number;
            deprecatedKeys: number;
            expiredKeys: number;
        };
        message?: undefined;
    } | {
        success: boolean;
        message: string;
        activeKey?: undefined;
        availableKeys?: undefined;
        statistics?: undefined;
    }>;
    createSession(body: SessionRequest, headers: any, req: Request): Promise<{
        success: boolean;
        needsKeyUpdate: boolean | undefined;
        message: string;
        sessionId?: undefined;
        expiresIn?: undefined;
    } | {
        success: boolean;
        sessionId: string;
        needsKeyUpdate: boolean | undefined;
        expiresIn: number;
        message?: undefined;
    } | {
        success: boolean;
        message: string;
        needsKeyUpdate?: undefined;
        sessionId?: undefined;
        expiresIn?: undefined;
    }>;
    createSecureSession(body: SessionRequest, headers: any, req: Request): Promise<{
        success: boolean;
        needsKeyUpdate: boolean | undefined;
        message: string;
        sessionId?: undefined;
        expiresIn?: undefined;
    } | {
        success: boolean;
        sessionId: string;
        needsKeyUpdate: boolean | undefined;
        expiresIn: number;
        message?: undefined;
    } | {
        success: boolean;
        message: string;
        needsKeyUpdate?: undefined;
        sessionId?: undefined;
        expiresIn?: undefined;
    }>;
    private generateSessionId;
    renewSession(sessionId: string): Promise<{
        success: boolean;
        code: HttpStatus;
        message: string;
        data: {
            sessionId: string;
            expiresIn: number;
            sessionType: string;
            throttled: boolean;
            originalExpiresAt?: undefined;
            newExpiresAt?: undefined;
        };
    } | {
        success: boolean;
        code: HttpStatus;
        message: string;
        data?: undefined;
    } | {
        success: boolean;
        code: HttpStatus;
        message: string;
        data: {
            sessionId: string;
            expiresIn: number;
            sessionType: string;
            originalExpiresAt: string;
            newExpiresAt: string;
            throttled?: undefined;
        };
    }>;
    deleteSession(sessionId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getSessionStats(): Promise<{
        success: boolean;
        message: string;
    }>;
    cleanupSessions(): Promise<{
        success: boolean;
        message: string;
    }>;
    createSessionDebug(body: {
        aesKey: string;
        aesIv: string;
    }): Promise<{
        success: boolean;
        data: {
            sessionId: string;
            expiresIn: number;
        };
        message?: undefined;
    } | {
        success: boolean;
        message: string;
        data?: undefined;
    }>;
}
export {};
