import { ActivityTagService } from './activity_tag.service';
import { CreateActivityTagDto } from './dto/create-activity_tag.dto';
import { UpdateActivityTagDto } from './dto/update-activity_tag.dto';
import { ActivityTag } from './entities/activity_tag.entity';
export declare class ActivityTagController {
    private readonly activityTagService;
    constructor(activityTagService: ActivityTagService);
    create(createActivityTagDto: CreateActivityTagDto): Promise<ActivityTag>;
    findAll(): Promise<ActivityTag[]>;
    findByActivityId(activityId: string): Promise<ActivityTag[]>;
    findByTagId(tagId: string): Promise<ActivityTag[]>;
    removeByActivityId(activityId: string): Promise<void>;
    findOne(id: string): Promise<ActivityTag>;
    update(id: string, updateActivityTagDto: UpdateActivityTagDto): Promise<ActivityTag>;
    remove(id: string): Promise<void>;
    hardRemove(id: string): Promise<void>;
}
