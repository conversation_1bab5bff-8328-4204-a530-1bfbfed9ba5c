"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartialFailureException = exports.IncompleteSettingsException = exports.EmptyClassException = exports.InsufficientTeacherPermissionException = exports.InsufficientStudentPointsException = exports.CourseNotFoundOrNotPublishedException = exports.DuplicateOperationException = exports.ConcurrencyConflictException = void 0;
const common_1 = require("@nestjs/common");
class ConcurrencyConflictException extends common_1.HttpException {
    constructor(courseId, classId, lockKey) {
        const response = {
            code: 409,
            message: '系统繁忙，请稍后重试',
            data: {
                reason: '无法获取操作锁，可能有其他用户正在为此班级开启课程',
                courseId,
                classId,
                lockKey,
                retryAfter: 30
            }
        };
        super(response, common_1.HttpStatus.CONFLICT);
    }
}
exports.ConcurrencyConflictException = ConcurrencyConflictException;
class DuplicateOperationException extends common_1.HttpException {
    constructor(courseId, classId, teacherId, existingRecordId, lastExecutionTime) {
        const response = {
            code: 405,
            message: '该课程已在此班级开启，请勿重复操作',
            data: {
                reason: '检测到重复的一键上课操作',
                courseId,
                classId,
                teacherId,
                existingRecordId,
                lastExecutionTime
            }
        };
        super(response, common_1.HttpStatus.METHOD_NOT_ALLOWED);
    }
}
exports.DuplicateOperationException = DuplicateOperationException;
class CourseNotFoundOrNotPublishedException extends common_1.HttpException {
    constructor(courseId) {
        const response = {
            code: 500,
            message: '课程不存在或未发布',
            data: {
                courseId,
                reason: '指定的课程不存在或状态不是已发布'
            }
        };
        super(response, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.CourseNotFoundOrNotPublishedException = CourseNotFoundOrNotPublishedException;
class InsufficientStudentPointsException extends common_1.HttpException {
    constructor(studentId, currentPoints, requiredPoints, studentName) {
        const response = {
            code: 402,
            message: '学生积分不足，无法完成积分分配',
            data: {
                reason: '部分学生积分不足，无法完成一键上课操作',
                studentId,
                studentName: studentName || `学生${studentId}`,
                currentPoints,
                requiredPoints,
                shortfall: requiredPoints - currentPoints,
                suggestion: '请为学生充值积分后重试，或降低课程所需积分'
            }
        };
        super(response, common_1.HttpStatus.PAYMENT_REQUIRED);
    }
}
exports.InsufficientStudentPointsException = InsufficientStudentPointsException;
class InsufficientTeacherPermissionException extends common_1.HttpException {
    constructor(teacherId, classId, courseId, courseStatus, creatorId) {
        let reason = '您没有权限操作此班级';
        if (courseId && courseStatus !== undefined && creatorId !== undefined) {
            if (courseStatus === 0) {
                if (creatorId !== teacherId) {
                    reason = '该课程尚未发布，只有课程创建者可以开启教学';
                }
            }
            else if (courseStatus === 2) {
                reason = '该课程已归档，无法开启教学';
            }
        }
        const response = {
            code: 403,
            message: '权限不足',
            data: {
                reason,
                teacherId,
                classId,
                courseId,
                courseStatus,
                creatorId
            }
        };
        super(response, common_1.HttpStatus.FORBIDDEN);
    }
}
exports.InsufficientTeacherPermissionException = InsufficientTeacherPermissionException;
class EmptyClassException extends common_1.HttpException {
    constructor(classId, studentCount = 0) {
        const response = {
            code: 422,
            message: '业务逻辑错误',
            data: {
                reason: '班级中没有学生，无法开启课程',
                classId,
                studentCount
            }
        };
        super(response, common_1.HttpStatus.UNPROCESSABLE_ENTITY);
    }
}
exports.EmptyClassException = EmptyClassException;
class IncompleteSettingsException extends common_1.HttpException {
    constructor(courseId, missingSettings) {
        const response = {
            code: 422,
            message: '业务逻辑错误',
            data: {
                reason: '课程设置不完整，请先完成课程配置',
                courseId,
                missingSettings
            }
        };
        super(response, common_1.HttpStatus.UNPROCESSABLE_ENTITY);
    }
}
exports.IncompleteSettingsException = IncompleteSettingsException;
class PartialFailureException extends common_1.HttpException {
    constructor(data) {
        const response = {
            code: 206,
            message: '课程开启完成，但部分操作失败',
            data
        };
        super(response, common_1.HttpStatus.PARTIAL_CONTENT);
    }
}
exports.PartialFailureException = PartialFailureException;
//# sourceMappingURL=teaching.exceptions.js.map