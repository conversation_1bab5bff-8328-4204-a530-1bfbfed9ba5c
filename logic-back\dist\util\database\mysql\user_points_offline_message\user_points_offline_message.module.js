"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPointsOfflineMessageModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_points_offline_message_service_1 = require("./user_points_offline_message.service");
const user_points_offline_message_controller_1 = require("./user_points_offline_message.controller");
const user_points_offline_message_entity_1 = require("./entities/user_points_offline_message.entity");
let UserPointsOfflineMessageModule = class UserPointsOfflineMessageModule {
};
exports.UserPointsOfflineMessageModule = UserPointsOfflineMessageModule;
exports.UserPointsOfflineMessageModule = UserPointsOfflineMessageModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([user_points_offline_message_entity_1.UserPointsOfflineMessage])],
        controllers: [user_points_offline_message_controller_1.UserPointsOfflineMessageController],
        providers: [user_points_offline_message_service_1.UserPointsOfflineMessageService],
        exports: [user_points_offline_message_service_1.UserPointsOfflineMessageService],
    })
], UserPointsOfflineMessageModule);
//# sourceMappingURL=user_points_offline_message.module.js.map