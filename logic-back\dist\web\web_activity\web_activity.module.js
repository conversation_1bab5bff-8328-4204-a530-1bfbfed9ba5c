"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityModule = void 0;
const common_1 = require("@nestjs/common");
const web_activity_controller_1 = require("./web_activity.controller");
const web_activity_service_1 = require("./web_activity.service");
const activity_module_1 = require("../../util/database/mysql/activity/activity.module");
const activity_submit_module_1 = require("../../util/database/mysql/activity_submit/activity_submit.module");
const activity_events_task_module_1 = require("../../util/database/mysql/activity_events_task/activity_events_task.module");
const user_student_module_1 = require("../../util/database/mysql/user_student/user_student.module");
const user_school_module_1 = require("../../util/database/mysql/user_school/user_school.module");
const user_class_module_1 = require("../../util/database/mysql/user_class/user_class.module");
const user_info_module_1 = require("../../util/database/mysql/user_info/user_info.module");
const http_response_result_module_1 = require("../http_response_result/http_response_result.module");
const ali_oss_module_1 = require("../../util/ali_service/ali_oss/ali_oss.module");
let WebActivityModule = class WebActivityModule {
};
exports.WebActivityModule = WebActivityModule;
exports.WebActivityModule = WebActivityModule = __decorate([
    (0, common_1.Module)({
        imports: [
            activity_module_1.ActivityModule,
            activity_submit_module_1.ActivitySubmitModule,
            activity_events_task_module_1.ActivityEventsTaskModule,
            user_student_module_1.UserStudentModule,
            user_school_module_1.UserSchoolModule,
            user_class_module_1.UserClassModule,
            user_info_module_1.UserInfoModule,
            http_response_result_module_1.HttpResponseResultModule,
            ali_oss_module_1.AliOssModule,
        ],
        controllers: [web_activity_controller_1.WebActivityController],
        providers: [web_activity_service_1.WebActivityService],
        exports: [web_activity_service_1.WebActivityService],
    })
], WebActivityModule);
//# sourceMappingURL=web_activity.module.js.map