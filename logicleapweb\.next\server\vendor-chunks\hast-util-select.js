"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-select";
exports.ids = ["vendor-chunks/hast-util-select"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-select/lib/attribute.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-select/lib/attribute.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attribute: () => (/* binding */ attribute)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @import {AstAttribute} from 'css-selector-parser'\n * @import {Element, Properties} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n\n\n\n\n\n/**\n * @param {AstAttribute} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {Schema} schema\n *   Schema of element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction attribute(query, element, schema) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_0__.find)(schema, query.name)\n  const propertyValue = element.properties[info.property]\n  let value = normalizeValue(propertyValue, info)\n\n  // Exists.\n  if (!query.value) {\n    return value !== undefined\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(query.value.type === 'String', 'expected plain string')\n  let key = query.value.value\n\n  // Case-sensitivity.\n  if (query.caseSensitivityModifier === 'i') {\n    key = key.toLowerCase()\n\n    if (value) {\n      value = value.toLowerCase()\n    }\n  }\n\n  if (value !== undefined) {\n    switch (query.operator) {\n      // Exact.\n      case '=': {\n        return key === value\n      }\n\n      // Ends.\n      case '$=': {\n        return key === value.slice(-key.length)\n      }\n\n      // Contains.\n      case '*=': {\n        return value.includes(key)\n      }\n\n      // Begins.\n      case '^=': {\n        return key === value.slice(0, key.length)\n      }\n\n      // Exact or prefix.\n      case '|=': {\n        return (\n          key === value ||\n          (key === value.slice(0, key.length) &&\n            value.charAt(key.length) === '-')\n        )\n      }\n\n      // Space-separated list.\n      case '~=': {\n        return (\n          // For all other values (including comma-separated lists), return whether this\n          // is an exact match.\n          key === value ||\n          // If this is a space-separated list, and the query is contained in it, return\n          // true.\n          space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse(value).includes(key)\n        )\n      }\n      // Other values are not yet supported by CSS.\n      // No default\n    }\n  }\n\n  return false\n}\n\n/**\n *\n * @param {Properties[keyof Properties]} value\n * @param {Info} info\n * @returns {string | undefined}\n */\nfunction normalizeValue(value, info) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'boolean') {\n    if (value) {\n      return info.attribute\n    }\n  } else if (Array.isArray(value)) {\n    if (value.length > 0) {\n      return (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.stringify)(value)\n    }\n  } else {\n    return String(value)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/attribute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/class-name.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-select/lib/class-name.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   className: () => (/* binding */ className)\n/* harmony export */ });\n/**\n * @import {AstClassName} from 'css-selector-parser'\n * @import {Element} from 'hast'\n */\n\n/** @type {Array<never>} */\nconst emptyClassNames = []\n\n/**\n * Check whether an element has all class names.\n *\n * @param {AstClassName} query\n *   AST rule (with `classNames`).\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction className(query, element) {\n  // Assume array.\n  const value = /** @type {Readonly<Array<string>>} */ (\n    element.properties.className || emptyClassNames\n  )\n\n  return value.includes(query.name)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvY2xhc3MtbmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGNBQWM7QUFDMUIsWUFBWSxTQUFTO0FBQ3JCOztBQUVBLFdBQVcsY0FBYztBQUN6Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekI7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSwyQkFBMkIseUJBQXlCO0FBQ3BEO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtc2VsZWN0L2xpYi9jbGFzcy1uYW1lLmpzP2YzN2YiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtBc3RDbGFzc05hbWV9IGZyb20gJ2Nzcy1zZWxlY3Rvci1wYXJzZXInXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICovXG5cbi8qKiBAdHlwZSB7QXJyYXk8bmV2ZXI+fSAqL1xuY29uc3QgZW1wdHlDbGFzc05hbWVzID0gW11cblxuLyoqXG4gKiBDaGVjayB3aGV0aGVyIGFuIGVsZW1lbnQgaGFzIGFsbCBjbGFzcyBuYW1lcy5cbiAqXG4gKiBAcGFyYW0ge0FzdENsYXNzTmFtZX0gcXVlcnlcbiAqICAgQVNUIHJ1bGUgKHdpdGggYGNsYXNzTmFtZXNgKS5cbiAqIEBwYXJhbSB7RWxlbWVudH0gZWxlbWVudFxuICogICBFbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYGVsZW1lbnRgIG1hdGNoZXMgYHF1ZXJ5YC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNsYXNzTmFtZShxdWVyeSwgZWxlbWVudCkge1xuICAvLyBBc3N1bWUgYXJyYXkuXG4gIGNvbnN0IHZhbHVlID0gLyoqIEB0eXBlIHtSZWFkb25seTxBcnJheTxzdHJpbmc+Pn0gKi8gKFxuICAgIGVsZW1lbnQucHJvcGVydGllcy5jbGFzc05hbWUgfHwgZW1wdHlDbGFzc05hbWVzXG4gIClcblxuICByZXR1cm4gdmFsdWUuaW5jbHVkZXMocXVlcnkubmFtZSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/class-name.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/enter-state.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-select/lib/enter-state.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enterState: () => (/* binding */ enterState)\n/* harmony export */ });\n/* harmony import */ var direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! direction */ \"(ssr)/./node_modules/direction/index.js\");\n/* harmony import */ var hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-string */ \"(ssr)/./node_modules/hast-util-to-string/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {Visitor} from 'unist-util-visit'\n * @import {ElementContent, Nodes} from 'hast'\n * @import {Direction, State} from './index.js'\n */\n\n\n\n\n\n\n/**\n * Enter a node.\n *\n * The caller is responsible for calling the return value `exit`.\n *\n * @param {State} state\n *   Current state.\n *\n *   Will be mutated: `exit` undos the changes.\n * @param {Nodes} node\n *   Node to enter.\n * @returns {() => undefined}\n *   Call to exit.\n */\n// eslint-disable-next-line complexity\nfunction enterState(state, node) {\n  const schema = state.schema\n  const language = state.language\n  const currentDirection = state.direction\n  const editableOrEditingHost = state.editableOrEditingHost\n  /** @type {Direction | undefined} */\n  let directionInferred\n\n  if (node.type === 'element') {\n    const lang = node.properties.xmlLang || node.properties.lang\n    const type = node.properties.type || 'text'\n    const direction = directionProperty(node)\n\n    if (lang !== null && lang !== undefined) {\n      state.language = String(lang)\n    }\n\n    if (schema && schema.space === 'html') {\n      if (node.properties.contentEditable === 'true') {\n        state.editableOrEditingHost = true\n      }\n\n      if (node.tagName === 'svg') {\n        state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n      }\n\n      // See: <https://html.spec.whatwg.org/#the-directionality>.\n      // Explicit `[dir=rtl]`.\n      if (direction === 'rtl') {\n        directionInferred = direction\n      } else if (\n        // Explicit `[dir=ltr]`.\n        direction === 'ltr' ||\n        // HTML with an invalid or no `[dir]`.\n        (direction !== 'auto' && node.tagName === 'html') ||\n        // `input[type=tel]` with an invalid or no `[dir]`.\n        (direction !== 'auto' && node.tagName === 'input' && type === 'tel')\n      ) {\n        directionInferred = 'ltr'\n        // `[dir=auto]` or `bdi` with an invalid or no `[dir]`.\n      } else if (direction === 'auto' || node.tagName === 'bdi') {\n        if (node.tagName === 'textarea') {\n          // Check contents of `<textarea>`.\n          directionInferred = directionBidi((0,hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__.toString)(node))\n        } else if (\n          node.tagName === 'input' &&\n          (type === 'email' ||\n            type === 'search' ||\n            type === 'tel' ||\n            type === 'text')\n        ) {\n          // Check value of `<input>`.\n          directionInferred = node.properties.value\n            ? directionBidi(String(node.properties.value))\n            : 'ltr'\n        } else {\n          // Check text nodes in `node`.\n          (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(node, inferDirectionality)\n        }\n      }\n\n      if (directionInferred) {\n        state.direction = directionInferred\n      }\n    }\n    // Turn off editing mode in non-HTML spaces.\n    else if (state.editableOrEditingHost) {\n      state.editableOrEditingHost = false\n    }\n  }\n\n  return reset\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function reset() {\n    state.schema = schema\n    state.language = language\n    state.direction = currentDirection\n    state.editableOrEditingHost = editableOrEditingHost\n  }\n\n  /** @type {Visitor<ElementContent>} */\n  function inferDirectionality(child) {\n    if (child.type === 'text') {\n      directionInferred = directionBidi(child.value)\n      return directionInferred ? unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.EXIT : undefined\n    }\n\n    if (\n      child !== node &&\n      child.type === 'element' &&\n      (child.tagName === 'bdi' ||\n        child.tagName === 'script' ||\n        child.tagName === 'style' ||\n        child.tagName === 'textare' ||\n        directionProperty(child))\n    ) {\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.SKIP\n    }\n  }\n}\n\n/**\n * See `wooorm/direction`.\n *\n * @param {string} value\n *   Value to check.\n * @returns {Exclude<Direction, 'auto'> | undefined}\n *   Directionality.\n */\nfunction directionBidi(value) {\n  const result = (0,direction__WEBPACK_IMPORTED_MODULE_4__.direction)(value)\n  return result === 'neutral' ? undefined : result\n}\n\n/**\n * @param {ElementContent} node\n *   Node to check.\n * @returns {Direction | undefined}\n *   Directionality.\n */\nfunction directionProperty(node) {\n  const value =\n    node.type === 'element' && typeof node.properties.dir === 'string'\n      ? node.properties.dir.toLowerCase()\n      : undefined\n\n  return value === 'auto' || value === 'ltr' || value === 'rtl'\n    ? value\n    : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/enter-state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/id.js":
/*!*************************************************!*\
  !*** ./node_modules/hast-util-select/lib/id.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   id: () => (/* binding */ id)\n/* harmony export */ });\n/**\n * @import {AstId} from 'css-selector-parser'\n * @import {Element} from 'hast'\n */\n\n/**\n * Check whether an element has an ID.\n *\n * @param {AstId} query\n *   AST rule (with `ids`).\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction id(query, element) {\n  return element.properties.id === query.name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvaWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtc2VsZWN0L2xpYi9pZC5qcz9iMjNjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QXN0SWR9IGZyb20gJ2Nzcy1zZWxlY3Rvci1wYXJzZXInXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICovXG5cbi8qKlxuICogQ2hlY2sgd2hldGhlciBhbiBlbGVtZW50IGhhcyBhbiBJRC5cbiAqXG4gKiBAcGFyYW0ge0FzdElkfSBxdWVyeVxuICogICBBU1QgcnVsZSAod2l0aCBgaWRzYCkuXG4gKiBAcGFyYW0ge0VsZW1lbnR9IGVsZW1lbnRcbiAqICAgRWxlbWVudC5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIGBlbGVtZW50YCBtYXRjaGVzIGBxdWVyeWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpZChxdWVyeSwgZWxlbWVudCkge1xuICByZXR1cm4gZWxlbWVudC5wcm9wZXJ0aWVzLmlkID09PSBxdWVyeS5uYW1lXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/id.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-select/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectAll: () => (/* binding */ selectAll)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/hast-util-select/lib/parse.js\");\n/* harmony import */ var _walk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./walk.js */ \"(ssr)/./node_modules/hast-util-select/lib/walk.js\");\n/**\n * @import {AstSelector} from 'css-selector-parser'\n * @import {Element, Nodes, RootContent} from 'hast'\n * @import {Schema} from 'property-information'\n */\n\n/**\n * @typedef {'html' | 'svg'} Space\n *   Name of namespace.\n *\n * @typedef {'auto' | 'ltr' | 'rtl'} Direction\n *   Direction.\n *\n * @typedef State\n *   Current state.\n * @property {Direction} direction\n *   Current direction.\n * @property {boolean} editableOrEditingHost\n *   Whether we’re in `contentEditable`.\n * @property {number | undefined} elementCount\n *   Track siblings: there are `n` siblings.\n * @property {number | undefined} elementIndex\n *   Track siblings: this current element has `n` elements before it.\n * @property {boolean} found\n *   Whether we found at least one match.\n * @property {string | undefined} language\n *   Current language.\n * @property {boolean} one\n *   Whether we can stop looking after we found one element.\n * @property {Array<Element>} results\n *   Matches.\n * @property {AstSelector} rootQuery\n *   Original root selectors.\n * @property {Schema} schema\n *   Current schema.\n * @property {Array<RootContent>} scopeElements\n *   Elements in scope.\n * @property {boolean} shallow\n *   Whether we only allow selectors without nesting.\n * @property {number | undefined} typeCount\n *   Track siblings: there are `n` siblings with this element’s tag name.\n * @property {number | undefined} typeIndex\n *   Track siblings: this current element has `n` elements with its tag name\n *   before it.\n */\n\n\n\n\n\n/**\n * Check that the given `node` matches `selector`.\n *\n * This only checks the element itself, not the surrounding tree.\n * Thus, nesting in selectors is not supported (`p b`, `p > b`), neither are\n * selectors like `:first-child`, etc.\n * This only checks that the given element matches the selector.\n *\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [node]\n *   Node that might match `selector`, should be an element (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {boolean}\n *   Whether `node` matches `selector`.\n */\nfunction matches(selector, node, space) {\n  const state = createState(selector, node, space)\n  state.one = true\n  state.shallow = true\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_0__.walk)(state, node || undefined)\n  return state.results.length > 0\n}\n\n/**\n * Select the first element that matches `selector` in the given `tree`.\n * Searches the tree in *preorder*.\n *\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [tree]\n *   Tree to search (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {Element | undefined}\n *   First element in `tree` that matches `selector` or `undefined` if nothing\n *   is found; this could be `tree` itself.\n */\nfunction select(selector, tree, space) {\n  const state = createState(selector, tree, space)\n  state.one = true\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_0__.walk)(state, tree || undefined)\n  return state.results[0]\n}\n\n/**\n * Select all elements that match `selector` in the given `tree`.\n * Searches the tree in *preorder*.\n *\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [tree]\n *   Tree to search (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {Array<Element>}\n *   Elements in `tree` that match `selector`.\n *   This could include `tree` itself.\n */\nfunction selectAll(selector, tree, space) {\n  const state = createState(selector, tree, space)\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_0__.walk)(state, tree || undefined)\n  return state.results\n}\n\n/**\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [tree]\n *   Tree to search (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {State} State\n *   State.\n */\nfunction createState(selector, tree, space) {\n  return {\n    direction: 'ltr',\n    editableOrEditingHost: false,\n    elementCount: undefined,\n    elementIndex: undefined,\n    found: false,\n    language: undefined,\n    one: false,\n    // State of the query.\n    results: [],\n    rootQuery: (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.parse)(selector),\n    schema: space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_2__.svg : property_information__WEBPACK_IMPORTED_MODULE_2__.html,\n    scopeElements: tree ? (tree.type === 'root' ? tree.children : [tree]) : [],\n    shallow: false,\n    typeIndex: undefined,\n    typeCount: undefined\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/name.js":
/*!***************************************************!*\
  !*** ./node_modules/hast-util-select/lib/name.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   name: () => (/* binding */ name)\n/* harmony export */ });\n/**\n * @import {AstTagName} from 'css-selector-parser'\n * @import {Element} from 'hast'\n */\n\n/**\n * Check whether an element has a tag name.\n *\n * @param {AstTagName} query\n *   AST rule (with `tag`).\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction name(query, element) {\n  return query.name === element.tagName\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvbmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLFlBQVk7QUFDeEIsWUFBWSxTQUFTO0FBQ3JCOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsWUFBWTtBQUN2QjtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1zZWxlY3QvbGliL25hbWUuanM/OTAyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0FzdFRhZ05hbWV9IGZyb20gJ2Nzcy1zZWxlY3Rvci1wYXJzZXInXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICovXG5cbi8qKlxuICogQ2hlY2sgd2hldGhlciBhbiBlbGVtZW50IGhhcyBhIHRhZyBuYW1lLlxuICpcbiAqIEBwYXJhbSB7QXN0VGFnTmFtZX0gcXVlcnlcbiAqICAgQVNUIHJ1bGUgKHdpdGggYHRhZ2ApLlxuICogQHBhcmFtIHtFbGVtZW50fSBlbGVtZW50XG4gKiAgIEVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciBgZWxlbWVudGAgbWF0Y2hlcyBgcXVlcnlgLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbmFtZShxdWVyeSwgZWxlbWVudCkge1xuICByZXR1cm4gcXVlcnkubmFtZSA9PT0gZWxlbWVudC50YWdOYW1lXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/name.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/parse.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-select/lib/parse.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var css_selector_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! css-selector-parser */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/index.js\");\n/**\n * @import {AstSelector} from 'css-selector-parser'\n */\n\n\n\nconst cssSelectorParse = (0,css_selector_parser__WEBPACK_IMPORTED_MODULE_0__.createParser)({syntax: 'selectors-4'})\n\n/**\n * @param {string} selector\n *   Selector to parse.\n * @returns {AstSelector}\n *   Parsed selector.\n */\nfunction parse(selector) {\n  if (typeof selector !== 'string') {\n    throw new TypeError('Expected `string` as selector, not `' + selector + '`')\n  }\n\n  return cssSelectorParse(selector)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvcGFyc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6Qjs7QUFFZ0Q7O0FBRWhELHlCQUF5QixpRUFBWSxFQUFFLHNCQUFzQjs7QUFFN0Q7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvcGFyc2UuanM/ODU4NSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0FzdFNlbGVjdG9yfSBmcm9tICdjc3Mtc2VsZWN0b3ItcGFyc2VyJ1xuICovXG5cbmltcG9ydCB7Y3JlYXRlUGFyc2VyfSBmcm9tICdjc3Mtc2VsZWN0b3ItcGFyc2VyJ1xuXG5jb25zdCBjc3NTZWxlY3RvclBhcnNlID0gY3JlYXRlUGFyc2VyKHtzeW50YXg6ICdzZWxlY3RvcnMtNCd9KVxuXG4vKipcbiAqIEBwYXJhbSB7c3RyaW5nfSBzZWxlY3RvclxuICogICBTZWxlY3RvciB0byBwYXJzZS5cbiAqIEByZXR1cm5zIHtBc3RTZWxlY3Rvcn1cbiAqICAgUGFyc2VkIHNlbGVjdG9yLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyc2Uoc2VsZWN0b3IpIHtcbiAgaWYgKHR5cGVvZiBzZWxlY3RvciAhPT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdFeHBlY3RlZCBgc3RyaW5nYCBhcyBzZWxlY3Rvciwgbm90IGAnICsgc2VsZWN0b3IgKyAnYCcpXG4gIH1cblxuICByZXR1cm4gY3NzU2VsZWN0b3JQYXJzZShzZWxlY3Rvcilcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/pseudo.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-select/lib/pseudo.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pseudo: () => (/* binding */ pseudo)\n/* harmony export */ });\n/* harmony import */ var bcp_47_match__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! bcp-47-match */ \"(ssr)/./node_modules/bcp-47-match/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var nth_check__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nth-check */ \"(ssr)/./node_modules/nth-check/lib/esm/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _walk_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./walk.js */ \"(ssr)/./node_modules/hast-util-select/lib/walk.js\");\n/**\n * @import {AstPseudoClass} from 'css-selector-parser'\n * @import {default as NthCheck} from 'nth-check'\n * @import {ElementContent, Element, Parents} from 'hast'\n * @import {State} from './index.js'\n */\n\n\n\n\n\n\n\n\n\n\n/** @type {NthCheck} */\n// @ts-expect-error: types are broken.\nconst nthCheck = nth_check__WEBPACK_IMPORTED_MODULE_0__[\"default\"][\"default\"] || nth_check__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n\n/** @type {(rule: AstPseudoClass, element: Element, index: number | undefined, parent: Parents | undefined, state: State) => boolean} */\nconst pseudo = (0,zwitch__WEBPACK_IMPORTED_MODULE_1__.zwitch)('name', {\n  handlers: {\n    'any-link': anyLink,\n    blank,\n    checked,\n    dir,\n    disabled,\n    empty,\n    enabled,\n    'first-child': firstChild,\n    'first-of-type': firstOfType,\n    has,\n    is,\n    lang,\n    'last-child': lastChild,\n    'last-of-type': lastOfType,\n    not,\n    'nth-child': nthChild,\n    'nth-last-child': nthLastChild,\n    'nth-last-of-type': nthLastOfType,\n    'nth-of-type': nthOfType,\n    'only-child': onlyChild,\n    'only-of-type': onlyOfType,\n    optional,\n    'read-only': readOnly,\n    'read-write': readWrite,\n    required,\n    root,\n    scope\n  },\n  invalid: invalidPseudo,\n  unknown: unknownPseudo\n})\n\n/**\n * Check whether an element matches an `:any-link` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction anyLink(_, element) {\n  return (\n    (element.tagName === 'a' ||\n      element.tagName === 'area' ||\n      element.tagName === 'link') &&\n    (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'href')\n  )\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {AstPseudoClass} query\n *   Query.\n */\nfunction assertDeep(state, query) {\n  if (state.shallow) {\n    throw new Error('Cannot use `:' + query.name + '` without parent')\n  }\n}\n\n/**\n * Check whether an element matches a `:blank` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction blank(_, element) {\n  return !someChildren(element, check)\n\n  /**\n   * @param {ElementContent} child\n   * @returns {boolean}\n   */\n  function check(child) {\n    return (\n      child.type === 'element' || (child.type === 'text' && !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(child))\n    )\n  }\n}\n\n/**\n * Check whether an element matches a `:checked` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction checked(_, element) {\n  if (element.tagName === 'input' || element.tagName === 'menuitem') {\n    return Boolean(\n      (element.properties.type === 'checkbox' ||\n        element.properties.type === 'radio') &&\n        (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'checked')\n    )\n  }\n\n  if (element.tagName === 'option') {\n    return (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'selected')\n  }\n\n  return false\n}\n\n/**\n * Check whether an element matches a `:dir()` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\n// eslint-disable-next-line unicorn/prevent-abbreviations\nfunction dir(query, _1, _2, _3, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'String', 'expected plain text')\n  return state.direction === query.argument.value\n}\n\n/**\n * Check whether an element matches a `:disabled` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction disabled(_, element) {\n  return (\n    (element.tagName === 'button' ||\n      element.tagName === 'input' ||\n      element.tagName === 'select' ||\n      element.tagName === 'textarea' ||\n      element.tagName === 'optgroup' ||\n      element.tagName === 'option' ||\n      element.tagName === 'menuitem' ||\n      element.tagName === 'fieldset') &&\n    (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'disabled')\n  )\n}\n\n/**\n * Check whether an element matches an `:empty` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction empty(_, element) {\n  return !someChildren(element, check)\n\n  /**\n   * @param {ElementContent} child\n   * @returns {boolean}\n   */\n  function check(child) {\n    return child.type === 'element' || child.type === 'text'\n  }\n}\n\n/**\n * Check whether an element matches an `:enabled` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction enabled(query, element) {\n  return !disabled(query, element)\n}\n\n/**\n * Check whether an element matches a `:first-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction firstChild(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.elementIndex === 0\n}\n\n/**\n * Check whether an element matches a `:first-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction firstOfType(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.typeIndex === 0\n}\n\n/**\n * @param {AstPseudoClass} query\n *   Query.\n * @returns {(value: number) => boolean}\n *   N.\n */\nfunction getCachedNthCheck(query) {\n  /** @type {(value: number) => boolean} */\n  // @ts-expect-error: cache.\n  let cachedFunction = query._cachedFn\n\n  if (!cachedFunction) {\n    const value = query.argument\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(value, 'expected `argument`')\n\n    if (value.type !== 'Formula') {\n      throw new Error(\n        'Expected `nth` formula, such as `even` or `2n+1` (`of` is not yet supported)'\n      )\n    }\n\n    cachedFunction = nthCheck(value.a + 'n+' + value.b)\n    // @ts-expect-error: cache.\n    query._cachedFn = cachedFunction\n  }\n\n  return cachedFunction\n}\n\n/**\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _1\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _2\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction has(query, element, _1, _2, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'Selector', 'expected selector')\n\n  /** @type {State} */\n  const childState = {\n    ...state,\n    // Not found yet.\n    found: false,\n    // One result is enough.\n    one: true,\n    results: [],\n    rootQuery: query.argument,\n    scopeElements: [element],\n    // Do walk deep.\n    shallow: false\n  }\n\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_5__.walk)(childState, {type: 'root', children: element.children})\n\n  return childState.results.length > 0\n}\n\n// Shouldn’t be called, parser gives correct data.\n/* c8 ignore next 3 */\nfunction invalidPseudo() {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.unreachable)('Invalid pseudo-selector')\n}\n\n/**\n * Check whether an element `:is` further selectors.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _1\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _2\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction is(query, element, _1, _2, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'Selector', 'expected selector')\n\n  /** @type {State} */\n  const childState = {\n    ...state,\n    // Not found yet.\n    found: false,\n    // One result is enough.\n    one: true,\n    results: [],\n    rootQuery: query.argument,\n    scopeElements: [element],\n    // Do walk deep.\n    shallow: false\n  }\n\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_5__.walk)(childState, element)\n\n  return childState.results[0] === element\n}\n\n/**\n * Check whether an element matches a `:lang()` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction lang(query, _1, _2, _3, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'String', 'expected string')\n\n  return (\n    state.language !== '' &&\n    state.language !== undefined &&\n    (0,bcp_47_match__WEBPACK_IMPORTED_MODULE_6__.extendedFilter)(state.language, (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.parse)(query.argument.value)).length > 0\n  )\n}\n\n/**\n * Check whether an element matches a `:last-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction lastChild(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return Boolean(\n    state.elementCount && state.elementIndex === state.elementCount - 1\n  )\n}\n\n/**\n * Check whether an element matches a `:last-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction lastOfType(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return (\n    typeof state.typeIndex === 'number' &&\n    typeof state.typeCount === 'number' &&\n    state.typeIndex === state.typeCount - 1\n  )\n}\n\n/**\n * Check whether an element does `:not` match further selectors.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} index\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction not(query, element, index, parent, state) {\n  return !is(query, element, index, parent, state)\n}\n\n/**\n * Check whether an element matches an `:nth-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthChild(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return (\n    typeof state.elementIndex === 'number' && cachedFunction(state.elementIndex)\n  )\n}\n\n/**\n * Check whether an element matches an `:nth-last-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthLastChild(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return Boolean(\n    typeof state.elementCount === 'number' &&\n      typeof state.elementIndex === 'number' &&\n      cachedFunction(state.elementCount - state.elementIndex - 1)\n  )\n}\n\n/**\n * Check whether an element matches a `:nth-last-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthLastOfType(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return (\n    typeof state.typeCount === 'number' &&\n    typeof state.typeIndex === 'number' &&\n    cachedFunction(state.typeCount - 1 - state.typeIndex)\n  )\n}\n\n/**\n * Check whether an element matches an `:nth-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthOfType(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return typeof state.typeIndex === 'number' && cachedFunction(state.typeIndex)\n}\n\n/**\n * Check whether an element matches an `:only-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction onlyChild(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.elementCount === 1\n}\n\n/**\n * Check whether an element matches an `:only-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction onlyOfType(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.typeCount === 1\n}\n\n/**\n * Check whether an element matches an `:optional` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction optional(query, element) {\n  return !required(query, element)\n}\n\n/**\n * Check whether an element matches a `:read-only` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} index\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction readOnly(query, element, index, parent, state) {\n  return !readWrite(query, element, index, parent, state)\n}\n\n/**\n * Check whether an element matches a `:read-write` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _1\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _2\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction readWrite(_, element, _1, _2, state) {\n  return element.tagName === 'input' || element.tagName === 'textarea'\n    ? !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'readOnly') && !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'disabled')\n    : Boolean(state.editableOrEditingHost)\n}\n\n/**\n * Check whether an element matches a `:required` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction required(_, element) {\n  return (\n    (element.tagName === 'input' ||\n      element.tagName === 'textarea' ||\n      element.tagName === 'select') &&\n    (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'required')\n  )\n}\n\n/**\n * Check whether an element matches a `:root` pseudo.\n *\n * @param {AstPseudoClass} _1\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction root(_1, element, _2, parent, state) {\n  return Boolean(\n    (!parent || parent.type === 'root') &&\n      state.schema &&\n      (state.schema.space === 'html' || state.schema.space === 'svg') &&\n      (element.tagName === 'html' || element.tagName === 'svg')\n  )\n}\n\n/**\n * Check whether an element matches a `:scope` pseudo.\n *\n * @param {AstPseudoClass} _1\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction scope(_1, element, _2, _3, state) {\n  return state.scopeElements.includes(element)\n}\n\n/**\n * Check children.\n *\n * @param {Element} element\n *   Element.\n * @param {(child: ElementContent) => boolean} check\n *   Check.\n * @returns {boolean}\n *   Whether a child of `element` matches `check`.\n */\nfunction someChildren(element, check) {\n  const children = element.children\n  let index = -1\n\n  while (++index < children.length) {\n    if (check(children[index])) return true\n  }\n\n  return false\n}\n\n/**\n * @param {unknown} query_\n *   Query-like value.\n * @returns {never}\n *   Nothing.\n * @throws\n *   Exception.\n */\nfunction unknownPseudo(query_) {\n  // Runtime JS guarantees it has a `name`.\n  const query = /** @type {AstPseudoClass} */ (query_)\n  throw new Error('Unknown pseudo-selector `' + query.name + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/pseudo.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/test.js":
/*!***************************************************!*\
  !*** ./node_modules/hast-util-select/lib/test.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   test: () => (/* binding */ test)\n/* harmony export */ });\n/* harmony import */ var _attribute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./attribute.js */ \"(ssr)/./node_modules/hast-util-select/lib/attribute.js\");\n/* harmony import */ var _class_name_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./class-name.js */ \"(ssr)/./node_modules/hast-util-select/lib/class-name.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./id.js */ \"(ssr)/./node_modules/hast-util-select/lib/id.js\");\n/* harmony import */ var _name_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./name.js */ \"(ssr)/./node_modules/hast-util-select/lib/name.js\");\n/* harmony import */ var _pseudo_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pseudo.js */ \"(ssr)/./node_modules/hast-util-select/lib/pseudo.js\");\n/**\n * @import {AstRule} from 'css-selector-parser'\n * @import {Element, Parents} from 'hast'\n * @import {State} from './index.js'\n */\n\n\n\n\n\n\n\n/**\n * Test a rule.\n *\n * @param {AstRule} query\n *   AST rule (with `pseudoClasses`).\n * @param {Element} element\n *   Element.\n * @param {number | undefined} index\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction test(query, element, index, parent, state) {\n  for (const item of query.items) {\n    // eslint-disable-next-line unicorn/prefer-switch\n    if (item.type === 'Attribute') {\n      if (!(0,_attribute_js__WEBPACK_IMPORTED_MODULE_0__.attribute)(item, element, state.schema)) return false\n    } else if (item.type === 'Id') {\n      if (!(0,_id_js__WEBPACK_IMPORTED_MODULE_1__.id)(item, element)) return false\n    } else if (item.type === 'ClassName') {\n      if (!(0,_class_name_js__WEBPACK_IMPORTED_MODULE_2__.className)(item, element)) return false\n    } else if (item.type === 'PseudoClass') {\n      if (!(0,_pseudo_js__WEBPACK_IMPORTED_MODULE_3__.pseudo)(item, element, index, parent, state)) return false\n    } else if (item.type === 'PseudoElement') {\n      throw new Error('Invalid selector: `::' + item.name + '`')\n    } else if (item.type === 'TagName') {\n      if (!(0,_name_js__WEBPACK_IMPORTED_MODULE_4__.name)(item, element)) return false\n    } else {\n      // Otherwise `item.type` is `WildcardTag`, which matches.\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/test.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/walk.js":
/*!***************************************************!*\
  !*** ./node_modules/hast-util-select/lib/walk.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walk: () => (/* binding */ walk)\n/* harmony export */ });\n/* harmony import */ var _enter_state_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enter-state.js */ \"(ssr)/./node_modules/hast-util-select/lib/enter-state.js\");\n/* harmony import */ var _test_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./test.js */ \"(ssr)/./node_modules/hast-util-select/lib/test.js\");\n/**\n * @import {AstRule} from 'css-selector-parser'\n * @import {Element, Nodes, Parents} from 'hast'\n * @import {State} from './index.js'\n */\n\n/**\n * @typedef Counts\n *   Info on elements in a parent.\n * @property {number} count\n *   Number of elements.\n * @property {Map<string, number>} types\n *   Number of elements by tag name.\n *\n * @typedef Nest\n *   Rule sets by nesting.\n * @property {Array<AstRule> | undefined} adjacentSibling\n *   `a + b`\n * @property {Array<AstRule> | undefined} descendant\n *   `a b`\n * @property {Array<AstRule> | undefined} directChild\n *   `a > b`\n * @property {Array<AstRule> | undefined} generalSibling\n *   `a ~ b`\n */\n\n\n\n\n/** @type {Array<never>} */\nconst empty = []\n\n/**\n * Walk a tree.\n *\n * @param {State} state\n *   State.\n * @param {Nodes | undefined} tree\n *   Tree.\n */\nfunction walk(state, tree) {\n  if (tree) {\n    one(state, [], tree, undefined, undefined, tree)\n  }\n}\n\n/**\n * Add a rule to a nesting map.\n *\n * @param {Nest} nest\n *   Nesting.\n * @param {keyof Nest} field\n *   Field.\n * @param {AstRule} rule\n *   Rule.\n */\nfunction add(nest, field, rule) {\n  const list = nest[field]\n  if (list) {\n    list.push(rule)\n  } else {\n    nest[field] = [rule]\n  }\n}\n\n/**\n * Check in a parent.\n *\n * @param {State} state\n *   State.\n * @param {Nest} nest\n *   Nesting.\n * @param {Parents} node\n *   Parent.\n * @param {Nodes} tree\n *   Tree.\n * @returns {undefined}\n *   Nothing.\n */\nfunction all(state, nest, node, tree) {\n  const fromParent = combine(nest.descendant, nest.directChild)\n  /** @type {Array<AstRule> | undefined} */\n  let fromSibling\n  let index = -1\n  /**\n   * Total counts.\n   * @type {Counts}\n   */\n  const total = {count: 0, types: new Map()}\n  /**\n   * Counts of previous siblings.\n   * @type {Counts}\n   */\n  const before = {count: 0, types: new Map()}\n\n  while (++index < node.children.length) {\n    count(total, node.children[index])\n  }\n\n  index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    // Uppercase to prevent prototype polution, injecting `constructor` or so.\n    // Normalize because HTML is insensitive.\n    const name =\n      child.type === 'element' ? child.tagName.toUpperCase() : undefined\n    // Before counting further elements:\n    state.elementIndex = before.count\n    state.typeIndex = name ? before.types.get(name) || 0 : 0\n    // After counting all elements.\n    state.elementCount = total.count\n    state.typeCount = name ? total.types.get(name) : 0\n\n    // Only apply if this is a parent, this should be an element, but we check\n    // for parents so that we delve into custom nodes too.\n    if ('children' in child) {\n      const forSibling = combine(fromParent, fromSibling)\n      const nest = one(\n        state,\n        forSibling,\n        node.children[index],\n        index,\n        node,\n        tree\n      )\n      fromSibling = combine(nest.generalSibling, nest.adjacentSibling)\n    }\n\n    // We found one thing, and one is enough.\n    if (state.one && state.found) {\n      break\n    }\n\n    count(before, node.children[index])\n  }\n}\n\n/**\n * Apply selectors to an element.\n *\n * @param {State} state\n *   Current state.\n * @param {Array<AstRule>} rules\n *   Rules to apply.\n * @param {Element} node\n *   Element to apply rules to.\n * @param {number | undefined} index\n *   Index of `node` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Nest}\n *   Further rules.\n */\nfunction applySelectors(state, rules, node, index, parent) {\n  /** @type {Nest} */\n  const nestResult = {\n    adjacentSibling: undefined,\n    descendant: undefined,\n    directChild: undefined,\n    generalSibling: undefined\n  }\n  let selectorIndex = -1\n\n  while (++selectorIndex < rules.length) {\n    const rule = rules[selectorIndex]\n\n    // We found one thing, and one is enough.\n    if (state.one && state.found) {\n      break\n    }\n\n    // When shallow, we don’t allow nested rules.\n    // Idea: we could allow a stack of parents?\n    // Might get quite complex though.\n    if (state.shallow && rule.nestedRule) {\n      throw new Error('Expected selector without nesting')\n    }\n\n    // If this rule matches:\n    if ((0,_test_js__WEBPACK_IMPORTED_MODULE_0__.test)(rule, node, index, parent, state)) {\n      const nest = rule.nestedRule\n\n      // Are there more?\n      if (nest) {\n        /** @type {keyof Nest} */\n        const label =\n          nest.combinator === '+'\n            ? 'adjacentSibling'\n            : nest.combinator === '~'\n              ? 'generalSibling'\n              : nest.combinator === '>'\n                ? 'directChild'\n                : 'descendant'\n        add(nestResult, label, nest)\n      } else {\n        // We have a match!\n        state.found = true\n\n        if (!state.results.includes(node)) {\n          state.results.push(node)\n        }\n      }\n    }\n\n    // Descendant.\n    if (rule.combinator === undefined) {\n      add(nestResult, 'descendant', rule)\n    }\n    // Adjacent.\n    else if (rule.combinator === '~') {\n      add(nestResult, 'generalSibling', rule)\n    }\n    // Drop direct child (`>`), adjacent sibling (`+`).\n  }\n\n  return nestResult\n}\n\n/**\n * Combine two lists, if needed.\n *\n * This is optimized to create as few lists as possible.\n *\n * @param {Array<AstRule> | undefined} left\n *   Rules.\n * @param {Array<AstRule> | undefined} right\n *   Rules.\n * @returns {Array<AstRule>}\n *   Rules.\n */\nfunction combine(left, right) {\n  return left && right && left.length > 0 && right.length > 0\n    ? [...left, ...right]\n    : left && left.length > 0\n      ? left\n      : right && right.length > 0\n        ? right\n        : empty\n}\n\n/**\n * Count a node.\n *\n * @param {Counts} counts\n *   Counts.\n * @param {Nodes} node\n *   Node (we’re looking for elements).\n * @returns {undefined}\n *   Nothing.\n */\nfunction count(counts, node) {\n  if (node.type === 'element') {\n    // Uppercase to prevent prototype polution, injecting `constructor` or so.\n    // Normalize because HTML is insensitive.\n    const name = node.tagName.toUpperCase()\n    const count = (counts.types.get(name) || 0) + 1\n    counts.count++\n    counts.types.set(name, count)\n  }\n}\n\n/**\n * Check a node.\n *\n * @param {State} state\n *   State.\n * @param {Array<AstRule>} currentRules\n *   Rules.\n * @param {Nodes} node\n *   Node.\n * @param {number | undefined} index\n *   Index of `node` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {Nodes} tree\n *   Tree.\n * @returns {Nest}\n *   Nesting.\n */\nfunction one(state, currentRules, node, index, parent, tree) {\n  /** @type {Nest} */\n  let nestResult = {\n    adjacentSibling: undefined,\n    descendant: undefined,\n    directChild: undefined,\n    generalSibling: undefined\n  }\n\n  const exit = (0,_enter_state_js__WEBPACK_IMPORTED_MODULE_1__.enterState)(state, node)\n\n  if (node.type === 'element') {\n    let rootRules = state.rootQuery.rules\n\n    // Remove direct child rules if this is the root.\n    // This only happens for a `:has()` rule, which can be like\n    // `a:has(> b)`.\n    if (parent && parent !== tree) {\n      rootRules = state.rootQuery.rules.filter(\n        (d) =>\n          d.combinator === undefined ||\n          (d.combinator === '>' && parent === tree)\n      )\n    }\n\n    nestResult = applySelectors(\n      state,\n      // Try the root rules for this element too.\n      combine(currentRules, rootRules),\n      node,\n      index,\n      parent\n    )\n  }\n\n  // If this is a parent, and we want to delve into them, and we haven’t found\n  // our single result yet.\n  if ('children' in node && !state.shallow && !(state.one && state.found)) {\n    all(state, nestResult, node, tree)\n  }\n\n  exit()\n\n  return nestResult\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/walk.js\n");

/***/ })

};
;