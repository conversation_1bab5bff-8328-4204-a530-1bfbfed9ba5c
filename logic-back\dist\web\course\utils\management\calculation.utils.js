"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationUtils = void 0;
class CalculationUtils {
    static calculateCompletionRate(publishedCourses, totalCourses) {
        if (totalCourses <= 0)
            return 0;
        if (publishedCourses < 0)
            return 0;
        const rate = publishedCourses / totalCourses;
        return parseFloat(rate.toFixed(2));
    }
    static calculateSkip(page, pageSize) {
        if (page < 1 || pageSize < 1)
            return 0;
        return (page - 1) * pageSize;
    }
    static calculateTotalPages(total, pageSize) {
        if (total <= 0 || pageSize <= 0)
            return 0;
        return Math.ceil(total / pageSize);
    }
    static calculateNextOrderIndex(maxOrderIndex) {
        return (maxOrderIndex || 0) + 1;
    }
    static formatVideoDuration(durationInSeconds) {
        if (durationInSeconds <= 0)
            return '无视频';
        const minutes = Math.floor(durationInSeconds / 60);
        const seconds = durationInSeconds % 60;
        if (minutes > 0) {
            let result = `${minutes}分钟`;
            if (seconds > 0)
                result += `${seconds}秒`;
            return result;
        }
        else {
            return `${seconds}秒`;
        }
    }
    static calculatePaginationInfo(page, pageSize, total) {
        const totalPages = this.calculateTotalPages(total, pageSize);
        return {
            page,
            pageSize,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
            skip: this.calculateSkip(page, pageSize)
        };
    }
}
exports.CalculationUtils = CalculationUtils;
//# sourceMappingURL=calculation.utils.js.map