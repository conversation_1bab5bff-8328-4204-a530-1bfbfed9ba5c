import { StudentSelfAssessmentSubmissionService } from './student_self_assessment_submission.service';
import { CreateStudentSelfAssessmentSubmissionDto } from './dto/create-student_self_assessment_submission.dto';
import { UpdateStudentSelfAssessmentSubmissionDto } from './dto/update-student_self_assessment_submission.dto';
import { CreateBulkStudentSelfAssessmentSubmissionDto } from './dto/create-bulk-student_self_assessment_submission.dto';
export declare class StudentSelfAssessmentSubmissionController {
    private readonly studentSelfAssessmentSubmissionService;
    constructor(studentSelfAssessmentSubmissionService: StudentSelfAssessmentSubmissionService);
    createBulk(createBulkDto: CreateBulkStudentSelfAssessmentSubmissionDto): Promise<{
        success: boolean;
        message: string;
    }>;
    findByAssignmentAndStudent(assignmentId: string, studentId: string): Promise<import("./entities/student_self_assessment_submission.entity").StudentSelfAssessmentSubmission[]>;
    findSubmissionsByItemId(itemId: string): Promise<{
        score: number;
        studentId: number;
        studentName: string;
        avatarUrl: string | undefined;
    }[]>;
    create(createStudentSelfAssessmentSubmissionDto: CreateStudentSelfAssessmentSubmissionDto): Promise<import("./entities/student_self_assessment_submission.entity").StudentSelfAssessmentSubmission>;
    findAll(): Promise<import("./entities/student_self_assessment_submission.entity").StudentSelfAssessmentSubmission[]>;
    findOne(id: string): Promise<import("./entities/student_self_assessment_submission.entity").StudentSelfAssessmentSubmission | null>;
    update(id: string, updateStudentSelfAssessmentSubmissionDto: UpdateStudentSelfAssessmentSubmissionDto): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
