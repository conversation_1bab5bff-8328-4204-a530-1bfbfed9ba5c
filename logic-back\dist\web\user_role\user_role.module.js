"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRoleModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_role_service_1 = require("./user_role.service");
const user_role_controller_1 = require("./user_role.controller");
const user_role_entity_1 = require("../../util/database/mysql/user_role/entities/user_role.entity");
const user_role_template_task_service_1 = require("./user_role_template_task.service");
const role_permission_template_entity_1 = require("../../util/database/mysql/role_permission_templates/entities/role_permission_template.entity");
const role_template_extension_permission_entity_1 = require("../../util/database/mysql/role_template_extension_permission/entities/role_template_extension_permission.entity");
const role_template_block_permission_entity_1 = require("../../util/database/mysql/role_template_block_permission/entities/role_template_block_permission.entity");
const block_entity_1 = require("../../util/database/mysql/block/entities/block.entity");
const extension_entity_1 = require("../../util/database/mysql/extensions/entities/extension.entity");
let UserRoleModule = class UserRoleModule {
};
exports.UserRoleModule = UserRoleModule;
exports.UserRoleModule = UserRoleModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_role_entity_1.UserRole,
                role_permission_template_entity_1.RolePermissionTemplate,
                role_template_extension_permission_entity_1.RoleTemplateExtensionPermission,
                role_template_block_permission_entity_1.RoleTemplateBlockPermission,
                block_entity_1.Block,
                extension_entity_1.Extension
            ])
        ],
        controllers: [user_role_controller_1.UserRoleController],
        providers: [
            user_role_service_1.UserRoleService,
            user_role_template_task_service_1.UserRoleTemplateTaskService
        ],
        exports: [user_role_service_1.UserRoleService, user_role_template_task_service_1.UserRoleTemplateTaskService],
    })
], UserRoleModule);
//# sourceMappingURL=user_role.module.js.map