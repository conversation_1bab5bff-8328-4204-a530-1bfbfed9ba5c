"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const task_template_entity_1 = require("./domain/entities/teaching/task-template.entity");
const course_teaching_record_entity_1 = require("./domain/entities/teaching/course-teaching-record.entity");
const course_tag_entity_1 = require("./domain/entities/marketplace/course-tag.entity");
const course_series_tag_entity_1 = require("./domain/entities/marketplace/course-series-tag.entity");
const teaching_controller_1 = require("./controller/teaching.controller");
const marketplace_controller_1 = require("./controller/marketplace.controller");
const management_service_1 = require("./application/services/management/management.service");
const teaching_service_1 = require("./application/services/teaching/teaching.service");
const lock_manager_1 = require("./utils/teaching/lock-manager");
const task_self_assessment_item_entity_1 = require("../../util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity");
const teacher_task_entity_1 = require("../../util/database/mysql/teacher_task/entities/teacher_task.entity");
const teacher_task_assignment_entity_1 = require("../../util/database/mysql/teacher_task_assignment/entities/teacher_task_assignment.entity");
const http_response_result_module_1 = require("../http_response_result/http_response_result.module");
const user_class_module_1 = require("../../util/database/mysql/user_class/user_class.module");
const user_join_role_module_1 = require("../../util/database/mysql/user_join_role/user_join_role.module");
const user_student_module_1 = require("../../util/database/mysql/user_student/user_student.module");
const web_point_permission_module_1 = require("../web_point_permission/web_point_permission.module");
const marketplace_service_1 = require("./application/services/marketplace/marketplace.service");
const management_controller_1 = require("./controller/management.controller");
const course_series_entity_1 = require("./domain/entities/management/course-series.entity");
const course_settings_entity_1 = require("./domain/entities/management/course-settings.entity");
const course_entity_1 = require("./domain/entities/management/course.entity");
let CourseModule = class CourseModule {
};
exports.CourseModule = CourseModule;
exports.CourseModule = CourseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                course_series_entity_1.CourseSeries,
                course_entity_1.Course,
                task_template_entity_1.TaskTemplate,
                course_settings_entity_1.CourseSettings,
                course_teaching_record_entity_1.CourseTeachingRecord,
                course_tag_entity_1.CourseTag,
                course_series_tag_entity_1.CourseSeriesTag,
                teacher_task_entity_1.TeacherTask,
                teacher_task_assignment_entity_1.TeacherTaskAssignment,
                task_self_assessment_item_entity_1.TaskSelfAssessmentItem,
            ]),
            http_response_result_module_1.HttpResponseResultModule,
            user_class_module_1.UserClassModule,
            user_student_module_1.UserStudentModule,
            web_point_permission_module_1.WebPointPermissionModule,
            user_join_role_module_1.UserJoinRoleModule,
        ],
        controllers: [
            management_controller_1.ManagementController,
            teaching_controller_1.TeachingController,
            marketplace_controller_1.MarketplaceController,
        ],
        providers: [
            management_service_1.ManagementService,
            teaching_service_1.TeachingService,
            marketplace_service_1.MarketplaceService,
            lock_manager_1.LockManager,
        ],
        exports: [
            management_service_1.ManagementService,
            teaching_service_1.TeachingService,
            marketplace_service_1.MarketplaceService,
        ]
    })
], CourseModule);
//# sourceMappingURL=course.module.js.map