"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPointsOfflineMessageController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_points_offline_message_service_1 = require("./user_points_offline_message.service");
const create_user_points_offline_message_dto_1 = require("./dto/create-user_points_offline_message.dto");
const update_user_points_offline_message_dto_1 = require("./dto/update-user_points_offline_message.dto");
const user_points_offline_message_entity_1 = require("./entities/user_points_offline_message.entity");
let UserPointsOfflineMessageController = class UserPointsOfflineMessageController {
    userPointsOfflineMessageService;
    constructor(userPointsOfflineMessageService) {
        this.userPointsOfflineMessageService = userPointsOfflineMessageService;
    }
    async create(createUserPointsOfflineMessageDto) {
        return await this.userPointsOfflineMessageService.create(createUserPointsOfflineMessageDto);
    }
    async findAll() {
        return await this.userPointsOfflineMessageService.findAll();
    }
    async findByUserId(userId) {
        return await this.userPointsOfflineMessageService.findByUserId(+userId);
    }
    async findPendingMessages() {
        return await this.userPointsOfflineMessageService.findPendingMessages();
    }
    async findOne(id) {
        return await this.userPointsOfflineMessageService.findOne(+id);
    }
    async update(id, updateUserPointsOfflineMessageDto) {
        return await this.userPointsOfflineMessageService.update(+id, updateUserPointsOfflineMessageDto);
    }
    async markAsSent(id) {
        return await this.userPointsOfflineMessageService.markAsSent(+id);
    }
    async remove(id) {
        await this.userPointsOfflineMessageService.remove(+id);
    }
};
exports.UserPointsOfflineMessageController = UserPointsOfflineMessageController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建离线积分消息' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: user_points_offline_message_entity_1.UserPointsOfflineMessage }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_points_offline_message_dto_1.CreateUserPointsOfflineMessageDto]),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有离线积分消息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_points_offline_message_entity_1.UserPointsOfflineMessage] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取离线积分消息' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_points_offline_message_entity_1.UserPointsOfflineMessage] }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('pending'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有待发送的离线积分消息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_points_offline_message_entity_1.UserPointsOfflineMessage] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "findPendingMessages", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取离线积分消息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '消息ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_points_offline_message_entity_1.UserPointsOfflineMessage }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '消息不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新离线积分消息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '消息ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_points_offline_message_entity_1.UserPointsOfflineMessage }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '消息不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_points_offline_message_dto_1.UpdateUserPointsOfflineMessageDto]),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/mark-as-sent'),
    (0, swagger_1.ApiOperation)({ summary: '标记消息为已发送' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '消息ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '标记成功', type: user_points_offline_message_entity_1.UserPointsOfflineMessage }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '消息不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "markAsSent", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除离线积分消息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '消息ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '消息不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserPointsOfflineMessageController.prototype, "remove", null);
exports.UserPointsOfflineMessageController = UserPointsOfflineMessageController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/离线积分消息(user_points_offline_message)'),
    (0, common_1.Controller)('user-points-offline-message'),
    __metadata("design:paramtypes", [user_points_offline_message_service_1.UserPointsOfflineMessageService])
], UserPointsOfflineMessageController);
//# sourceMappingURL=user_points_offline_message.controller.js.map