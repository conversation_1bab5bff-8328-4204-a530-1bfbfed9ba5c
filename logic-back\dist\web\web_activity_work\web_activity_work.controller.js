"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityWorkController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const web_activity_work_service_1 = require("./web_activity_work.service");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
let WebActivityWorkController = class WebActivityWorkController {
    webActivityWorkService;
    httpResponseResultService;
    constructor(webActivityWorkService, httpResponseResultService) {
        this.webActivityWorkService = webActivityWorkService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async addActivityWorks(data) {
        try {
            const result = await this.webActivityWorkService.addActivityWorks(data.activityId, data.works);
            return this.httpResponseResultService.success(result, '添加成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '添加失败', null, 400);
        }
    }
    async addImageToActivity(data) {
        try {
            const result = await this.webActivityWorkService.addImageToActivity(data.activityId, data.works);
            return this.httpResponseResultService.success(result, '添加成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '添加失败', null, 400);
        }
    }
    async updateActivityWorks(activityId, data) {
        try {
            const result = await this.webActivityWorkService.updateActivityWorks(activityId, data.works);
            return this.httpResponseResultService.success(result, '更新成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '更新失败', null, 400);
        }
    }
    async checkUserSubmitted(activityId, userId, req) {
        try {
            if (!req?.user && !userId) {
                return this.httpResponseResultService.success({
                    submitted: false
                }, '查询成功');
            }
            const submitted = await this.webActivityWorkService.checkUserSubmitted(activityId, userId || req?.user?.id);
            return this.httpResponseResultService.success({
                submitted
            }, '查询成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '查询失败', null, 400);
        }
    }
    async getActivityWorks(activityId, isAwarded, category, userId, status, contentType) {
        try {
            const result = await this.webActivityWorkService.getActivityWorks(activityId, { isAwarded, category, userId, status, contentType });
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async deleteActivityWork(id) {
        try {
            const result = await this.webActivityWorkService.deleteActivityWork(id);
            return this.httpResponseResultService.success(result, '删除成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '删除失败', null, 400);
        }
    }
    async deleteActivityWorks(data) {
        try {
            const result = await this.webActivityWorkService.deleteActivityWorks(data.activityId, data.workIds);
            return this.httpResponseResultService.success(result, '删除成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '删除失败', null, 400);
        }
    }
    async setWorkAwarded(id, isAwarded) {
        try {
            const result = await this.webActivityWorkService.setWorkAwarded(id, isAwarded);
            return this.httpResponseResultService.success(result, '设置成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '设置失败', null, 400);
        }
    }
    async updateWorkCategory(id, category) {
        try {
            const result = await this.webActivityWorkService.updateWorkCategory(id, category);
            return this.httpResponseResultService.success(result, '更新成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '更新失败', null, 400);
        }
    }
    async updateWorkStatus(id, status) {
        try {
            const result = await this.webActivityWorkService.updateWorkStatus(id, status);
            return this.httpResponseResultService.success(result, '更新成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '更新失败', null, 400);
        }
    }
    async cancelSubmission(id, req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('未登录用户无法取消报名', null, 401);
            }
            const result = await this.webActivityWorkService.cancelUserSubmission(id, userId);
            return this.httpResponseResultService.success(result, '取消成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '取消失败', null, 400);
        }
    }
};
exports.WebActivityWorkController = WebActivityWorkController;
__decorate([
    (0, common_1.Post)('/add-works'),
    (0, swagger_1.ApiOperation)({ summary: '添加活动作品关联' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['activityId', 'works'],
            properties: {
                activityId: { type: 'number', description: '活动ID' },
                works: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            workId: { type: 'number', description: '作品ID' },
                            userId: { type: 'number', description: '用户ID' },
                            isAwarded: { type: 'boolean', description: '是否获奖' },
                            category: { type: 'string', description: '作品分类' },
                            sort: { type: 'number', description: '排序' },
                            status: { type: 'number', description: '状态' },
                            contentType: { type: 'number', description: '内容类型' },
                            isImage: { type: 'boolean', description: '是否为图片' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "addActivityWorks", null);
__decorate([
    (0, common_1.Post)('/add-image'),
    (0, swagger_1.ApiOperation)({ summary: '添加图片内容到活动' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['activityId', 'works'],
            properties: {
                activityId: { type: 'number', description: '活动ID' },
                works: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            workId: { type: 'number', description: '作品ID' },
                            userId: { type: 'number', description: '用户ID' },
                            isAwarded: { type: 'boolean', description: '是否获奖' },
                            category: { type: 'string', description: '作品分类' },
                            sort: { type: 'number', description: '排序' },
                            status: { type: 'number', description: '状态' },
                            contentType: { type: 'number', description: '内容类型' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "addImageToActivity", null);
__decorate([
    (0, common_1.Put)('/update-works/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动作品关联' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['works'],
            properties: {
                works: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            workId: { type: 'number', description: '作品ID' },
                            userId: { type: 'number', description: '用户ID' },
                            isAwarded: { type: 'boolean', description: '是否获奖' },
                            category: { type: 'string', description: '作品分类' },
                            sort: { type: 'number', description: '排序' },
                            status: { type: 'number', description: '状态' },
                            contentType: { type: 'number', description: '内容类型' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "updateActivityWorks", null);
__decorate([
    (0, common_1.Get)('/check-submitted/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户是否已提交该活动的作品' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: Number, description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "checkUserSubmitted", null);
__decorate([
    (0, common_1.Get)('/list/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '获取活动的作品列表' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiQuery)({ name: 'isAwarded', required: false, type: Boolean, description: '是否获奖' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, type: String, description: '作品分类' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: Number, description: '用户ID' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, type: Number, description: '状态' }),
    (0, swagger_1.ApiQuery)({ name: 'contentType', required: false, type: Number, description: '内容类型' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, common_1.Query)('isAwarded')),
    __param(2, (0, common_1.Query)('category')),
    __param(3, (0, common_1.Query)('userId')),
    __param(4, (0, common_1.Query)('status')),
    __param(5, (0, common_1.Query)('contentType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Boolean, String, Number, Number, Number]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "getActivityWorks", null);
__decorate([
    (0, common_1.Delete)('/remove/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除活动作品关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "deleteActivityWork", null);
__decorate([
    (0, common_1.Post)('/batch-remove'),
    (0, swagger_1.ApiOperation)({ summary: '批量删除活动作品关联' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['activityId', 'workIds'],
            properties: {
                activityId: { type: 'number', description: '活动ID' },
                workIds: { type: 'array', items: { type: 'number' }, description: '作品ID数组' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "deleteActivityWorks", null);
__decorate([
    (0, common_1.Put)('/set-awarded/:id'),
    (0, swagger_1.ApiOperation)({ summary: '设置作品为获奖作品' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['isAwarded'],
            properties: {
                isAwarded: { type: 'boolean', description: '是否获奖' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('isAwarded')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Boolean]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "setWorkAwarded", null);
__decorate([
    (0, common_1.Put)('/update-category/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新作品分类' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['category'],
            properties: {
                category: { type: 'string', description: '作品分类' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "updateWorkCategory", null);
__decorate([
    (0, common_1.Put)('/update-status/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新报名状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['status'],
            properties: {
                status: { type: 'number', description: '报名状态' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "updateWorkStatus", null);
__decorate([
    (0, common_1.Put)('/cancel/:id'),
    (0, swagger_1.ApiOperation)({ summary: '用户取消报名' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityWorkController.prototype, "cancelSubmission", null);
exports.WebActivityWorkController = WebActivityWorkController = __decorate([
    (0, swagger_1.ApiTags)('web/活动作品关联管理(web_activity_work)'),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)('api/v1/activity_work'),
    __metadata("design:paramtypes", [web_activity_work_service_1.WebActivityWorkService,
        http_response_result_service_1.HttpResponseResultService])
], WebActivityWorkController);
//# sourceMappingURL=web_activity_work.controller.js.map