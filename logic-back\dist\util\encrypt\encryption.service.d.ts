import { KeyManagementService } from './key-management/key-management.service';
import { RedisSessionService } from './session/redis-session.service';
export declare enum SessionType {
    STANDARD = "standard",
    SECURE = "secure"
}
export interface SessionCreateResult {
    success: boolean;
    needsKeyUpdate?: boolean;
    errorMessage?: string;
}
export interface RedisSessionData {
    aesKey: string;
    iv: string;
    expiresAt: number;
    sessionType: SessionType;
    clientInfo?: any;
    createdAt: number;
}
export declare class EncryptionService {
    private readonly keyManagementService;
    private readonly redisSessionService;
    private readonly logger;
    private rsaPrivateKey;
    private rsaPublicKey;
    private readonly sessionCache;
    private readonly standardSessionTTL;
    private readonly secureSessionTTL;
    constructor(keyManagementService: KeyManagementService, redisSessionService: RedisSessionService);
    private getKeyFingerprint;
    getPublicKey(): {
        keyId: string;
        publicKey: string;
    };
    createSession(sessionId: string, encryptedAesKey: string, keyId?: string, sessionType?: SessionType, clientInfo?: any): Promise<SessionCreateResult>;
    createSecureSession(sessionId: string, encryptedAesKey: string, keyId?: string, clientInfo?: any): Promise<SessionCreateResult>;
    encrypt(data: string, sessionId: string): Promise<string>;
    decrypt(encryptedData: string, sessionId: string): Promise<string>;
    encryptWithSystemKey(data: string): string;
    decryptWithSystemKey(encryptedData: string): string;
    private getSessionKey;
    private parseSessionKey;
    getSessionType(sessionId: string): Promise<SessionType | undefined>;
    isSecureSession(sessionId: string): Promise<boolean>;
    deleteSession(sessionId: string): Promise<boolean>;
    cleanupExpiredSessions(): Promise<void>;
    logSessionStats(): Promise<void>;
    createSessionDebug(sessionId: string, aesKeyBase64: string, aesIvBase64: string): Promise<void>;
}
