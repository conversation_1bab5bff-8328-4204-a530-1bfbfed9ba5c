"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseSettingsResponseDto = exports.CourseSettingsDataDto = exports.CoursePreviewInfoDto = exports.TaskTemplateInfoDto = exports.CourseSettingsInfoDto = exports.CourseContentInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CourseContentInfoDto {
    hasVideo;
    hasDocument;
    hasAudio;
    videoDuration;
    videoDurationLabel;
    videoName;
    resourcesCount;
}
exports.CourseContentInfoDto = CourseContentInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含视频：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseContentInfoDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含文档：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseContentInfoDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含音频：0=否，1=是', example: 0 }),
    __metadata("design:type", Number)
], CourseContentInfoDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频时长(秒)', example: 3600 }),
    __metadata("design:type", Number)
], CourseContentInfoDto.prototype, "videoDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频时长标签', example: '60分钟' }),
    __metadata("design:type", String)
], CourseContentInfoDto.prototype, "videoDurationLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频文件名', example: 'Node.js基础入门讲解.mp4' }),
    __metadata("design:type", String)
], CourseContentInfoDto.prototype, "videoName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '资源数量', example: 3 }),
    __metadata("design:type", Number)
], CourseContentInfoDto.prototype, "resourcesCount", void 0);
class CourseSettingsInfoDto {
    templateId;
    templateName;
    requiredPoints;
    autoCreateTasks;
    autoCreateTasksLabel;
}
exports.CourseSettingsInfoDto = CourseSettingsInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '模板ID', example: 5 }),
    __metadata("design:type", Number)
], CourseSettingsInfoDto.prototype, "templateId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '模板名称', example: 'Node.js开发环境模板' }),
    __metadata("design:type", String)
], CourseSettingsInfoDto.prototype, "templateName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所需积分', example: 100 }),
    __metadata("design:type", Number)
], CourseSettingsInfoDto.prototype, "requiredPoints", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否自动创建任务：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseSettingsInfoDto.prototype, "autoCreateTasks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '自动创建任务标签', example: '是' }),
    __metadata("design:type", String)
], CourseSettingsInfoDto.prototype, "autoCreateTasksLabel", void 0);
class TaskTemplateInfoDto {
    id;
    taskName;
    taskDescription;
    durationDays;
    status;
    statusLabel;
    attachmentsCount;
    assessmentItemsCount;
    firstAttachmentType;
}
exports.TaskTemplateInfoDto = TaskTemplateInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '模板ID', example: 8 }),
    __metadata("design:type", Number)
], TaskTemplateInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称', example: 'Node.js基础练习' }),
    __metadata("design:type", String)
], TaskTemplateInfoDto.prototype, "taskName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务描述',
        example: '创建一个简单的Node.js应用，实现文件读写和HTTP服务器功能'
    }),
    __metadata("design:type", String)
], TaskTemplateInfoDto.prototype, "taskDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '持续天数', example: 7 }),
    __metadata("design:type", Number)
], TaskTemplateInfoDto.prototype, "durationDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：0=禁用，1=启用', example: 1 }),
    __metadata("design:type", Number)
], TaskTemplateInfoDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态标签', example: '启用' }),
    __metadata("design:type", String)
], TaskTemplateInfoDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附件数量', example: 2 }),
    __metadata("design:type", Number)
], TaskTemplateInfoDto.prototype, "attachmentsCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '自评项数量', example: 3 }),
    __metadata("design:type", Number)
], TaskTemplateInfoDto.prototype, "assessmentItemsCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第一个附件类型', example: 'document' }),
    __metadata("design:type", String)
], TaskTemplateInfoDto.prototype, "firstAttachmentType", void 0);
class CoursePreviewInfoDto {
    willAllocatePoints;
    pointsPerStudent;
    willApplyTemplate;
    willCreateTasks;
    tasksCount;
}
exports.CoursePreviewInfoDto = CoursePreviewInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否分配积分', example: true }),
    __metadata("design:type", Boolean)
], CoursePreviewInfoDto.prototype, "willAllocatePoints", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每学生积分', example: 100 }),
    __metadata("design:type", Number)
], CoursePreviewInfoDto.prototype, "pointsPerStudent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否应用模板', example: true }),
    __metadata("design:type", Boolean)
], CoursePreviewInfoDto.prototype, "willApplyTemplate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否创建任务', example: true }),
    __metadata("design:type", Boolean)
], CoursePreviewInfoDto.prototype, "willCreateTasks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务数量', example: 2 }),
    __metadata("design:type", Number)
], CoursePreviewInfoDto.prototype, "tasksCount", void 0);
class CourseSettingsDataDto {
    courseId;
    courseName;
    seriesName;
    contentInfo;
    settings;
    taskTemplates;
    preview;
}
exports.CourseSettingsDataDto = CourseSettingsDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 25 }),
    __metadata("design:type", Number)
], CourseSettingsDataDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程名称', example: '第一课：Node.js基础入门' }),
    __metadata("design:type", String)
], CourseSettingsDataDto.prototype, "courseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列名称', example: 'Node.js后端开发系列' }),
    __metadata("design:type", String)
], CourseSettingsDataDto.prototype, "seriesName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '内容信息', type: CourseContentInfoDto }),
    __metadata("design:type", CourseContentInfoDto)
], CourseSettingsDataDto.prototype, "contentInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置信息', type: CourseSettingsInfoDto }),
    __metadata("design:type", CourseSettingsInfoDto)
], CourseSettingsDataDto.prototype, "settings", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务模板列表', type: [TaskTemplateInfoDto] }),
    __metadata("design:type", Array)
], CourseSettingsDataDto.prototype, "taskTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预览信息', type: CoursePreviewInfoDto }),
    __metadata("design:type", CoursePreviewInfoDto)
], CourseSettingsDataDto.prototype, "preview", void 0);
class CourseSettingsResponseDto {
    code;
    message;
    data;
}
exports.CourseSettingsResponseDto = CourseSettingsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], CourseSettingsResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], CourseSettingsResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程设置数据', type: CourseSettingsDataDto }),
    __metadata("design:type", CourseSettingsDataDto)
], CourseSettingsResponseDto.prototype, "data", void 0);
//# sourceMappingURL=course-settings.dto.js.map