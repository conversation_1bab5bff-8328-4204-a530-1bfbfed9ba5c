import { CreateParticipationAuditDto } from './dto/create-participation_audit.dto';
import { UpdateParticipationAuditDto } from './dto/update-participation_audit.dto';
import { Repository } from 'typeorm';
import { ParticipationAudit } from './entities/participation_audit.entity';
export declare class ParticipationAuditService {
    private participationAuditRepository;
    constructor(participationAuditRepository: Repository<ParticipationAudit>);
    create(createParticipationAuditDto: CreateParticipationAuditDto): Promise<ParticipationAudit>;
    findAll(): Promise<ParticipationAudit[]>;
    findOne(id: number): Promise<ParticipationAudit>;
    update(id: number, updateParticipationAuditDto: UpdateParticipationAuditDto): Promise<ParticipationAudit>;
    remove(id: number): Promise<void>;
}
