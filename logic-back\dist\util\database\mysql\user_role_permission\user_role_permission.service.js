"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRolePermissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_role_permission_entity_1 = require("./entities/user_role_permission.entity");
let UserRolePermissionService = class UserRolePermissionService {
    userRolePermissionRepository;
    constructor(userRolePermissionRepository) {
        this.userRolePermissionRepository = userRolePermissionRepository;
    }
    async create(createUserRolePermissionDto) {
        const exists = await this.userRolePermissionRepository.findOne({
            where: {
                roleId: createUserRolePermissionDto.roleId,
                permissionId: createUserRolePermissionDto.permissionId
            }
        });
        if (exists) {
            throw new common_1.ConflictException('该角色已拥有此权限');
        }
        const rolePermission = this.userRolePermissionRepository.create(createUserRolePermissionDto);
        return await this.userRolePermissionRepository.save(rolePermission);
    }
    async findAll() {
        return await this.userRolePermissionRepository.find({
            order: { createTime: 'DESC' }
        });
    }
    async findOne(id) {
        const rolePermission = await this.userRolePermissionRepository.findOne({ where: { id } });
        if (!rolePermission) {
            throw new common_1.NotFoundException(`角色权限关联ID为${id}的记录不存在`);
        }
        return rolePermission;
    }
    async findByRole(roleId) {
        return await this.userRolePermissionRepository.find({
            where: { roleId },
            order: { createTime: 'DESC' }
        });
    }
    async findByPermission(permissionId) {
        return await this.userRolePermissionRepository.find({
            where: { permissionId },
            order: { createTime: 'DESC' }
        });
    }
    async findByRoleAndPermission(roleId, permissionId) {
        return await this.userRolePermissionRepository.findOne({
            where: { roleId, permissionId }
        });
    }
    async update(id, updateUserRolePermissionDto) {
        if (updateUserRolePermissionDto.roleId !== undefined && updateUserRolePermissionDto.permissionId !== undefined) {
            const exists = await this.userRolePermissionRepository.findOne({
                where: {
                    roleId: updateUserRolePermissionDto.roleId,
                    permissionId: updateUserRolePermissionDto.permissionId
                }
            });
            if (exists && exists.id !== id) {
                throw new common_1.ConflictException('该角色已拥有此权限');
            }
        }
        const rolePermission = await this.findOne(id);
        Object.assign(rolePermission, updateUserRolePermissionDto);
        return await this.userRolePermissionRepository.save(rolePermission);
    }
    async remove(id) {
        const result = await this.userRolePermissionRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`角色权限关联ID为${id}的记录不存在`);
        }
    }
    async removeByRoleAndPermission(roleId, permissionId) {
        const result = await this.userRolePermissionRepository.delete({ roleId, permissionId });
        if (result.affected === 0) {
            throw new common_1.NotFoundException('该角色未拥有此权限');
        }
    }
    async batchAssignPermissions(roleId, permissionIds) {
        const results = [];
        for (const permissionId of permissionIds) {
            const exists = await this.findByRoleAndPermission(roleId, permissionId);
            if (!exists) {
                const rolePermission = this.userRolePermissionRepository.create({
                    roleId,
                    permissionId
                });
                results.push(await this.userRolePermissionRepository.save(rolePermission));
            }
        }
        return results;
    }
    async removeAllByRole(roleId) {
        await this.userRolePermissionRepository.delete({ roleId });
    }
};
exports.UserRolePermissionService = UserRolePermissionService;
exports.UserRolePermissionService = UserRolePermissionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_role_permission_entity_1.UserRolePermission)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserRolePermissionService);
//# sourceMappingURL=user_role_permission.service.js.map