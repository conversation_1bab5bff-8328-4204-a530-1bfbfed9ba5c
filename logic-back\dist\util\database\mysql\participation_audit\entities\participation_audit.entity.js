"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipationAudit = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let ParticipationAudit = class ParticipationAudit {
    id;
    activityId;
    workId;
    activityWorkId;
    userId;
    auditorId;
    auditorName;
    result;
    reason;
    beforeStatus;
    afterStatus;
    createTime;
    updateTime;
    operationIp;
    deviceInfo;
};
exports.ParticipationAudit = ParticipationAudit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动ID' }),
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "activityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品ID' }),
    (0, swagger_1.ApiProperty)({ description: '作品ID' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "workId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动作品关联ID' }),
    (0, swagger_1.ApiProperty)({ description: '活动作品关联ID' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "activityWorkId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核人ID' }),
    (0, swagger_1.ApiProperty)({ description: '审核人ID' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "auditorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核人姓名' }),
    (0, swagger_1.ApiProperty)({ description: '审核人姓名' }),
    __metadata("design:type", String)
], ParticipationAudit.prototype, "auditorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核结果：1-通过 2-拒绝', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '审核结果：1-通过 2-拒绝', default: 0 }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核意见', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '审核意见', required: false }),
    __metadata("design:type", String)
], ParticipationAudit.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核前状态' }),
    (0, swagger_1.ApiProperty)({ description: '审核前状态' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "beforeStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核后状态' }),
    (0, swagger_1.ApiProperty)({ description: '审核后状态' }),
    __metadata("design:type", Number)
], ParticipationAudit.prototype, "afterStatus", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ParticipationAudit.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ParticipationAudit.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '操作IP', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '操作IP', required: false }),
    __metadata("design:type", String)
], ParticipationAudit.prototype, "operationIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '设备信息', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    __metadata("design:type", String)
], ParticipationAudit.prototype, "deviceInfo", void 0);
exports.ParticipationAudit = ParticipationAudit = __decorate([
    (0, typeorm_1.Entity)('participation_audit')
], ParticipationAudit);
//# sourceMappingURL=participation_audit.entity.js.map