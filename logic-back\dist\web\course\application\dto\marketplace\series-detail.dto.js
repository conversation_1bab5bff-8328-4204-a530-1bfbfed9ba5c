"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeriesDetailResponseDto = exports.SeriesDetailDataDto = exports.DefaultCourseDto = exports.CourseInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const series_list_dto_1 = require("./series-list.dto");
class CourseInfoDto {
    id;
    title;
    description;
    coverImage;
    orderIndex;
    status;
    statusLabel;
    hasVideo;
    hasDocument;
    hasAudio;
    videoDuration;
    videoDurationLabel;
    videoName;
    firstTeachingTitle;
    resourcesCount;
}
exports.CourseInfoDto = CourseInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 1 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程标题', example: '第一课：React基础概念' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程描述', example: '了解React的核心概念和基本用法' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程封面', example: 'https://example.com/course1-cover.jpg' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序索引', example: 1 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "orderIndex", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程状态：0=草稿，1=已发布，2=已归档', example: 1 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程状态标签', example: '已发布' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含视频：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含文档：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含音频：0=否，1=是', example: 0 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频时长(秒)', example: 1800 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "videoDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频时长标签', example: '30分钟' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "videoDurationLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频文件名', example: 'React基础概念讲解.mp4' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "videoName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第一个教学信息标题', example: '教学目标' }),
    __metadata("design:type", String)
], CourseInfoDto.prototype, "firstTeachingTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '资源数量', example: 2 }),
    __metadata("design:type", Number)
], CourseInfoDto.prototype, "resourcesCount", void 0);
class DefaultCourseDto extends CourseInfoDto {
    contentConfig;
    teachingInfo;
    additionalResources;
}
exports.DefaultCourseDto = DefaultCourseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '内容配置(完整JSON)',
        example: {
            hasVideo: 1,
            hasDocument: 1,
            hasAudio: 0,
            video: {
                url: 'https://example.com/videos/react-basics.mp4',
                name: 'React基础概念讲解.mp4'
            },
            document: {
                url: 'https://example.com/documents/react-basics-slides.pdf',
                name: 'React基础概念课件.pdf'
            },
            audio: null
        }
    }),
    __metadata("design:type", Object)
], DefaultCourseDto.prototype, "contentConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '教学信息(完整JSON)',
        example: [
            {
                title: '教学目标',
                content: [
                    '理解React的核心概念和设计思想',
                    '掌握JSX语法的基本使用方法'
                ]
            }
        ]
    }),
    __metadata("design:type", Array)
], DefaultCourseDto.prototype, "teachingInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '附加资源(完整JSON)',
        example: [
            {
                title: 'React官方文档',
                url: 'https://react.dev/',
                description: 'React官方学习资源'
            }
        ]
    }),
    __metadata("design:type", Array)
], DefaultCourseDto.prototype, "additionalResources", void 0);
class SeriesDetailDataDto {
    id;
    title;
    description;
    coverImage;
    category;
    categoryLabel;
    status;
    statusLabel;
    projectMembers;
    totalCourses;
    totalStudents;
    creatorId;
    createdAt;
    updatedAt;
    tags;
    courses;
    defaultCourse;
    currentCourseId;
}
exports.SeriesDetailDataDto = SeriesDetailDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列ID', example: 123 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列标题', example: 'React前端开发系列' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列描述', example: '从零开始学习React开发，包含基础概念、组件开发、状态管理等核心内容' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '封面图片URL', example: 'https://example.com/series-cover.jpg' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类：0=官方，1=社区', example: 0 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类标签', example: '官方' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "categoryLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：0=草稿，1=已发布，2=已归档', example: 1 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态标签', example: '已发布' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目成员', example: '张老师、李助教、王同学' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "projectMembers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程总数', example: 3 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "totalCourses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生总数', example: 850 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "totalStudents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建者ID', example: 100 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "creatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-15T10:30:00Z' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2024-01-22T09:15:00Z' }),
    __metadata("design:type", String)
], SeriesDetailDataDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签列表', type: [series_list_dto_1.TagInfoDto] }),
    __metadata("design:type", Array)
], SeriesDetailDataDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程列表', type: [CourseInfoDto] }),
    __metadata("design:type", Array)
], SeriesDetailDataDto.prototype, "courses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '默认显示的课程详情', type: DefaultCourseDto }),
    __metadata("design:type", DefaultCourseDto)
], SeriesDetailDataDto.prototype, "defaultCourse", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前课程ID', example: 1 }),
    __metadata("design:type", Number)
], SeriesDetailDataDto.prototype, "currentCourseId", void 0);
class SeriesDetailResponseDto {
    code;
    message;
    data;
}
exports.SeriesDetailResponseDto = SeriesDetailResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], SeriesDetailResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], SeriesDetailResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列详情数据', type: () => SeriesDetailDataDto }),
    __metadata("design:type", SeriesDetailDataDto)
], SeriesDetailResponseDto.prototype, "data", void 0);
//# sourceMappingURL=series-detail.dto.js.map