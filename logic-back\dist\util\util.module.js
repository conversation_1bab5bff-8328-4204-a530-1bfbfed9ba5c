"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UtilModule = void 0;
const aliyun_static_gesture_recognition_module_1 = require("./ai_providers/aliyun_static_gesture_recognition/aliyun_static_gesture_recognition.module");
const common_1 = require("@nestjs/common");
const yaml_module_1 = require("./yaml/yaml.module");
const ali_oss_module_1 = require("./ali_service/ali_oss/ali_oss.module");
const mysql_module_1 = require("./database/mysql/mysql.module");
const redis_module_1 = require("./database/redis/redis.module");
const queue_module_1 = require("./queue/queue.module");
const web_socket_module_1 = require("./web_socket/web_socket.module");
const aliyun_expression_module_1 = require("./ai_providers/aliyun_expression/aliyun_expression.module");
const aliyun_face_recognition_module_1 = require("./ai_providers/aliyun_face_recognition/aliyun_face_recognition.module");
const minimax_image_module_1 = require("./ai_providers/minimax-image/minimax-image.module");
const minimax_tts_module_1 = require("./ai_providers/minimax-tts/minimax-tts.module");
const ali_qwen_vision_module_1 = require("./ai_providers/ali-qwen-vision/ali-qwen-vision.module");
const ali_qwen_turbo_module_1 = require("./ai_providers/ali-qwen-turbo/ali-qwen-turbo.module");
const zhipu_llm_module_1 = require("./ai_providers/zhipu-llm/zhipu-llm.module");
const aliyun_segment_image_module_1 = require("./ai_providers/aliyun_segment_image/aliyun_segment_image.module");
const aliyun_image_score_module_1 = require("./ai_providers/aliyun_image_score/aliyun_image_score.module");
const baidu_image_enhance_module_1 = require("./ai_providers/baidu_image_enhance/baidu_image_enhance.module");
const ali_sms_module_1 = require("./ali_service/ali_sms/ali_sms.module");
const aliyun_face_compare_module_1 = require("./ai_providers/aliyun_face_compare/aliyun_face_compare.module");
const xunfei_speech_recognition_module_1 = require("./ai_providers/xunfei_speech_recognition/xunfei_speech_recognition.module");
const aliyun_object_detection_module_1 = require("./ai_providers/aliyun_object_detection/aliyun_object_detection.module");
const encrypt_module_1 = require("./encrypt/encrypt.module");
let UtilModule = class UtilModule {
};
exports.UtilModule = UtilModule;
exports.UtilModule = UtilModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mysql_module_1.MysqlModule,
            yaml_module_1.YamlModule,
            ali_oss_module_1.AliOssModule,
            redis_module_1.RedisModule,
            queue_module_1.QueueModule,
            web_socket_module_1.WebSocketModule,
            aliyun_expression_module_1.AliyunExpressionModule,
            minimax_image_module_1.MinimaxImageModule,
            minimax_tts_module_1.MinimaxTtsModule,
            ali_qwen_vision_module_1.AliQwenVisionModule,
            ali_qwen_turbo_module_1.AliQwenTurboModule,
            zhipu_llm_module_1.ZhipuLlmModule,
            aliyun_segment_image_module_1.AliyunSegmentImageModule,
            aliyun_image_score_module_1.AliyunImageScoreModule,
            baidu_image_enhance_module_1.BaiduImageEnhanceModule,
            ali_sms_module_1.AliSmsModule,
            aliyun_face_recognition_module_1.AliyunFaceRecognitionModule,
            aliyun_face_compare_module_1.AliyunFaceCompareModule,
            xunfei_speech_recognition_module_1.XunfeiSpeechRecognitionModule,
            aliyun_object_detection_module_1.AliyunObjectDetectionModule,
            aliyun_static_gesture_recognition_module_1.AliyunStaticGestureRecognitionModule,
            encrypt_module_1.EncryptModule
        ],
    })
], UtilModule);
//# sourceMappingURL=util.module.js.map