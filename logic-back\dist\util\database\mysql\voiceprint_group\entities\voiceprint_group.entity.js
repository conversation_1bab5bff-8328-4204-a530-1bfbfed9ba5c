"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintGroupEntity = void 0;
const typeorm_1 = require("typeorm");
const voiceprint_feature_entity_1 = require("../../voiceprint_feature/entities/voiceprint-feature.entity");
let VoiceprintGroupEntity = class VoiceprintGroupEntity {
    id;
    userId;
    groupId;
    groupName;
    description;
    createdAt;
    updatedAt;
    features;
};
exports.VoiceprintGroupEntity = VoiceprintGroupEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'tinyint' }),
    __metadata("design:type", Number)
], VoiceprintGroupEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', length: 36, comment: '用户ID' }),
    __metadata("design:type", String)
], VoiceprintGroupEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'group_id', length: 100, comment: '讯飞声纹特征库ID' }),
    __metadata("design:type", String)
], VoiceprintGroupEntity.prototype, "groupId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'group_name', length: 100, comment: '声纹特征库名称' }),
    __metadata("design:type", String)
], VoiceprintGroupEntity.prototype, "groupName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'description', length: 500, nullable: true, comment: '描述信息' }),
    __metadata("design:type", String)
], VoiceprintGroupEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], VoiceprintGroupEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], VoiceprintGroupEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => voiceprint_feature_entity_1.VoiceprintFeature, feature => feature.group),
    __metadata("design:type", Array)
], VoiceprintGroupEntity.prototype, "features", void 0);
exports.VoiceprintGroupEntity = VoiceprintGroupEntity = __decorate([
    (0, typeorm_1.Entity)('voiceprint_group')
], VoiceprintGroupEntity);
//# sourceMappingURL=voiceprint_group.entity.js.map