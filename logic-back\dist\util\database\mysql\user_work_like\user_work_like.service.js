"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserWorkLikeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_work_like_entity_1 = require("./entities/user_work_like.entity");
let UserWorkLikeService = class UserWorkLikeService {
    userWorkLikeRepository;
    constructor(userWorkLikeRepository) {
        this.userWorkLikeRepository = userWorkLikeRepository;
    }
    async create(createUserWorkLikeDto) {
        const existingLike = await this.userWorkLikeRepository.findOne({
            where: {
                userId: createUserWorkLikeDto.userId,
                targetId: createUserWorkLikeDto.targetId,
                targetType: createUserWorkLikeDto.targetType,
            }
        });
        if (existingLike) {
            existingLike.status = createUserWorkLikeDto.status || 1;
            if (existingLike.status === 1) {
                existingLike.likeTime = new Date();
                existingLike.unlikeTime = null;
            }
            else {
                existingLike.unlikeTime = new Date();
            }
            return this.userWorkLikeRepository.save(existingLike);
        }
        else {
            const userWorkLike = this.userWorkLikeRepository.create(createUserWorkLikeDto);
            return this.userWorkLikeRepository.save(userWorkLike);
        }
    }
    async findAll() {
        return this.userWorkLikeRepository.find();
    }
    async findOne(id) {
        const userWorkLike = await this.userWorkLikeRepository.findOne({ where: { id } });
        if (!userWorkLike) {
            throw new common_1.NotFoundException(`用户作品点赞关系ID ${id} 未找到`);
        }
        return userWorkLike;
    }
    async update(id, updateUserWorkLikeDto) {
        await this.userWorkLikeRepository.update(id, updateUserWorkLikeDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.userWorkLikeRepository.delete(id);
    }
    async findByUserIdAndTargetId(userId, targetId, targetType) {
        return this.userWorkLikeRepository.findOne({
            where: { userId, targetId, targetType },
            relations: ['work'],
        });
    }
    async findByUserId(userId) {
        return this.userWorkLikeRepository.find({
            where: { userId, status: 1 },
            relations: ['work'],
            order: { likeTime: 'DESC' },
        });
    }
    async findByTargetId(targetId, targetType) {
        return this.userWorkLikeRepository.find({
            where: { targetId, targetType, status: 1 },
            order: { likeTime: 'DESC' },
        });
    }
    async toggleLike(userId, targetId, targetType) {
        const existingLike = await this.userWorkLikeRepository.findOne({
            where: { userId, targetId, targetType },
        });
        if (existingLike) {
            existingLike.status = existingLike.status === 1 ? 0 : 1;
            if (existingLike.status === 1) {
                existingLike.likeTime = new Date();
                existingLike.unlikeTime = null;
            }
            else {
                existingLike.unlikeTime = new Date();
            }
            return this.userWorkLikeRepository.save(existingLike);
        }
        else {
            const newLike = this.userWorkLikeRepository.create({
                userId,
                targetId,
                targetType,
                status: 1,
                likeTime: new Date(),
            });
            return this.userWorkLikeRepository.save(newLike);
        }
    }
};
exports.UserWorkLikeService = UserWorkLikeService;
exports.UserWorkLikeService = UserWorkLikeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_work_like_entity_1.UserWorkLike)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserWorkLikeService);
//# sourceMappingURL=user_work_like.service.js.map