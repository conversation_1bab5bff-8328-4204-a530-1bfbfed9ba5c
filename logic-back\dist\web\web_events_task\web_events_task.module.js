"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebEventsTaskModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const web_events_task_controller_1 = require("./web_events_task.controller");
const web_events_task_service_1 = require("./web_events_task.service");
const activity_events_task_module_1 = require("../../util/database/mysql/activity_events_task/activity_events_task.module");
const activity_events_task_entity_1 = require("../../util/database/mysql/activity_events_task/entities/activity_events_task.entity");
const http_response_result_module_1 = require("../http_response_result/http_response_result.module");
let WebEventsTaskModule = class WebEventsTaskModule {
};
exports.WebEventsTaskModule = WebEventsTaskModule;
exports.WebEventsTaskModule = WebEventsTaskModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([activity_events_task_entity_1.ActivityEventsTask]),
            activity_events_task_module_1.ActivityEventsTaskModule,
            http_response_result_module_1.HttpResponseResultModule,
        ],
        controllers: [web_events_task_controller_1.WebEventsTaskController],
        providers: [web_events_task_service_1.WebEventsTaskService],
        exports: [web_events_task_service_1.WebEventsTaskService],
    })
], WebEventsTaskModule);
//# sourceMappingURL=web_events_task.module.js.map