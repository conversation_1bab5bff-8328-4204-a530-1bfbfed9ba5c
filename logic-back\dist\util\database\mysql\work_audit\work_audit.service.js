"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkAuditService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const work_audit_entity_1 = require("./entities/work_audit.entity");
let WorkAuditService = class WorkAuditService {
    workAuditRepository;
    constructor(workAuditRepository) {
        this.workAuditRepository = workAuditRepository;
    }
    async create(createWorkAuditDto) {
        const workAudit = this.workAuditRepository.create(createWorkAuditDto);
        return await this.workAuditRepository.save(workAudit);
    }
    async findAll() {
        return await this.workAuditRepository.find();
    }
    async findOne(id) {
        const workAudit = await this.workAuditRepository.findOne({ where: { id } });
        if (!workAudit) {
            throw new common_1.NotFoundException(`作品审核记录 #${id} 未找到`);
        }
        return workAudit;
    }
    async update(id, updateWorkAuditDto) {
        const workAudit = await this.findOne(id);
        Object.assign(workAudit, updateWorkAuditDto);
        return await this.workAuditRepository.save(workAudit);
    }
    async remove(id) {
        const result = await this.workAuditRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`作品审核记录 #${id} 未找到`);
        }
    }
};
exports.WorkAuditService = WorkAuditService;
exports.WorkAuditService = WorkAuditService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(work_audit_entity_1.WorkAudit)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], WorkAuditService);
//# sourceMappingURL=work_audit.service.js.map