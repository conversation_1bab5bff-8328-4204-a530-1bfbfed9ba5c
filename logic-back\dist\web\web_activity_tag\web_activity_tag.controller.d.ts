import { WebActivityTagService } from './web_activity_tag.service';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class WebActivityTagController {
    private readonly webActivityTagService;
    private readonly httpResponseResultService;
    constructor(webActivityTagService: WebActivityTagService, httpResponseResultService: HttpResponseResultService);
    addActivityTags(data: {
        activityId: number;
        tagIds: number[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    updateActivityTags(activityId: number, data: {
        tagIds: number[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    deleteActivityTags(activityId: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    getActivityTagIds(activityId: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<number[]>>;
    getTagActivityIds(tagId: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<number[]>>;
}
