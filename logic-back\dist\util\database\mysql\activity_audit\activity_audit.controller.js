"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityAuditController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const activity_audit_service_1 = require("./activity_audit.service");
const create_activity_audit_dto_1 = require("./dto/create-activity_audit.dto");
const update_activity_audit_dto_1 = require("./dto/update-activity_audit.dto");
const activity_audit_entity_1 = require("./entities/activity_audit.entity");
let ActivityAuditController = class ActivityAuditController {
    activityAuditService;
    constructor(activityAuditService) {
        this.activityAuditService = activityAuditService;
    }
    create(createActivityAuditDto) {
        return this.activityAuditService.create(createActivityAuditDto);
    }
    findAll() {
        return this.activityAuditService.findAll();
    }
    findByActivityId(activityId) {
        return this.activityAuditService.findByActivityId(+activityId);
    }
    findByAuditorId(auditorId) {
        return this.activityAuditService.findByAuditorId(+auditorId);
    }
    findByResult(result) {
        return this.activityAuditService.findByResult(+result);
    }
    findOne(id) {
        return this.activityAuditService.findOne(+id);
    }
    update(id, updateActivityAuditDto) {
        return this.activityAuditService.update(+id, updateActivityAuditDto);
    }
    remove(id) {
        return this.activityAuditService.remove(+id);
    }
    hardRemove(id) {
        return this.activityAuditService.hardRemove(+id);
    }
};
exports.ActivityAuditController = ActivityAuditController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建活动审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '创建成功', type: activity_audit_entity_1.ActivityAudit }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_audit_dto_1.CreateActivityAuditDto]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有活动审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_audit_entity_1.ActivityAudit] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定活动的所有审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_audit_entity_1.ActivityAudit] }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "findByActivityId", null);
__decorate([
    (0, common_1.Get)('auditor/:auditorId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定审核人的所有审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_audit_entity_1.ActivityAudit] }),
    __param(0, (0, common_1.Param)('auditorId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "findByAuditorId", null);
__decorate([
    (0, common_1.Get)('result/:result'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定审核结果的所有审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_audit_entity_1.ActivityAudit] }),
    __param(0, (0, common_1.Param)('result')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "findByResult", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取活动审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: activity_audit_entity_1.ActivityAudit }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '活动审核记录不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动审核记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: activity_audit_entity_1.ActivityAudit }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '活动审核记录不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_activity_audit_dto_1.UpdateActivityAuditDto]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除活动审核记录（软删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)(':id/hard'),
    (0, swagger_1.ApiOperation)({ summary: '彻底删除活动审核记录（硬删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityAuditController.prototype, "hardRemove", null);
exports.ActivityAuditController = ActivityAuditController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/活动审核记录(activity_audit)'),
    (0, common_1.Controller)('activity-audit'),
    __metadata("design:paramtypes", [activity_audit_service_1.ActivityAuditService])
], ActivityAuditController);
//# sourceMappingURL=activity_audit.controller.js.map