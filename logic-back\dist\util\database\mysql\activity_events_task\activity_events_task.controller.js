"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityEventsTaskController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const activity_events_task_service_1 = require("./activity_events_task.service");
const create_activity_events_task_dto_1 = require("./dto/create-activity-events-task.dto");
const update_activity_events_task_dto_1 = require("./dto/update-activity-events-task.dto");
const activity_events_task_entity_1 = require("./entities/activity_events_task.entity");
let ActivityEventsTaskController = class ActivityEventsTaskController {
    activityEventsTaskService;
    constructor(activityEventsTaskService) {
        this.activityEventsTaskService = activityEventsTaskService;
    }
    async create(createActivityEventsTaskDto) {
        return this.activityEventsTaskService.create(createActivityEventsTaskDto);
    }
    async findAll() {
        return this.activityEventsTaskService.findAll();
    }
    async getStatistics() {
        return this.activityEventsTaskService.getStatistics();
    }
    async findByStatus(status) {
        return this.activityEventsTaskService.findByStatus(status);
    }
    async findByUser(userId) {
        return this.activityEventsTaskService.findByUser(userId);
    }
    async findByCreator(creatorId) {
        return this.activityEventsTaskService.findByCreator(creatorId);
    }
    async findBySchool(schoolName) {
        return this.activityEventsTaskService.findBySchool(schoolName);
    }
    async findByActivityId(activityId) {
        return this.activityEventsTaskService.findByActivityId(activityId);
    }
    async findOne(id) {
        return this.activityEventsTaskService.findOne(id);
    }
    async update(id, updateActivityEventsTaskDto) {
        return this.activityEventsTaskService.update(id, updateActivityEventsTaskDto);
    }
    async updateStatus(id, status) {
        return this.activityEventsTaskService.updateStatus(id, status);
    }
    async remove(id) {
        return this.activityEventsTaskService.remove(id);
    }
};
exports.ActivityEventsTaskController = ActivityEventsTaskController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建赛事任务' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: activity_events_task_entity_1.ActivityEventsTask }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_events_task_dto_1.CreateActivityEventsTaskDto]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有赛事任务' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取赛事任务统计信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('status/:status'),
    (0, swagger_1.ApiOperation)({ summary: '根据状态获取赛事任务' }),
    (0, swagger_1.ApiParam)({ name: 'status', description: '任务状态：0-待开始 1-进行中 2-已提交 3-已审核 4-审核不通过' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __param(0, (0, common_1.Param)('status', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取赛事任务' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findByUser", null);
__decorate([
    (0, common_1.Get)('creator/:creatorId'),
    (0, swagger_1.ApiOperation)({ summary: '根据创建者ID获取赛事任务' }),
    (0, swagger_1.ApiParam)({ name: 'creatorId', description: '创建者ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __param(0, (0, common_1.Param)('creatorId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findByCreator", null);
__decorate([
    (0, common_1.Get)('school'),
    (0, swagger_1.ApiOperation)({ summary: '根据学校名称获取赛事任务' }),
    (0, swagger_1.ApiQuery)({ name: 'schoolName', description: '学校名称' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __param(0, (0, common_1.Query)('schoolName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findBySchool", null);
__decorate([
    (0, common_1.Get)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '根据活动ID获取赛事任务' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findByActivityId", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取赛事任务详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '赛事任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: activity_events_task_entity_1.ActivityEventsTask }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '赛事任务不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新赛事任务' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '赛事任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: activity_events_task_entity_1.ActivityEventsTask }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '赛事任务不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_activity_events_task_dto_1.UpdateActivityEventsTaskDto]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新赛事任务状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '赛事任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: activity_events_task_entity_1.ActivityEventsTask }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '赛事任务不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('status', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除赛事任务（软删除）' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '赛事任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '赛事任务不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityEventsTaskController.prototype, "remove", null);
exports.ActivityEventsTaskController = ActivityEventsTaskController = __decorate([
    (0, swagger_1.ApiTags)('赛事任务管理'),
    (0, common_1.Controller)('activity-events-task'),
    __metadata("design:paramtypes", [activity_events_task_service_1.ActivityEventsTaskService])
], ActivityEventsTaskController);
//# sourceMappingURL=activity_events_task.controller.js.map