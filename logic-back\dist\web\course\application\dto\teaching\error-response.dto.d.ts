export declare class ConcurrencyConflictDataDto {
    reason: string;
    courseId: number;
    classId: number;
    lockKey: string;
    retryAfter: number;
}
export declare class DuplicateOperationDataDto {
    reason: string;
    courseId: number;
    classId: number;
    teacherId: number;
    existingRecordId: number;
    lastExecutionTime: string;
}
export declare class CourseNotFoundDataDto {
    courseId: number;
    reason: string;
}
export declare class InsufficientPermissionDataDto {
    reason: string;
    teacherId: number;
    classId: number;
}
export declare class EmptyClassDataDto {
    reason: string;
    classId: number;
    studentCount: number;
}
export declare class IncompleteSettingsDataDto {
    reason: string;
    courseId: number;
    missingSettings: string[];
}
export declare class FailedOperationDto {
    operation: string;
    error: string;
    affectedStudents: number;
}
export declare class PartialFailureDetailsDto {
    courseName: string;
    seriesName: string;
    className: string;
    studentCount: number;
    pointsPerStudent: number;
    templateName: string;
    createdTasks: any[];
    failedOperations: FailedOperationDto[];
    warningMessages: string[];
}
export declare class PartialFailureDataDto {
    success: boolean;
    teachingRecordId: number;
    pointsAllocated: number;
    tasksCreated: number;
    templateApplied: boolean;
    executionTime: string;
    lockAcquireTime: number;
    totalExecutionTime: number;
    details: PartialFailureDetailsDto;
}
