import { CreateWorkAuditDto } from './dto/create-work_audit.dto';
import { UpdateWorkAuditDto } from './dto/update-work_audit.dto';
import { Repository } from 'typeorm';
import { WorkAudit } from './entities/work_audit.entity';
export declare class WorkAuditService {
    private workAuditRepository;
    constructor(workAuditRepository: Repository<WorkAudit>);
    create(createWorkAuditDto: CreateWorkAuditDto): Promise<WorkAudit>;
    findAll(): Promise<WorkAudit[]>;
    findOne(id: number): Promise<WorkAudit>;
    update(id: number, updateWorkAuditDto: UpdateWorkAuditDto): Promise<WorkAudit>;
    remove(id: number): Promise<void>;
}
