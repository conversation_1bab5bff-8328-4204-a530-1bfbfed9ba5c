"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateActivityAuditDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateActivityAuditDto {
    activityId;
    auditorId;
    auditorName;
    result;
    reason;
    beforeStatus;
    afterStatus;
    operationIp;
    deviceInfo;
    isDelete;
}
exports.CreateActivityAuditDto = CreateActivityAuditDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityAuditDto.prototype, "activityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核人ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityAuditDto.prototype, "auditorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核人姓名' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActivityAuditDto.prototype, "auditorName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核结果：1-通过 2-拒绝', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityAuditDto.prototype, "result", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核意见', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateActivityAuditDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核前状态' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityAuditDto.prototype, "beforeStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核后状态' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityAuditDto.prototype, "afterStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作IP', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateActivityAuditDto.prototype, "operationIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateActivityAuditDto.prototype, "deviceInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false, required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateActivityAuditDto.prototype, "isDelete", void 0);
//# sourceMappingURL=create-activity_audit.dto.js.map