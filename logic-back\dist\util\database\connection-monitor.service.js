"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ConnectionMonitorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionMonitorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
let ConnectionMonitorService = ConnectionMonitorService_1 = class ConnectionMonitorService {
    dataSource;
    logger = new common_1.Logger(ConnectionMonitorService_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        this.logger.log('数据库连接监控服务已启动');
        await this.checkConnectionStatus();
    }
    async checkConnectionStatus() {
        try {
            if (this.dataSource && this.dataSource.driver && this.dataSource.driver.pool) {
                const pool = this.dataSource.driver.pool;
                const poolStatus = {
                    totalConnections: pool._allConnections ? pool._allConnections.length : 0,
                    freeConnections: pool._freeConnections ? pool._freeConnections.length : 0,
                    acquiringConnections: pool._acquiringConnections ? pool._acquiringConnections.length : 0,
                    connectionLimit: pool.config ? pool.config.connectionLimit : 0,
                    queueLimit: pool.config ? pool.config.queueLimit : 0,
                };
                const usageRate = poolStatus.totalConnections / poolStatus.connectionLimit;
                if (usageRate > 0.8) {
                    this.logger.warn(`数据库连接池使用率过高: ${(usageRate * 100).toFixed(1)}%`, poolStatus);
                }
                if (new Date().getMinutes() % 10 === 0) {
                    this.logger.log('数据库连接池状态:', poolStatus);
                }
            }
        }
        catch (error) {
            this.logger.error('检查数据库连接状态失败:', error);
        }
    }
    async cleanupIdleConnections() {
        try {
            if (this.dataSource && this.dataSource.driver && this.dataSource.driver.pool) {
                const pool = this.dataSource.driver.pool;
                if (pool._freeConnections && pool._freeConnections.length > 5) {
                    this.logger.log(`清理多余的空闲连接，当前空闲连接数: ${pool._freeConnections.length}`);
                    const connectionsToClose = pool._freeConnections.splice(5);
                    for (const connection of connectionsToClose) {
                        try {
                            if (connection && connection.end) {
                                connection.end();
                            }
                        }
                        catch (error) {
                            this.logger.error('关闭空闲连接失败:', error);
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('清理空闲连接失败:', error);
        }
    }
    async getConnectionPoolStatus() {
        try {
            if (this.dataSource && this.dataSource.driver && this.dataSource.driver.pool) {
                const pool = this.dataSource.driver.pool;
                return {
                    totalConnections: pool._allConnections ? pool._allConnections.length : 0,
                    freeConnections: pool._freeConnections ? pool._freeConnections.length : 0,
                    acquiringConnections: pool._acquiringConnections ? pool._acquiringConnections.length : 0,
                    connectionLimit: pool.config ? pool.config.connectionLimit : 0,
                    queueLimit: pool.config ? pool.config.queueLimit : 0,
                    isConnected: this.dataSource.isInitialized,
                };
            }
            return {
                totalConnections: 0,
                freeConnections: 0,
                acquiringConnections: 0,
                connectionLimit: 0,
                queueLimit: 0,
                isConnected: false,
            };
        }
        catch (error) {
            this.logger.error('获取连接池状态失败:', error);
            throw error;
        }
    }
    async forceReleaseIdleConnections() {
        try {
            if (this.dataSource && this.dataSource.driver && this.dataSource.driver.pool) {
                const pool = this.dataSource.driver.pool;
                if (pool._freeConnections) {
                    const idleCount = pool._freeConnections.length;
                    this.logger.log(`强制释放 ${idleCount} 个空闲连接`);
                    const connectionsToClose = pool._freeConnections.splice(0);
                    for (const connection of connectionsToClose) {
                        try {
                            if (connection && connection.end) {
                                connection.end();
                            }
                        }
                        catch (error) {
                            this.logger.error('关闭连接失败:', error);
                        }
                    }
                    return { releasedConnections: idleCount };
                }
            }
            return { releasedConnections: 0 };
        }
        catch (error) {
            this.logger.error('强制释放空闲连接失败:', error);
            throw error;
        }
    }
};
exports.ConnectionMonitorService = ConnectionMonitorService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConnectionMonitorService.prototype, "checkConnectionStatus", null);
__decorate([
    (0, schedule_1.Cron)('0 */30 * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConnectionMonitorService.prototype, "cleanupIdleConnections", null);
exports.ConnectionMonitorService = ConnectionMonitorService = ConnectionMonitorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], ConnectionMonitorService);
//# sourceMappingURL=connection-monitor.service.js.map