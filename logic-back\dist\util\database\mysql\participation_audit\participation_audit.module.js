"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipationAuditModule = void 0;
const common_1 = require("@nestjs/common");
const participation_audit_service_1 = require("./participation_audit.service");
const participation_audit_controller_1 = require("./participation_audit.controller");
const typeorm_1 = require("@nestjs/typeorm");
const participation_audit_entity_1 = require("./entities/participation_audit.entity");
let ParticipationAuditModule = class ParticipationAuditModule {
};
exports.ParticipationAuditModule = ParticipationAuditModule;
exports.ParticipationAuditModule = ParticipationAuditModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([participation_audit_entity_1.ParticipationAudit])],
        controllers: [participation_audit_controller_1.ParticipationAuditController],
        providers: [participation_audit_service_1.ParticipationAuditService],
        exports: [participation_audit_service_1.ParticipationAuditService],
    })
], ParticipationAuditModule);
//# sourceMappingURL=participation_audit.module.js.map