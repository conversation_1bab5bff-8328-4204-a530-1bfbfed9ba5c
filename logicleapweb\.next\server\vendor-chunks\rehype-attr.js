"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-attr";
exports.ids = ["vendor-chunks/rehype-attr"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-attr/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/rehype-attr/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/rehype-attr/lib/utils.js\");\n\n\nconst rehypeAttrs = (options = {}) => {\n    const { properties = 'data', codeBlockParames = true } = options;\n    return (tree) => {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, 'element', (node, index, parent) => {\n            if (codeBlockParames && node.tagName === 'pre' && node && Array.isArray(node.children) && parent && Array.isArray(parent.children) && parent.children.length > 1) {\n                const firstChild = node.children[0];\n                if (firstChild && firstChild.tagName === 'code' && typeof index === 'number') {\n                    const child = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.prevChild)(parent.children, index);\n                    if (child) {\n                        const attr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getCommentObject)(child);\n                        if (Object.keys(attr).length > 0) {\n                            node.properties = { ...node.properties, ...{ 'data-type': 'rehyp' } };\n                            firstChild.properties = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.propertiesHandle)(firstChild.properties, attr, properties);\n                        }\n                    }\n                }\n            }\n            if (/^(em|strong|b|a|i|p|pre|kbd|blockquote|h(1|2|3|4|5|6)|code|table|img|del|ul|ol)$/.test(node.tagName) && parent && Array.isArray(parent.children) && typeof index === 'number') {\n                const child = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.nextChild)(parent.children, index, '', codeBlockParames);\n                if (child) {\n                    const attr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getCommentObject)(child);\n                    if (Object.keys(attr).length > 0) {\n                        node.properties = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.propertiesHandle)(node.properties, attr, properties);\n                    }\n                }\n            }\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (rehypeAttrs);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-attr/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-attr/lib/utils.js":
/*!***********************************************!*\
  !*** ./node_modules/rehype-attr/lib/utils.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCommentObject: () => (/* binding */ getCommentObject),\n/* harmony export */   getURLParameters: () => (/* binding */ getURLParameters),\n/* harmony export */   nextChild: () => (/* binding */ nextChild),\n/* harmony export */   prevChild: () => (/* binding */ prevChild),\n/* harmony export */   propertiesHandle: () => (/* binding */ propertiesHandle)\n/* harmony export */ });\nconst getURLParameters = (url = '') => (url.match(/([^?=&]+)(=([^&]*))/g) || []).reduce((a, v) => ((a[v.slice(0, v.indexOf('='))] = v.slice(v.indexOf('=') + 1)), a), {});\nconst prevChild = (data = [], index) => {\n    let i = index;\n    while (i > -1) {\n        i--;\n        if (!data[i])\n            return;\n        if ((data[i] && data[i].value && data[i].value.replace(/(\\n|\\s)/g, '') !== '') || data[i].type !== 'text') {\n            if (!/^rehype:/.test(data[i].value) || data[i].type !== 'comment')\n                return;\n            return data[i];\n        }\n    }\n    return;\n};\nconst nextChild = (data = [], index, tagName, codeBlockParames) => {\n    let i = index;\n    while (i < data.length) {\n        i++;\n        if (tagName) {\n            const element = data[i];\n            if (element && element.value && element.value.replace(/(\\n|\\s)/g, '') !== '' || data[i] && data[i].type === 'element') {\n                return element.tagName === tagName ? element : undefined;\n            }\n        }\n        else {\n            const element = data[i];\n            if (!element || element.type === 'element')\n                return;\n            if (element.type === 'text' && element.value.replace(/(\\n|\\s)/g, '') !== '')\n                return;\n            if (element.type && /^(comment|raw)$/ig.test(element.type)) {\n                if (element.value && !/^rehype:/.test(element.value.replace(/^(\\s+)?<!--(.*?)-->/, '$2') || '')) {\n                    return;\n                }\n                ;\n                if (codeBlockParames) {\n                    const nextNode = nextChild(data, i, 'pre', codeBlockParames);\n                    if (nextNode)\n                        return;\n                    element.value = (element.value || '').replace(/^(\\n|\\s)+/, '');\n                    return element;\n                }\n                else {\n                    element.value = (element.value || '').replace(/^(\\n|\\s)+/, '');\n                    return element;\n                }\n            }\n        }\n    }\n    return;\n};\n/**\n * 获取代码注视的位置\n * @param data 数据\n * @param index 当前数据所在的位置\n * @returns 返回 当前参数数据 Object，`{}`\n */\nconst getCommentObject = ({ value = '' }) => {\n    const param = getURLParameters(value.replace(/^<!--(.*?)-->/, '$1').replace(/^rehype:/, ''));\n    Object.keys(param).forEach((keyName) => {\n        if (param[keyName] === 'true') {\n            param[keyName] = true;\n        }\n        if (param[keyName] === 'false') {\n            param[keyName] = false;\n        }\n        if (typeof param[keyName] === 'string' && !/^0/.test(param[keyName]) && !isNaN(+param[keyName])) {\n            param[keyName] = +param[keyName];\n        }\n    });\n    return param;\n};\nconst propertiesHandle = (defaultAttrs, attrs, type) => {\n    if (type === 'string') {\n        return { ...defaultAttrs, 'data-config': JSON.stringify({ ...attrs, rehyp: true }) };\n    }\n    else if (type === 'attr') {\n        return { ...defaultAttrs, ...attrs };\n    }\n    return { ...defaultAttrs, 'data-config': { ...attrs, rehyp: true } };\n};\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLWF0dHIvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQU8sd0tBQXdLO0FBQ3hLO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ08sNEJBQTRCLFlBQVk7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ087QUFDUDtBQUNBLGlCQUFpQixpREFBaUQsdUJBQXVCO0FBQ3pGO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhLGtDQUFrQztBQUMvQztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JlaHlwZS1hdHRyL2xpYi91dGlscy5qcz80ODljIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRVUkxQYXJhbWV0ZXJzID0gKHVybCA9ICcnKSA9PiAodXJsLm1hdGNoKC8oW14/PSZdKykoPShbXiZdKikpL2cpIHx8IFtdKS5yZWR1Y2UoKGEsIHYpID0+ICgoYVt2LnNsaWNlKDAsIHYuaW5kZXhPZignPScpKV0gPSB2LnNsaWNlKHYuaW5kZXhPZignPScpICsgMSkpLCBhKSwge30pO1xuZXhwb3J0IGNvbnN0IHByZXZDaGlsZCA9IChkYXRhID0gW10sIGluZGV4KSA9PiB7XG4gICAgbGV0IGkgPSBpbmRleDtcbiAgICB3aGlsZSAoaSA+IC0xKSB7XG4gICAgICAgIGktLTtcbiAgICAgICAgaWYgKCFkYXRhW2ldKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBpZiAoKGRhdGFbaV0gJiYgZGF0YVtpXS52YWx1ZSAmJiBkYXRhW2ldLnZhbHVlLnJlcGxhY2UoLyhcXG58XFxzKS9nLCAnJykgIT09ICcnKSB8fCBkYXRhW2ldLnR5cGUgIT09ICd0ZXh0Jykge1xuICAgICAgICAgICAgaWYgKCEvXnJlaHlwZTovLnRlc3QoZGF0YVtpXS52YWx1ZSkgfHwgZGF0YVtpXS50eXBlICE9PSAnY29tbWVudCcpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgcmV0dXJuIGRhdGFbaV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuO1xufTtcbmV4cG9ydCBjb25zdCBuZXh0Q2hpbGQgPSAoZGF0YSA9IFtdLCBpbmRleCwgdGFnTmFtZSwgY29kZUJsb2NrUGFyYW1lcykgPT4ge1xuICAgIGxldCBpID0gaW5kZXg7XG4gICAgd2hpbGUgKGkgPCBkYXRhLmxlbmd0aCkge1xuICAgICAgICBpKys7XG4gICAgICAgIGlmICh0YWdOYW1lKSB7XG4gICAgICAgICAgICBjb25zdCBlbGVtZW50ID0gZGF0YVtpXTtcbiAgICAgICAgICAgIGlmIChlbGVtZW50ICYmIGVsZW1lbnQudmFsdWUgJiYgZWxlbWVudC52YWx1ZS5yZXBsYWNlKC8oXFxufFxccykvZywgJycpICE9PSAnJyB8fCBkYXRhW2ldICYmIGRhdGFbaV0udHlwZSA9PT0gJ2VsZW1lbnQnKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVsZW1lbnQudGFnTmFtZSA9PT0gdGFnTmFtZSA/IGVsZW1lbnQgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCBlbGVtZW50ID0gZGF0YVtpXTtcbiAgICAgICAgICAgIGlmICghZWxlbWVudCB8fCBlbGVtZW50LnR5cGUgPT09ICdlbGVtZW50JylcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICBpZiAoZWxlbWVudC50eXBlID09PSAndGV4dCcgJiYgZWxlbWVudC52YWx1ZS5yZXBsYWNlKC8oXFxufFxccykvZywgJycpICE9PSAnJylcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICBpZiAoZWxlbWVudC50eXBlICYmIC9eKGNvbW1lbnR8cmF3KSQvaWcudGVzdChlbGVtZW50LnR5cGUpKSB7XG4gICAgICAgICAgICAgICAgaWYgKGVsZW1lbnQudmFsdWUgJiYgIS9ecmVoeXBlOi8udGVzdChlbGVtZW50LnZhbHVlLnJlcGxhY2UoL14oXFxzKyk/PCEtLSguKj8pLS0+LywgJyQyJykgfHwgJycpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgO1xuICAgICAgICAgICAgICAgIGlmIChjb2RlQmxvY2tQYXJhbWVzKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5leHROb2RlID0gbmV4dENoaWxkKGRhdGEsIGksICdwcmUnLCBjb2RlQmxvY2tQYXJhbWVzKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5leHROb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICBlbGVtZW50LnZhbHVlID0gKGVsZW1lbnQudmFsdWUgfHwgJycpLnJlcGxhY2UoL14oXFxufFxccykrLywgJycpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZWxlbWVudDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGVsZW1lbnQudmFsdWUgPSAoZWxlbWVudC52YWx1ZSB8fCAnJykucmVwbGFjZSgvXihcXG58XFxzKSsvLCAnJyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlbGVtZW50O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm47XG59O1xuLyoqXG4gKiDojrflj5bku6PnoIHms6jop4bnmoTkvY3nva5cbiAqIEBwYXJhbSBkYXRhIOaVsOaNrlxuICogQHBhcmFtIGluZGV4IOW9k+WJjeaVsOaNruaJgOWcqOeahOS9jee9rlxuICogQHJldHVybnMg6L+U5ZueIOW9k+WJjeWPguaVsOaVsOaNriBPYmplY3TvvIxge31gXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRDb21tZW50T2JqZWN0ID0gKHsgdmFsdWUgPSAnJyB9KSA9PiB7XG4gICAgY29uc3QgcGFyYW0gPSBnZXRVUkxQYXJhbWV0ZXJzKHZhbHVlLnJlcGxhY2UoL148IS0tKC4qPyktLT4vLCAnJDEnKS5yZXBsYWNlKC9ecmVoeXBlOi8sICcnKSk7XG4gICAgT2JqZWN0LmtleXMocGFyYW0pLmZvckVhY2goKGtleU5hbWUpID0+IHtcbiAgICAgICAgaWYgKHBhcmFtW2tleU5hbWVdID09PSAndHJ1ZScpIHtcbiAgICAgICAgICAgIHBhcmFtW2tleU5hbWVdID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGFyYW1ba2V5TmFtZV0gPT09ICdmYWxzZScpIHtcbiAgICAgICAgICAgIHBhcmFtW2tleU5hbWVdID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXJhbVtrZXlOYW1lXSA9PT0gJ3N0cmluZycgJiYgIS9eMC8udGVzdChwYXJhbVtrZXlOYW1lXSkgJiYgIWlzTmFOKCtwYXJhbVtrZXlOYW1lXSkpIHtcbiAgICAgICAgICAgIHBhcmFtW2tleU5hbWVdID0gK3BhcmFtW2tleU5hbWVdO1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHBhcmFtO1xufTtcbmV4cG9ydCBjb25zdCBwcm9wZXJ0aWVzSGFuZGxlID0gKGRlZmF1bHRBdHRycywgYXR0cnMsIHR5cGUpID0+IHtcbiAgICBpZiAodHlwZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIHsgLi4uZGVmYXVsdEF0dHJzLCAnZGF0YS1jb25maWcnOiBKU09OLnN0cmluZ2lmeSh7IC4uLmF0dHJzLCByZWh5cDogdHJ1ZSB9KSB9O1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlID09PSAnYXR0cicpIHtcbiAgICAgICAgcmV0dXJuIHsgLi4uZGVmYXVsdEF0dHJzLCAuLi5hdHRycyB9O1xuICAgIH1cbiAgICByZXR1cm4geyAuLi5kZWZhdWx0QXR0cnMsICdkYXRhLWNvbmZpZyc6IHsgLi4uYXR0cnMsIHJlaHlwOiB0cnVlIH0gfTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-attr/lib/utils.js\n");

/***/ })

};
;