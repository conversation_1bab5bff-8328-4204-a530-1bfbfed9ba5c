import { Repository } from 'typeorm';
import { CreateActivityWorkDto } from './dto/create-activity_work.dto';
import { UpdateActivityWorkDto } from './dto/update-activity_work.dto';
import { ActivityWork } from './entities/activity_work.entity';
export declare class ActivityWorkService {
    private readonly activityWorkRepository;
    constructor(activityWorkRepository: Repository<ActivityWork>);
    create(createActivityWorkDto: CreateActivityWorkDto): Promise<ActivityWork>;
    findAll(): Promise<ActivityWork[]>;
    findOne(id: number): Promise<ActivityWork>;
    update(id: number, updateActivityWorkDto: UpdateActivityWorkDto): Promise<ActivityWork>;
    remove(id: number): Promise<void>;
    hardRemove(id: number): Promise<void>;
    findByActivityId(activityId: number): Promise<ActivityWork[]>;
    findByWorkId(workId: number): Promise<ActivityWork[]>;
    findByUserId(userId: number): Promise<ActivityWork[]>;
    findByIsSelected(isSelected: number): Promise<ActivityWork[]>;
    findWinners(): Promise<ActivityWork[]>;
    updateIsSelected(id: number, isSelected: number): Promise<ActivityWork>;
    updateIsWinner(id: number, isWinner: number): Promise<ActivityWork>;
    removeByActivityId(activityId: number): Promise<void>;
    addActivityWorks(activityId: number, works: {
        workId: number;
        userId?: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
        status?: number;
        contentType?: number;
        isImage?: boolean;
    }[]): Promise<boolean>;
    addImageToActivity(activityId: number, works: {
        workId: number;
        userId?: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
        status?: number;
        contentType?: number;
    }[]): Promise<boolean>;
    updateActivityWorks(activityId: number, works: {
        workId: number;
        userId?: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
        status?: number;
        contentType?: number;
    }[]): Promise<boolean>;
    checkUserSubmitted(activityId: number, userId?: number): Promise<boolean>;
    getActivityWorks(activityId: number, filters?: {
        isAwarded?: boolean;
        category?: string;
        userId?: number;
        status?: number;
        contentType?: number;
    }): Promise<ActivityWork[]>;
    deleteActivityWork(id: number): Promise<boolean>;
    deleteActivityWorks(activityId: number, workIds: number[]): Promise<boolean>;
    setWorkAwarded(id: number, isAwarded: boolean): Promise<boolean>;
    updateWorkCategory(id: number, category: string): Promise<boolean>;
    updateWorkStatus(id: number, status: number): Promise<boolean>;
    cancelUserSubmission(id: number, userId: number): Promise<boolean>;
}
