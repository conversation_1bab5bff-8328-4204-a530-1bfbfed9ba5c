import { Repository } from 'typeorm';
import { VoiceprintGroupEntity } from './entities/voiceprint_group.entity';
import { CreateVoiceprintGroupDto, UpdateVoiceprintGroupDto, QueryVoiceprintGroupDto } from './dto/voiceprint_group.dto';
import { XunfeiVoiceprintRecognitionService } from '../../../ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.service';
export declare class VoiceprintGroupService {
    private voiceprintGroupRepository;
    private xunfeiVoiceprintService;
    constructor(voiceprintGroupRepository: Repository<VoiceprintGroupEntity>, xunfeiVoiceprintService: XunfeiVoiceprintRecognitionService);
    create(userId: string, createDto: CreateVoiceprintGroupDto): Promise<VoiceprintGroupEntity>;
    findByUserId(userId: string, queryDto?: QueryVoiceprintGroupDto): Promise<VoiceprintGroupEntity[]>;
    findOne(id: number): Promise<VoiceprintGroupEntity | null>;
    findByGroupId(groupId: string): Promise<VoiceprintGroupEntity | null>;
    update(id: number, updateDto: UpdateVoiceprintGroupDto): Promise<VoiceprintGroupEntity>;
    delete(id: number): Promise<boolean>;
}
