import { ActivityEventsTaskService } from './activity_events_task.service';
import { CreateActivityEventsTaskDto } from './dto/create-activity-events-task.dto';
import { UpdateActivityEventsTaskDto } from './dto/update-activity-events-task.dto';
import { ActivityEventsTask } from './entities/activity_events_task.entity';
export declare class ActivityEventsTaskController {
    private readonly activityEventsTaskService;
    constructor(activityEventsTaskService: ActivityEventsTaskService);
    create(createActivityEventsTaskDto: CreateActivityEventsTaskDto): Promise<ActivityEventsTask>;
    findAll(): Promise<ActivityEventsTask[]>;
    getStatistics(): Promise<any>;
    findByStatus(status: number): Promise<ActivityEventsTask[]>;
    findByUser(userId: number): Promise<ActivityEventsTask[]>;
    findByCreator(creatorId: number): Promise<ActivityEventsTask[]>;
    findBySchool(schoolName: string): Promise<ActivityEventsTask[]>;
    findByActivityId(activityId: number): Promise<ActivityEventsTask[]>;
    findOne(id: number): Promise<ActivityEventsTask>;
    update(id: number, updateActivityEventsTaskDto: UpdateActivityEventsTaskDto): Promise<ActivityEventsTask>;
    updateStatus(id: number, status: number): Promise<ActivityEventsTask>;
    remove(id: number): Promise<void>;
}
