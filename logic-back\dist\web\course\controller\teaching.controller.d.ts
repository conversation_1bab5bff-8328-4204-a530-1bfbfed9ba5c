import { TeachingService } from '../application/services/teaching/teaching.service';
import { OneClickStartDto } from '../application/dto/teaching/one-click-start.dto';
import { GetTeachingRecordsQueryDto } from '../application/dto/teaching/teaching-records.dto';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
export declare class TeachingController {
    private readonly teachingService;
    private readonly httpResponseResultService;
    constructor(teachingService: TeachingService, httpResponseResultService: HttpResponseResultService);
    private handleTeachingException;
    oneClickStart(dto: OneClickStartDto, currentUser: any): Promise<{
        code: any;
        msg: any;
        data: any;
    }>;
    getCourseSettings(courseId: string): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/teaching/course-settings.dto").CourseSettingsDataDto>>;
    getTeachingRecords(query: GetTeachingRecordsQueryDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/teaching/teaching-records.dto").TeachingRecordsDataDto>>;
}
