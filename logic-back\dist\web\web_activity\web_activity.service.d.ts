import { ActivityService } from 'src/util/database/mysql/activity/activity.service';
import { Activity } from 'src/util/database/mysql/activity/entities/activity.entity';
import { ActivitySubmitService } from 'src/util/database/mysql/activity_submit/activity_submit.service';
import { ActivityEventsTaskService } from 'src/util/database/mysql/activity_events_task/activity_events_task.service';
import { UserStudentService } from 'src/util/database/mysql/user_student/user_student.service';
import { UserSchoolService } from 'src/util/database/mysql/user_school/user_school.service';
import { UserClassService } from 'src/util/database/mysql/user_class/user_class.service';
import { UserInfoService } from 'src/util/database/mysql/user_info/user_info.service';
import { AliOssService } from 'src/util/ali_service/ali_oss/ali_oss.service';
export declare class WebActivityService {
    private readonly activityService;
    private readonly activitySubmitService;
    private readonly activityEventsTaskService;
    private readonly userStudentService;
    private readonly userSchoolService;
    private readonly userClassService;
    private readonly userInfoService;
    private readonly aliOssService;
    constructor(activityService: ActivityService, activitySubmitService: ActivitySubmitService, activityEventsTaskService: ActivityEventsTaskService, userStudentService: UserStudentService, userSchoolService: UserSchoolService, userClassService: UserClassService, userInfoService: UserInfoService, aliOssService: AliOssService);
    create(data: {
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage?: string;
        organizer: string;
        tagIds?: number[];
        activityType?: number;
        attachmentFiles?: string;
        promotionImage?: string;
        competitionGroups?: string;
        registrationForm?: string;
    }, creatorId: number): Promise<Activity>;
    updateActivity(id: number, data: {
        name?: string;
        startTime?: Date;
        endTime?: Date;
        coverImage?: string;
        organizer?: string;
        tagIds?: number[];
        status?: number;
        activityType?: number;
        attachmentFiles?: string;
        promotionImage?: string;
        competitionGroups?: string;
        registrationForm?: string;
    }): Promise<boolean>;
    deleteActivity(id: number): Promise<boolean>;
    getById(id: number): Promise<Activity>;
    list(params: {
        page?: number;
        size?: number;
        status?: number;
        keyword?: string;
    }): Promise<{
        list: Activity[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    getActivityWithWorks(id: number): Promise<{
        works: import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[];
        id: number;
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage: string;
        organizer: string;
        creatorId: number;
        createTime: Date;
        updateTime: Date;
        status: number;
        activityType: number;
        isDelete: boolean;
        reviewReason: string;
        attachmentFiles: string;
        promotionImage: string;
        competitionGroups: string;
        registrationForm: string;
        activityTags: import("../../util/database/mysql/activity_tag/entities/activity_tag.entity").ActivityTag[];
        activityWorks: import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[];
    }>;
    getActivityContentByType(id: number): Promise<{
        contentType: number;
        id: number;
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage: string;
        organizer: string;
        creatorId: number;
        createTime: Date;
        updateTime: Date;
        status: number;
        activityType: number;
        isDelete: boolean;
        reviewReason: string;
        attachmentFiles: string;
        promotionImage: string;
        competitionGroups: string;
        registrationForm: string;
        activityTags: import("../../util/database/mysql/activity_tag/entities/activity_tag.entity").ActivityTag[];
        activityWorks: import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[];
    }>;
    addWorksToActivity(activityId: number, works: {
        workId: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
    }[]): Promise<boolean>;
    setAwardedWorks(activityId: number, workIds: number[]): Promise<boolean>;
    uploadSignature(data: {
        signatureData: string;
        activityId: number;
        userId: number;
        userIp: string;
    }): Promise<{
        filePath: string;
        fileName: string;
        uploadTime: Date;
        userIp: string;
    }>;
    submitRegistration(data: {
        activityId: number;
        userId: number;
        userName: string;
        userPhone: string;
        agreementAccepted: boolean;
        parentConsentAccepted: boolean;
        parentSignaturePath: string;
        signatureTime?: Date;
        signatureIp?: string;
        remark?: string;
    }): Promise<import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit>;
    checkUserRegistration(activityId: number, userId: number): Promise<{
        submitted: boolean | null;
        submit: import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit | undefined;
    }>;
    cancelRegistration(activityId: number, userId: number): Promise<import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit>;
    getUserRegistrations(userId: number, query: {
        page?: number;
        size?: number;
    }): Promise<{
        list: import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit[];
        total: number;
        page: number;
        size: number;
        totalPages: number;
    }>;
    getRegistrationStatistics(activityId: number): Promise<{
        total: number;
        submitted: number;
        cancelled: number;
        approved: number;
        rejected: number;
    }>;
}
