import { UserRolePermissionService } from './user_role_permission.service';
import { CreateUserRolePermissionDto } from './dto/create-user_role_permission.dto';
import { UpdateUserRolePermissionDto } from './dto/update-user_role_permission.dto';
import { UserRolePermission } from './entities/user_role_permission.entity';
export declare class UserRolePermissionController {
    private readonly userRolePermissionService;
    constructor(userRolePermissionService: UserRolePermissionService);
    create(createUserRolePermissionDto: CreateUserRolePermissionDto): Promise<UserRolePermission>;
    batchAssignPermissions(roleId: number, permissionIds: number[]): Promise<UserRolePermission[]>;
    findAll(): Promise<UserRolePermission[]>;
    findByRole(roleId: string): Promise<UserRolePermission[]>;
    findByPermission(permissionId: string): Promise<UserRolePermission[]>;
    findOne(id: string): Promise<UserRolePermission>;
    update(id: string, updateUserRolePermissionDto: UpdateUserRolePermissionDto): Promise<UserRolePermission>;
    remove(id: string): Promise<void>;
    removeByRoleAndPermission(roleId: string, permissionId: string): Promise<void>;
    removeAllByRole(roleId: string): Promise<void>;
}
