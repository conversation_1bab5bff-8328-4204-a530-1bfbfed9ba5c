"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserPointsOfflineMessageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_user_points_offline_message_dto_1 = require("./create-user_points_offline_message.dto");
class UpdateUserPointsOfflineMessageDto extends (0, swagger_1.PartialType)(create_user_points_offline_message_dto_1.CreateUserPointsOfflineMessageDto) {
}
exports.UpdateUserPointsOfflineMessageDto = UpdateUserPointsOfflineMessageDto;
//# sourceMappingURL=update-user_points_offline_message.dto.js.map