import { OnModuleInit, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { UserRoleTemplateTaskService } from './web/user_role/user_role_template_task.service';
export declare class AppModule implements OnModuleInit, NestModule {
    private readonly userRoleTemplateTaskService?;
    constructor(userRoleTemplateTaskService?: UserRoleTemplateTaskService | undefined);
    configure(consumer: MiddlewareConsumer): void;
    onModuleInit(): Promise<void>;
}
