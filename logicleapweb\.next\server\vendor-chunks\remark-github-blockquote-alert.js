"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-github-blockquote-alert";
exports.ids = ["vendor-chunks/remark-github-blockquote-alert"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-github-blockquote-alert/lib/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/remark-github-blockquote-alert/lib/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAlertIcon: () => (/* binding */ getAlertIcon),\n/* harmony export */   remarkAlert: () => (/* binding */ remarkAlert)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n\nconst alertRegex = /^\\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\\]/i;\nconst alertLegacyRegex = /^\\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)(\\/.*)?\\]/i;\n/**\n * Alerts are a Markdown extension based on the blockquote syntax that you can use to emphasize critical information.\n * On GitHub, they are displayed with distinctive colors and icons to indicate the significance of the content.\n * https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax#alerts\n */\nconst remarkAlert = ({ legacyTitle = false, tagName = \"div\" } = {}) => {\n    return (tree) => {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, \"blockquote\", (node, index, parent) => {\n            let alertType = '';\n            let title = '';\n            let isNext = true;\n            let child = node.children.map((item) => {\n                if (isNext && item.type === \"paragraph\") {\n                    const firstNode = item.children[0];\n                    const text = firstNode.type === 'text' ? firstNode.value : '';\n                    const reg = legacyTitle ? alertLegacyRegex : alertRegex;\n                    const match = text.match(reg);\n                    if (match) {\n                        isNext = false;\n                        alertType = match[1].toLocaleLowerCase();\n                        title = legacyTitle ? match[2] || alertType.toLocaleUpperCase() : alertType.toLocaleUpperCase();\n                        if (text.includes('\\n')) {\n                            item.children[0] = {\n                                type: 'text',\n                                value: text.replace(reg, '').replace(/^\\n+/, ''),\n                            };\n                        }\n                        if (!text.includes('\\n')) {\n                            const itemChild = [];\n                            item.children.forEach((item, idx) => {\n                                if (idx == 0)\n                                    return;\n                                if (idx == 1 && item.type === 'break') {\n                                    return;\n                                }\n                                itemChild.push(item);\n                            });\n                            item.children = [...itemChild];\n                        }\n                    }\n                }\n                return item;\n            });\n            if (!!alertType) {\n                node.data = {\n                    hName: tagName,\n                    hProperties: {\n                        class: `markdown-alert markdown-alert-${alertType}`,\n                        dir: 'auto'\n                    },\n                };\n                child.unshift({\n                    type: \"paragraph\",\n                    children: [\n                        getAlertIcon(alertType),\n                        {\n                            type: \"text\",\n                            value: title.replace(/^\\//, ''),\n                        }\n                    ],\n                    data: {\n                        hProperties: {\n                            class: \"markdown-alert-title\",\n                            dir: \"auto\"\n                        }\n                    }\n                });\n            }\n            node.children = [...child];\n        });\n    };\n};\nfunction getAlertIcon(type) {\n    let pathD = pathData[type] ?? '';\n    return {\n        type: \"emphasis\",\n        data: {\n            hName: \"svg\",\n            hProperties: {\n                class: \"octicon\",\n                viewBox: '0 0 16 16',\n                width: '16',\n                height: '16',\n                ariaHidden: 'true',\n            },\n        },\n        children: [\n            {\n                type: \"emphasis\",\n                data: {\n                    hName: \"path\",\n                    hProperties: {\n                        d: pathD\n                    }\n                },\n                children: []\n            }\n        ]\n    };\n}\nconst pathData = {\n    note: 'M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13ZM6.5 7.75A.75.75 0 0 1 7.25 7h1a.75.75 0 0 1 .75.75v2.75h.25a.75.75 0 0 1 0 1.5h-2a.75.75 0 0 1 0-1.5h.25v-2h-.25a.75.75 0 0 1-.75-.75ZM8 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z',\n    tip: 'M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z',\n    important: 'M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z',\n    warning: 'M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z',\n    caution: 'M4.47.22A.749.749 0 0 1 5 0h6c.199 0 .389.079.53.22l4.25 4.25c.141.14.22.331.22.53v6a.749.749 0 0 1-.22.53l-4.25 4.25A.749.749 0 0 1 11 16H5a.749.749 0 0 1-.53-.22L.22 11.53A.749.749 0 0 1 0 11V5c0-.199.079-.389.22-.53Zm.84 1.28L1.5 5.31v5.38l3.81 3.81h5.38l3.81-3.81V5.31L10.69 1.5ZM8 4a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 8 4Zm0 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z',\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-github-blockquote-alert/lib/index.js\n");

/***/ })

};
;