"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateActivityWorkDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateActivityWorkDto {
    activityId;
    workId;
    userId;
    workTitle;
    workCover;
    workDesc;
    isSelected;
    isWinner;
    awardId;
    awardName;
    isShow;
    creatorId;
    isDelete;
}
exports.CreateActivityWorkDto = CreateActivityWorkDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "activityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "workId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品标题' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActivityWorkDto.prototype, "workTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品封面' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActivityWorkDto.prototype, "workCover", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品描述', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateActivityWorkDto.prototype, "workDesc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否入选：0-未入选 1-已入选', default: 0, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "isSelected", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否获奖：0-未获奖 1-已获奖', default: 0, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "isWinner", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '奖项ID', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "awardId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '奖项名称', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateActivityWorkDto.prototype, "awardName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否展示：0-不展示 1-展示', default: 1, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "isShow", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateActivityWorkDto.prototype, "creatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false, required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateActivityWorkDto.prototype, "isDelete", void 0);
//# sourceMappingURL=create-activity_work.dto.js.map