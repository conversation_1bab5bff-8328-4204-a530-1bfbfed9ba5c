"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityWorkService = void 0;
const common_1 = require("@nestjs/common");
const activity_work_service_1 = require("../../util/database/mysql/activity_work/activity_work.service");
let WebActivityWorkService = class WebActivityWorkService {
    activityWorkService;
    constructor(activityWorkService) {
        this.activityWorkService = activityWorkService;
    }
    async addActivityWorks(activityId, works) {
        return await this.activityWorkService.addActivityWorks(activityId, works);
    }
    async addImageToActivity(activityId, works) {
        return await this.activityWorkService.addImageToActivity(activityId, works);
    }
    async updateActivityWorks(activityId, works) {
        return await this.activityWorkService.updateActivityWorks(activityId, works);
    }
    async checkUserSubmitted(activityId, userId) {
        return await this.activityWorkService.checkUserSubmitted(activityId, userId);
    }
    async getActivityWorks(activityId, filters) {
        return await this.activityWorkService.getActivityWorks(activityId, filters);
    }
    async deleteActivityWork(id) {
        return await this.activityWorkService.deleteActivityWork(id);
    }
    async deleteActivityWorks(activityId, workIds) {
        return await this.activityWorkService.deleteActivityWorks(activityId, workIds);
    }
    async setWorkAwarded(id, isAwarded) {
        return await this.activityWorkService.setWorkAwarded(id, isAwarded);
    }
    async updateWorkCategory(id, category) {
        return await this.activityWorkService.updateWorkCategory(id, category);
    }
    async updateWorkStatus(id, status) {
        return await this.activityWorkService.updateWorkStatus(id, status);
    }
    async cancelUserSubmission(id, userId) {
        return await this.activityWorkService.cancelUserSubmission(id, userId);
    }
};
exports.WebActivityWorkService = WebActivityWorkService;
exports.WebActivityWorkService = WebActivityWorkService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [activity_work_service_1.ActivityWorkService])
], WebActivityWorkService);
//# sourceMappingURL=web_activity_work.service.js.map