"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebEventsTaskService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_events_task_service_1 = require("../../util/database/mysql/activity_events_task/activity_events_task.service");
const activity_events_task_entity_1 = require("../../util/database/mysql/activity_events_task/entities/activity_events_task.entity");
let WebEventsTaskService = class WebEventsTaskService {
    activityEventsTaskService;
    activityEventsTaskRepository;
    constructor(activityEventsTaskService, activityEventsTaskRepository) {
        this.activityEventsTaskService = activityEventsTaskService;
        this.activityEventsTaskRepository = activityEventsTaskRepository;
    }
    async createTask(createDto) {
        return await this.activityEventsTaskService.create(createDto);
    }
    async getUserTasks(userId) {
        return await this.activityEventsTaskService.findByUser(userId);
    }
    async getActivityTasks(activityId) {
        return await this.activityEventsTaskService.findByActivityId(activityId);
    }
    async getTaskDetail(taskId, userId) {
        const task = await this.activityEventsTaskService.findOne(taskId);
        if (userId && task.userId !== userId) {
            throw new common_1.NotFoundException('赛事任务不存在或无权访问');
        }
        return task;
    }
    async updateTask(taskId, updateDto, userId) {
        const task = await this.activityEventsTaskService.findOne(taskId);
        if (userId && task.userId !== userId) {
            throw new common_1.NotFoundException('赛事任务不存在或无权访问');
        }
        return await this.activityEventsTaskService.update(taskId, updateDto);
    }
    async updateTaskStatus(taskId, status, userId) {
        const task = await this.activityEventsTaskService.findOne(taskId);
        if (userId && task.userId !== userId) {
            throw new common_1.NotFoundException('赛事任务不存在或无权访问');
        }
        return await this.activityEventsTaskService.updateStatus(taskId, status);
    }
    async adminReviewTask(taskId, status, remark) {
        const task = await this.activityEventsTaskService.findOne(taskId);
        if (!task) {
            throw new common_1.NotFoundException('赛事任务不存在');
        }
        if (remark) {
            await this.activityEventsTaskService.update(taskId, { remark });
        }
        return await this.activityEventsTaskService.updateStatus(taskId, status);
    }
    async deleteTask(taskId, userId) {
        const task = await this.activityEventsTaskService.findOne(taskId);
        if (userId && task.userId !== userId) {
            throw new common_1.NotFoundException('赛事任务不存在或无权访问');
        }
        await this.activityEventsTaskService.remove(taskId);
    }
    async getTaskStatistics(userId) {
        if (userId) {
            const userTasks = await this.activityEventsTaskService.findByUser(userId);
            const stats = {
                total: userTasks.length,
                pending: userTasks.filter(task => task.status === 0).length,
                inProgress: userTasks.filter(task => task.status === 1).length,
                submitted: userTasks.filter(task => task.status === 2).length,
                reviewed: userTasks.filter(task => task.status === 3).length,
                rejected: userTasks.filter(task => task.status === 4).length,
                withWork: userTasks.filter(task => task.workId).length,
                withFile: userTasks.filter(task => task.workFile).length,
            };
            return stats;
        }
        else {
            return await this.activityEventsTaskService.getStatistics();
        }
    }
    async getUserTasksByStatus(userId, status) {
        const userTasks = await this.activityEventsTaskService.findByUser(userId);
        return userTasks.filter(task => task.status === status);
    }
    async searchUserTasks(userId, keyword) {
        const userTasks = await this.activityEventsTaskService.findByUser(userId);
        if (!keyword) {
            return userTasks;
        }
        const lowerKeyword = keyword.toLowerCase();
        return userTasks.filter(task => task.eventName.toLowerCase().includes(lowerKeyword) ||
            (task.workDescription && task.workDescription.toLowerCase().includes(lowerKeyword)) ||
            (task.instructorName && task.instructorName.toLowerCase().includes(lowerKeyword)) ||
            (task.schoolName && task.schoolName.toLowerCase().includes(lowerKeyword)));
    }
    async getUpcomingTasks(userId) {
        const userTasks = await this.activityEventsTaskService.findByUser(userId);
        const now = new Date();
        return userTasks.filter(task => task.status === 0 &&
            new Date(task.startTime) > now).sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
    }
    async getOngoingTasks(userId) {
        const userTasks = await this.activityEventsTaskService.findByUser(userId);
        const now = new Date();
        return userTasks.filter(task => task.status === 1 ||
            (task.status === 0 && new Date(task.startTime) <= now && new Date(task.endTime) >= now));
    }
    async batchUpdateTaskStatus(taskIds, status, userId) {
        for (const taskId of taskIds) {
            await this.updateTaskStatus(taskId, status, userId);
        }
    }
    async submitTask(taskId, submitDto, userId) {
        const task = await this.activityEventsTaskService.findOne(taskId);
        if (userId && task.userId !== userId) {
            throw new common_1.NotFoundException('赛事任务不存在或无权访问');
        }
        const now = new Date();
        if (now < new Date(task.startTime)) {
            throw new common_1.BadRequestException('任务尚未开始，无法提交');
        }
        if (now > new Date(task.endTime)) {
            throw new common_1.BadRequestException('任务已结束，无法提交');
        }
        if (!submitDto.isResubmit && (task.status === 2 || task.status === 3)) {
            throw new common_1.BadRequestException('任务已提交，如需重新提交请设置重新提交标志');
        }
        if (!submitDto.workId && !submitDto.workFile) {
            throw new common_1.BadRequestException('请至少提供作品ID或作品文件');
        }
        const updateData = {
            ...submitDto,
            status: 2,
            updateTime: now,
        };
        if (submitDto.isResubmit) {
            const resubmitNote = `[${now.toLocaleString('zh-CN')}] 重新提交`;
            updateData.remark = task.remark ? `${task.remark}\n${resubmitNote}` : resubmitNote;
        }
        await this.activityEventsTaskService.update(taskId, updateData);
        if (submitDto.workId && submitDto.workId > 0) {
            await this.activityEventsTaskRepository
                .createQueryBuilder()
                .update(activity_events_task_entity_1.ActivityEventsTask)
                .set({ workFile: () => 'NULL' })
                .where('id = :id', { id: taskId })
                .execute();
        }
        else if (submitDto.workFile && submitDto.workFile.trim()) {
            await this.activityEventsTaskRepository
                .createQueryBuilder()
                .update(activity_events_task_entity_1.ActivityEventsTask)
                .set({ workId: () => 'NULL' })
                .where('id = :id', { id: taskId })
                .execute();
        }
        if (submitDto.workFile === '') {
            await this.activityEventsTaskRepository
                .createQueryBuilder()
                .update(activity_events_task_entity_1.ActivityEventsTask)
                .set({ workFile: () => 'NULL' })
                .where('id = :id', { id: taskId })
                .execute();
        }
        if (submitDto.workId === 0) {
            await this.activityEventsTaskRepository
                .createQueryBuilder()
                .update(activity_events_task_entity_1.ActivityEventsTask)
                .set({ workId: () => 'NULL' })
                .where('id = :id', { id: taskId })
                .execute();
        }
        return await this.activityEventsTaskService.findOne(taskId);
    }
    async canSubmitTask(taskId, userId) {
        try {
            const task = await this.activityEventsTaskService.findOne(taskId);
            if (userId && task.userId !== userId) {
                return { canSubmit: false, reason: '赛事任务不存在或无权访问' };
            }
            const now = new Date();
            if (now < new Date(task.startTime)) {
                return { canSubmit: false, reason: '任务尚未开始' };
            }
            if (now > new Date(task.endTime)) {
                return { canSubmit: false, reason: '任务已结束' };
            }
            if (task.status === 4) {
                const rejectReason = task.remark || '任务审核不通过';
                return { canSubmit: true, isResubmit: true, rejectReason: rejectReason };
            }
            if (task.status === 2 || task.status === 3) {
                return { canSubmit: true, isResubmit: true };
            }
            return { canSubmit: true };
        }
        catch (error) {
            return { canSubmit: false, reason: error.message };
        }
    }
};
exports.WebEventsTaskService = WebEventsTaskService;
exports.WebEventsTaskService = WebEventsTaskService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(activity_events_task_entity_1.ActivityEventsTask)),
    __metadata("design:paramtypes", [activity_events_task_service_1.ActivityEventsTaskService,
        typeorm_2.Repository])
], WebEventsTaskService);
//# sourceMappingURL=web_events_task.service.js.map