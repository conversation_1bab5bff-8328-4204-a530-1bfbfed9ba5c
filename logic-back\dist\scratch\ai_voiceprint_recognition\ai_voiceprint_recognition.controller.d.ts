import { AiVoiceprintRecognitionService } from './ai_voiceprint_recognition.service';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
import { ScratchConfigService } from '../config/scratch.config.service';
import { UserPointService } from 'src/web/user_point/user_point.service';
import { VoiceprintGroupService } from 'src/util/database/mysql/voiceprint_group/voiceprint_group.service';
import { VoiceprintFeatureService } from 'src/util/database/mysql/voiceprint_feature/voiceprint_feature.service';
export declare class AiVoiceprintRecognitionController {
    private readonly aiVoiceprintRecognitionService;
    private readonly httpResponseResultService;
    private readonly scratchConfigService;
    private readonly userPointService;
    private readonly voiceprintGroupService;
    private readonly voiceprintFeatureService;
    constructor(aiVoiceprintRecognitionService: AiVoiceprintRecognitionService, httpResponseResultService: HttpResponseResultService, scratchConfigService: ScratchConfigService, userPointService: UserPointService, voiceprintGroupService: VoiceprintGroupService, voiceprintFeatureService: VoiceprintFeatureService);
    createGroup(token: string, body: any): Promise<import("../../web/http_response_result/http-response.interface").HttpResponse<any>>;
    createFeature(token: string, body: any): Promise<import("../../web/http_response_result/http-response.interface").HttpResponse<any>>;
    compareFeature(token: string, body: any): Promise<any>;
    searchFeature(token: string, body: any): Promise<any>;
    queryFeatureList(token: string, body: any): Promise<any>;
    getUserVoiceprintLibraries(token: string): Promise<import("../../web/http_response_result/http-response.interface").HttpResponse<any>>;
    getJobStatus(jobId: string): Promise<any>;
    updateFeature(token: string, body: any): Promise<any>;
    deleteFeature(token: string, body: any): Promise<any>;
    deleteGroup(token: string, body: any): Promise<import("../../web/http_response_result/http-response.interface").HttpResponse<any>>;
    getVoiceprintFeatures(token: string, groupId: string): Promise<import("../../web/http_response_result/http-response.interface").HttpResponse<any>>;
}
