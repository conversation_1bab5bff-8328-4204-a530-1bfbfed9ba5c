export declare class PassiveLogoutService {
    recordPassiveLogout(userId: number, reason: string, clientInfo?: any): Promise<void>;
    recordDeviceKickOut(userId: number, newDeviceInfo: string, clientInfo?: any): Promise<void>;
    recordTokenExpired(userId: number, clientInfo?: any): Promise<void>;
    recordAdminForceLogout(userId: number, adminId: number, clientInfo?: any): Promise<void>;
    recordSecurityLogout(userId: number, securityReason: string, clientInfo?: any): Promise<void>;
    recordMaintenanceLogout(userId: number, maintenanceReason: string, clientInfo?: any): Promise<void>;
    recordAccountStatusLogout(userId: number, statusChange: string, clientInfo?: any): Promise<void>;
    recordPermissionChangeLogout(userId: number, permissionChange: string, clientInfo?: any): Promise<void>;
    recordBatchPassiveLogout(userIds: number[], reason: string, clientInfo?: any): Promise<void>;
}
export declare enum PassiveLogoutReason {
    DEVICE_KICK_OUT = "\u65B0\u8BBE\u5907\u767B\u5F55\u6324\u51FA",
    TOKEN_EXPIRED = "Token\u8FC7\u671F",
    ADMIN_FORCE = "\u7BA1\u7406\u5458\u5F3A\u5236\u767B\u51FA",
    SECURITY_POLICY = "\u5B89\u5168\u7B56\u7565\u89E6\u53D1",
    SYSTEM_MAINTENANCE = "\u7CFB\u7EDF\u7EF4\u62A4",
    ACCOUNT_STATUS_CHANGE = "\u8D26\u6237\u72B6\u6001\u53D8\u66F4",
    PERMISSION_CHANGE = "\u6743\u9650\u53D8\u66F4",
    SESSION_TIMEOUT = "\u4F1A\u8BDD\u8D85\u65F6",
    CONCURRENT_LOGIN_LIMIT = "\u5E76\u53D1\u767B\u5F55\u9650\u5236",
    IP_RESTRICTION = "IP\u9650\u5236",
    SUSPICIOUS_ACTIVITY = "\u53EF\u7591\u6D3B\u52A8\u68C0\u6D4B"
}
export declare class PassiveLogoutUtil {
    private static service;
    static setService(service: PassiveLogoutService): void;
    static recordDeviceKickOut(userId: number, newDeviceInfo: string, clientInfo?: any): Promise<void>;
    static recordTokenExpired(userId: number, clientInfo?: any): Promise<void>;
    static recordAdminForceLogout(userId: number, adminId: number, clientInfo?: any): Promise<void>;
    static recordSecurityLogout(userId: number, securityReason: string, clientInfo?: any): Promise<void>;
}
