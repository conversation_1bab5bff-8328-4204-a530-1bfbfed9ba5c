{"version": 3, "file": "user_role.service.js", "sourceRoot": "", "sources": ["../../../src/web/user_role/user_role.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuG;AACvG,6CAAmD;AAGnD,oGAAuF;AACvF,qCAAqC;AAG9B,IAAM,eAAe,GAArB,MAAM,eAAe;IAGhB;IAFV,YAEU,kBAAwC;QAAxC,uBAAkB,GAAlB,kBAAkB,CAAsB;IAC9C,CAAC;IAEL,KAAK,CAAC,MAAM,CAAC,iBAAoC;QAE/C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE;SACxC,CAAC,CAAC;QACH,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC;QACtE,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE;SACxC,CAAC,CAAC;QACH,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC;QACtE,CAAC;QAGD,IAAI,iBAAiB,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACnE,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE;SACnC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,IAAI,EAAE;SAChB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;SACxB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpB,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE;SACnC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,iBAAoC;QAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGxC,IAAI,iBAAiB,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;YACvE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE;aACxC,CAAC,CAAC;YACH,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAGD,IAAI,iBAAiB,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;YACvE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE;aACxC,CAAC,CAAC;YACH,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAGD,IAAI,iBAAiB,CAAC,SAAS,KAAK,CAAC,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAChC,CAAC;QAGD,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,iBAAiB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,4BAAmB,CAAC,kBAAkB,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAC3C,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGxC,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,4BAAmB,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;QAGD,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,4BAAmB,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;QAC3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGxC,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,4BAAmB,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;QAGD,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,4BAAmB,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;QAED,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QACzB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,EAAU;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGxC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,4BAAmB,CAAC,iBAAiB,CAAC,CAAC;QACnD,CAAC;QAGD,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAGD,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;QACvB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;YAC1B,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,SAAgD,EAAE;QAC/D,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QACnC,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,OAAO,EAAE,CAAC;YACZ,EAAE,CAAC,QAAQ,CAAC,oDAAoD,EAAE,EAAE,OAAO,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC,CAAC;QACjG,CAAC;QACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,EAAE,CAAC,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;CACF,CAAA;AA3LY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,2BAAQ,CAAC,CAAA;qCACC,oBAAU;GAH7B,eAAe,CA2L3B"}