import { Repository } from 'typeorm';
import { ActivityEventsTaskService } from 'src/util/database/mysql/activity_events_task/activity_events_task.service';
import { CreateActivityEventsTaskDto } from 'src/util/database/mysql/activity_events_task/dto/create-activity-events-task.dto';
import { UpdateActivityEventsTaskDto } from 'src/util/database/mysql/activity_events_task/dto/update-activity-events-task.dto';
import { SubmitEventsTaskDto } from 'src/util/database/mysql/activity_events_task/dto/submit-events-task.dto';
import { ActivityEventsTask } from 'src/util/database/mysql/activity_events_task/entities/activity_events_task.entity';
export declare class WebEventsTaskService {
    private readonly activityEventsTaskService;
    private readonly activityEventsTaskRepository;
    constructor(activityEventsTaskService: ActivityEventsTaskService, activityEventsTaskRepository: Repository<ActivityEventsTask>);
    createTask(createDto: CreateActivityEventsTaskDto): Promise<ActivityEventsTask>;
    getUserTasks(userId: number): Promise<ActivityEventsTask[]>;
    getActivityTasks(activityId: number): Promise<ActivityEventsTask[]>;
    getTaskDetail(taskId: number, userId?: number): Promise<ActivityEventsTask>;
    updateTask(taskId: number, updateDto: UpdateActivityEventsTaskDto, userId?: number): Promise<ActivityEventsTask>;
    updateTaskStatus(taskId: number, status: number, userId?: number): Promise<ActivityEventsTask>;
    adminReviewTask(taskId: number, status: number, remark?: string): Promise<ActivityEventsTask>;
    deleteTask(taskId: number, userId?: number): Promise<void>;
    getTaskStatistics(userId?: number): Promise<any>;
    getUserTasksByStatus(userId: number, status: number): Promise<ActivityEventsTask[]>;
    searchUserTasks(userId: number, keyword: string): Promise<ActivityEventsTask[]>;
    getUpcomingTasks(userId: number): Promise<ActivityEventsTask[]>;
    getOngoingTasks(userId: number): Promise<ActivityEventsTask[]>;
    batchUpdateTaskStatus(taskIds: number[], status: number, userId?: number): Promise<void>;
    submitTask(taskId: number, submitDto: SubmitEventsTaskDto, userId?: number): Promise<ActivityEventsTask>;
    canSubmitTask(taskId: number, userId?: number): Promise<{
        canSubmit: boolean;
        reason?: string;
        isResubmit?: boolean;
        rejectReason?: string;
    }>;
}
