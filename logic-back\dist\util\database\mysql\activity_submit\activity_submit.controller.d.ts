import { ActivitySubmitService } from './activity_submit.service';
import { CreateActivitySubmitDto } from './dto/create-activity-submit.dto';
import { UpdateActivitySubmitDto } from './dto/update-activity-submit.dto';
import { ActivitySubmit } from './entities/activity_submit.entity';
export declare class ActivitySubmitController {
    private readonly activitySubmitService;
    constructor(activitySubmitService: ActivitySubmitService);
    create(createActivitySubmitDto: CreateActivitySubmitDto): Promise<ActivitySubmit>;
    findAll(): Promise<ActivitySubmit[]>;
    findByActivity(activityId: number): Promise<ActivitySubmit[]>;
    findByUser(userId: number): Promise<ActivitySubmit[]>;
    checkUserSubmit(activityId: number, userId: number): Promise<{
        submitted: boolean;
        submit?: ActivitySubmit;
    }>;
    getStatistics(activityId: number): Promise<{
        total: number;
        submitted: number;
        cancelled: number;
        approved: number;
        rejected: number;
    }>;
    findOne(id: number): Promise<ActivitySubmit>;
    update(id: number, updateActivitySubmitDto: UpdateActivitySubmitDto): Promise<ActivitySubmit>;
    cancel(id: number): Promise<ActivitySubmit>;
    cancelByUser(activityId: number, userId: number): Promise<ActivitySubmit>;
    remove(id: number): Promise<void>;
}
