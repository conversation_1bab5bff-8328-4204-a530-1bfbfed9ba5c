"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiVoiceprintRecognitionService = void 0;
const common_1 = require("@nestjs/common");
const AiExtent_service_1 = require("../util/AiExtent.service");
const queue_service_1 = require("../../util/queue/queue.service");
const scratch_config_service_1 = require("../config/scratch.config.service");
const web_socket_service_1 = require("../../util/web_socket/web_socket.service");
const web_point_service_1 = require("../../web/web_point/web_point.service");
const xunfei_voiceprint_recognition_service_1 = require("../../util/ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.service");
let AiVoiceprintRecognitionService = class AiVoiceprintRecognitionService extends AiExtent_service_1.AiExtentService {
    queueService;
    scratchConfigService;
    webSocketService;
    webPointService;
    voiceprintService;
    constructor(queueService, scratchConfigService, webSocketService, webPointService, voiceprintService) {
        super(queueService);
        this.queueService = queueService;
        this.scratchConfigService = scratchConfigService;
        this.webSocketService = webSocketService;
        this.webPointService = webPointService;
        this.voiceprintService = voiceprintService;
        this.initQueue(this.scratchConfigService.getQueueConfig('voiceprint', 'xunfei'));
    }
    async createVoiceprintGroup(data) {
        return await this.addJob('create-voiceprint-group', data);
    }
    async createVoiceprintFeature(data) {
        return await this.addJob('create-voiceprint-feature', data);
    }
    async compareVoiceprint(data) {
        return await this.addJob('compare-voiceprint', data);
    }
    async searchVoiceprint(data) {
        return await this.addJob('search-voiceprint', data);
    }
    async queryVoiceprintFeatureList(data) {
        return await this.addJob('query-voiceprint-feature-list', data);
    }
    async deleteVoiceprintFeature(data) {
        return await this.addJob('delete-voiceprint-feature', data);
    }
    async updateVoiceprintFeature(data) {
        return await this.addJob('update-voiceprint-feature', data);
    }
    async jobFunction(job) {
        try {
            console.log(`🔄 开始处理声纹识别任务 - JobID: ${job.id}, 类型: ${job.name}`);
            const jobData = job.data;
            const { userId, tabId } = jobData;
            this.webSocketService.sendMessage('voiceprint_recognition_status', {
                status: 'processing',
                message: '正在处理声纹识别请求',
                jobId: job.id
            }, { userId, tabId });
            let result;
            switch (job.name) {
                case 'create-voiceprint-group':
                    const { groupId, groupName, groupInfo } = jobData;
                    result = await this.voiceprintService.createGroup(groupId, groupName, groupInfo || '');
                    break;
                case 'create-voiceprint-feature':
                    const { groupId: featureGroupId, featureId, featureInfo, audioBase64 } = jobData;
                    result = await this.voiceprintService.createFeature(featureGroupId, featureId, featureInfo || '', audioBase64);
                    break;
                case 'compare-voiceprint':
                    const { groupId: compareGroupId, dstFeatureId, audioBase64: compareAudio } = jobData;
                    result = await this.voiceprintService.compareFeature(compareGroupId, dstFeatureId, compareAudio);
                    break;
                case 'search-voiceprint':
                    const { groupId: searchGroupId, audioBase64: searchAudio, topK } = jobData;
                    result = await this.voiceprintService.searchFeature(searchGroupId, searchAudio, topK || 3);
                    break;
                case 'query-voiceprint-feature-list':
                    const { groupId: queryGroupId } = jobData;
                    result = await this.voiceprintService.queryFeatureList(queryGroupId);
                    break;
                case 'delete-voiceprint-feature':
                    const { groupId: deleteGroupId, featureId: deleteFeatureId } = jobData;
                    result = await this.voiceprintService.deleteFeature(deleteGroupId, deleteFeatureId);
                    break;
                case 'update-voiceprint-feature':
                    const { groupId: updateGroupId, featureId: updateFeatureId, featureInfo: updateFeatureInfo, audioBase64: updateAudio } = jobData;
                    result = await this.voiceprintService.updateFeature(updateGroupId, updateFeatureId, updateFeatureInfo || '', updateAudio);
                    break;
                default:
                    throw new Error(`未知的任务类型: ${job.name}`);
            }
            console.log(`✅ 声纹识别任务完成 - JobID: ${job.id}`);
            return {
                success: true,
                result: result.result,
                timestamp: Date.now()
            };
        }
        catch (error) {
            console.error(`❌ 声纹识别任务失败 - JobID: ${job.id}:`, error);
            if (job.data.userId && job.data.tabId) {
                this.webSocketService.sendMessage('voiceprint_recognition_status', {
                    status: 'error',
                    message: `处理失败: ${error.message || '未知错误'}`,
                    jobId: job.id,
                    timestamp: Date.now()
                }, { userId: job.data.userId, tabId: job.data.tabId });
            }
            throw error;
        }
    }
    async successFunction(job) {
        console.log('声纹识别任务成功方法触发');
        const { userId, tabId } = job.data;
        const jobName = job.name;
        const chargeableJobs = {
            'create-voiceprint-group': '创建声纹特征库',
            'compare-voiceprint': '声纹比对',
            'search-voiceprint': '声纹检索'
        };
        if (chargeableJobs[jobName]) {
            try {
                let points = 0;
                if (jobName === 'create-voiceprint-group') {
                    points = this.scratchConfigService.getPointsConfig('voiceprint', 'create').points;
                }
                else if (jobName === 'compare-voiceprint') {
                    points = this.scratchConfigService.getPointsConfig('voiceprint', 'compare').points;
                }
                else if (jobName === 'search-voiceprint') {
                    points = this.scratchConfigService.getPointsConfig('voiceprint', 'search').points;
                }
                const reason = chargeableJobs[jobName];
                await this.webPointService.deductPoints(userId, points, reason);
                console.log(`任务 ${jobName} (${reason}) 扣除积分成功`, points);
            }
            catch (err) {
                console.log(`任务 ${jobName} 扣除积分失败`, err);
            }
        }
        this.webSocketService.sendMessage('voiceprint_recognition_status', {
            status: 'completed',
            message: '任务已完成',
            jobId: job.id,
            result: job.returnvalue,
            timestamp: Date.now()
        }, { userId, tabId });
        this.removeUserTabJobMapping(userId);
    }
    singleFailFunction(job, err) {
    }
    completeFailFunction(job, err) {
        const errorMessage = err ? err.message : '未开始';
        this.webSocketService.sendMessage('voiceprint_recognition_status', {
            status: 'error',
            message: errorMessage,
            jobId: job.id,
            timestamp: Date.now()
        }, { userId: job.data.userId, tabId: job.data.tabId });
        this.removeUserTabJobMapping(job.data.userId);
    }
    insertAddJobCenter(data, job) {
        this.SetMapping(data.userId, data.tabId, job.data.id);
        this.webSocketService.sendMessage('voiceprint_recognition_status', {
            status: 'pending',
            message: '任务加入队列',
            jobId: job.id,
            timestamp: Date.now()
        }, { userId: data.userId, tabId: data.tabId });
    }
    async getJobStatus(jobId) {
        try {
            if (!this.queue) {
                throw new Error('队列未初始化');
            }
            const job = await this.queue.getJob(jobId);
            if (!job) {
                throw new Error('任务不存在');
            }
            const position = await job.getState() === 'waiting' ? await job.position() : 0;
            return {
                ...job.toJSON(),
                position
            };
        }
        catch (error) {
            console.error(`获取任务状态失败: ${error.message}`);
            throw error;
        }
    }
};
exports.AiVoiceprintRecognitionService = AiVoiceprintRecognitionService;
exports.AiVoiceprintRecognitionService = AiVoiceprintRecognitionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [queue_service_1.QueueService,
        scratch_config_service_1.ScratchConfigService,
        web_socket_service_1.WebSocketService,
        web_point_service_1.WebPointService,
        xunfei_voiceprint_recognition_service_1.XunfeiVoiceprintRecognitionService])
], AiVoiceprintRecognitionService);
//# sourceMappingURL=ai_voiceprint_recognition.service.js.map