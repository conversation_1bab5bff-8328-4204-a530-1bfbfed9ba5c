import { MarketplaceService } from '../application/services/marketplace/marketplace.service';
import { GetSeriesListQueryDto } from '../application/dto/marketplace/series-list.dto';
import { GetTagsListQueryDto } from '../application/dto/marketplace/tags-list.dto';
import { CreateTagDto, UpdateTagDto } from '../application/dto/marketplace/tag-management.dto';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
export declare class MarketplaceController {
    private readonly marketplaceService;
    private readonly httpResponseResultService;
    constructor(marketplaceService: MarketplaceService, httpResponseResultService: HttpResponseResultService);
    getSeriesList(query: GetSeriesListQueryDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/series-list.dto").SeriesListDataDto>>;
    getSeriesDetail(seriesId: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<null> | import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/series-detail.dto").SeriesDetailDataDto>>;
    getCourseDetail(seriesId: number, courseId: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<null> | import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/course-detail.dto").CourseDetailDataDto>>;
    getTagsList(query: GetTagsListQueryDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/tags-list.dto").TagsListDataDto>>;
    createTag(createTagDto: CreateTagDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<null> | import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/tag-management.dto").TagDetailDataDto>>;
    updateTag(id: number, updateTagDto: UpdateTagDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<null> | import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/tag-management.dto").TagDetailDataDto>>;
    deleteTag(id: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<null>>;
    getTagById(id: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<null> | import("../../http_response_result/http-response.interface").HttpResponse<import("../application/dto/marketplace/tag-management.dto").TagDetailDataDto>>;
}
