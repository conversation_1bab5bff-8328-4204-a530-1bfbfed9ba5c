import { CreateStudentSelfAssessmentSubmissionDto } from './dto/create-student_self_assessment_submission.dto';
import { UpdateStudentSelfAssessmentSubmissionDto } from './dto/update-student_self_assessment_submission.dto';
import { StudentSelfAssessmentSubmission } from './entities/student_self_assessment_submission.entity';
import { EntityManager, Repository } from 'typeorm';
import { TaskSelfAssessmentItem } from '../task_self_assessment_item/entities/task_self_assessment_item.entity';
import { CreateBulkStudentSelfAssessmentSubmissionDto } from './dto/create-bulk-student_self_assessment_submission.dto';
import { UserInfo } from 'src/util/database/mysql/user_info/entities/user_info.entity';
export declare class StudentSelfAssessmentSubmissionService {
    private submissionRepository;
    private itemRepository;
    private userInfoRepository;
    private entityManager;
    constructor(submissionRepository: Repository<StudentSelfAssessmentSubmission>, itemRepository: Repository<TaskSelfAssessmentItem>, userInfoRepository: Repository<UserInfo>, entityManager: EntityManager);
    createBulk(createBulkDto: CreateBulkStudentSelfAssessmentSubmissionDto): Promise<{
        success: boolean;
        message: string;
    }>;
    create(createStudentSelfAssessmentSubmissionDto: CreateStudentSelfAssessmentSubmissionDto): Promise<StudentSelfAssessmentSubmission>;
    findAll(): Promise<StudentSelfAssessmentSubmission[]>;
    findOne(id: number): Promise<StudentSelfAssessmentSubmission | null>;
    update(id: number, updateStudentSelfAssessmentSubmissionDto: UpdateStudentSelfAssessmentSubmissionDto): Promise<import("typeorm").UpdateResult>;
    remove(id: number): Promise<import("typeorm").DeleteResult>;
    findByAssignmentAndStudent(assignmentId: number, studentId: number): Promise<StudentSelfAssessmentSubmission[]>;
    findByAssessmentItemIdWithStudentInfo(assessmentItemId: number): Promise<{
        score: number;
        studentId: number;
        studentName: string;
        avatarUrl: string | undefined;
    }[]>;
}
