"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitySubmitModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const activity_submit_service_1 = require("./activity_submit.service");
const activity_submit_controller_1 = require("./activity_submit.controller");
const activity_submit_entity_1 = require("./entities/activity_submit.entity");
const activity_entity_1 = require("../activity/entities/activity.entity");
let ActivitySubmitModule = class ActivitySubmitModule {
};
exports.ActivitySubmitModule = ActivitySubmitModule;
exports.ActivitySubmitModule = ActivitySubmitModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([activity_submit_entity_1.ActivitySubmit, activity_entity_1.Activity])],
        controllers: [activity_submit_controller_1.ActivitySubmitController],
        providers: [activity_submit_service_1.ActivitySubmitService],
        exports: [activity_submit_service_1.ActivitySubmitService],
    })
], ActivitySubmitModule);
//# sourceMappingURL=activity_submit.module.js.map