'use client';

import React, { useState, useEffect } from 'react';
import { X, Settings, Plus, BookOpen, FileText } from 'lucide-react';
import { courseManagementApi } from '@/lib/api/course-management';
import { courseApi } from '@/lib/api/course';
import { Select } from 'antd';
import './CourseListEditModal.css';

interface CourseItem {
  id: number;
  seriesId: number;
  title: string;
  description: string;
  coverImage: string;
  orderIndex: number;
  status: number;
  statusLabel: string;
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  videoDuration: number;
  videoDurationLabel: string;
  videoName: string;
  firstTeachingTitle: string;
  resourcesCount: number;
  createdAt: string;
  updatedAt: string;
}

interface CourseTag {
  id: number;
  name: string;
  color: string;
  category: number;
  description: string;
  status: number;
}

interface ApiResponse {
  code: number;
  message: string;
  data: {
    list: CourseItem[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

interface CourseListEditModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  seriesTitle: string;
  seriesCoverImage?: string;
  seriesId: number;
}

// API调用函数
const fetchCourseList = async (seriesId: number): Promise<ApiResponse> => {
  return await courseManagementApi.getSeriesCourses(seriesId);
};

// 获取课程标签
const fetchCourseTags = async (): Promise<any> => {
  try {
    console.log('🔍 开始调用 courseApi.getCourseTags');
    const result = await courseApi.getCourseTags({
      page: 1,
      pageSize: 100,
      status: 1 // 只获取启用的标签
    });
    console.log('🔍 courseApi.getCourseTags 返回结果:', result);
    return result;
  } catch (error) {
    console.error('🔍 courseApi.getCourseTags 调用失败:', error);
    throw error;
  }
};

const CourseListEditModal: React.FC<CourseListEditModalProps> = ({
  isVisible,
  onClose,
  onSave,
  seriesTitle,
  seriesCoverImage,
  seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId
}) => {
  const [courseList, setCourseList] = useState<CourseItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [rightPanelType, setRightPanelType] = useState<'none' | 'settings' | 'course'>('none');
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);

  const [editingTitle, setEditingTitle] = useState(seriesTitle);
  const [courseGoals, setCourseGoals] = useState('');
  const [courseObjectives, setCourseObjectives] = useState('');
  const [projectMembers, setProjectMembers] = useState('');

  // 课程标签相关状态
  const [courseTags, setCourseTags] = useState<CourseTag[]>([]);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [tagsLoading, setTagsLoading] = useState(false);

  // 课程详细编辑状态
  const [courseDetail, setCourseDetail] = useState({
    title: '',
    description: '',
    coverImage: '',
    videoUrl: '',
    videoName: '',
    isVideoEnabled: false,
    attachmentUrl: '',
    attachmentName: '',
    isAttachmentEnabled: false,
    teachingMaterials: [] as { type: string; name: string }[],
    // 支持teachingInfo结构
    teachingInfo: [] as { title: string; content: string[] }[],
    // 支持contentConfig结构
    contentConfig: {
      hasVideo: 0,
      hasDocument: 0,
      hasAudio: 0,
      video: { url: '', name: '' },
      document: { url: '', name: '' },
      audio: { url: '', name: '' }
    },
    courseContent: {
      topic: '',
      content: ''
    },
    isOneKeyOpen: false,
    isDistributionEnabled: false,
    distributionReward: '',
    selectedTemplate: '',
    isDistributionWater: false,
    requiredEnergy: '',
    energyAmount: '',
    isDistributionLimit: false,
    distributionConditions: {
      inviteCount: '',
      taskCount: '',
      experience: ''
    },
    isDistributionTime: false,
    distributionTimeConditions: {
      startTime: '',
      endTime: ''
    },
    distributionMaterials: [] as { type: string; name: string }[],
    // 任务配置相关状态
    taskConfig: {
      taskName: '',
      taskDuration: '',
      taskDescription: '',
      selfAssessmentItems: [''],
      referenceWorks: [] as { type: string; name: string }[],
      referenceResources: [] as { type: string; name: string; url?: string }[]
    }
  });

  // 获取课程列表数据
  useEffect(() => {
    if (isVisible && seriesId) {
      // 检查用户登录状态
      const token = localStorage.getItem('token');
      console.log('🔐 检查登录状态，token存在:', !!token);
      console.log('🔍 seriesId:', seriesId);

      if (!token) {
        console.error('❌ 用户未登录，无法获取课程列表');
        // 设置空列表，显示空状态
        setCourseList([]);
        setLoading(false);
        return;
      }

      loadCourseList();
      loadCourseTags();
    }
  }, [isVisible, seriesId]);

  const loadCourseList = async () => {
    try {
      setLoading(true);
      console.log('🔍 开始加载课程列表，seriesId:', seriesId);

      const response = await fetchCourseList(seriesId);
      console.log('📡 API响应:', response);

      if (response.code === 200) {
        console.log('✅ 课程列表数据:', response.data);
        const courses = response.data.courses || response.data.list || [];
        console.log('✅ 解析的课程数组:', courses);
        setCourseList(courses);
      } else {
        console.error('❌ API返回错误:', response);
        setCourseList([]);
      }
    } catch (error: any) {
      console.error('❌ 加载课程列表失败:', error);

      // 检查是否是认证错误
      if (error.response?.status === 401) {
        console.error('🔐 认证失败，用户未登录或token已过期');
      } else if (error.response?.status === 403) {
        console.error('🚫 权限不足，无法访问该系列课程');
      } else if (error.response?.status === 404) {
        console.error('📭 系列课程不存在，seriesId:', seriesId);
      } else {
        console.error('🔧 其他错误:', error.message);
      }

      setCourseList([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载课程标签
  const loadCourseTags = async () => {
    try {
      setTagsLoading(true);
      console.log('🏷️ 开始加载课程标签');



      const response = await fetchCourseTags();
      console.log('📡 标签API完整响应:', response);

      // 检查响应结构
      if (response && response.data) {
        console.log('📊 响应数据:', response.data);

        let tags: CourseTag[] = [];

        // 处理标准的API响应格式 (response.data.list) - 根据实际API响应
        if (response.data.list && Array.isArray(response.data.list)) {
          tags = response.data.list;
          console.log('✅ 从 data.list 解析到标签:', tags.length, '个');
        }
        // 处理直接数组格式 (response.data)
        else if (Array.isArray(response.data)) {
          tags = response.data;
          console.log('✅ 从 data 数组解析到标签:', tags.length, '个');
        }
        // 处理嵌套的API响应格式 (response.data.data.list) - 备用格式
        else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {
          tags = response.data.data.list;
          console.log('✅ 从 data.data.list 解析到标签:', tags.length, '个');
        }

        // 验证标签数据格式
        console.log('🔍 原始标签数据:', tags);
        console.log('🔍 标签数据类型检查:');
        tags.forEach((tag, index) => {
          console.log(`标签${index}:`, {
            tag,
            hasTag: !!tag,
            idType: typeof tag?.id,
            nameType: typeof tag?.name,
            nameValue: tag?.name,
            nameNotEmpty: tag?.name?.trim() !== ''
          });
        });

        const validTags = tags.filter(tag => {
          const isValid = tag &&
            typeof tag.id === 'number' &&
            typeof tag.name === 'string' &&
            tag.name.trim() !== '';

          if (!isValid) {
            console.log('❌ 无效标签:', tag, {
              hasTag: !!tag,
              idType: typeof tag?.id,
              nameType: typeof tag?.name,
              nameValue: tag?.name
            });
          }

          return isValid;
        });

        console.log('✅ 有效标签数量:', validTags.length);
        console.log('✅ 有效标签详情:', validTags);

        if (validTags.length > 0) {
          setCourseTags(validTags);
          console.log('✅ 成功设置真实标签数据');
          return;
        } else {
          console.warn('⚠️ 没有有效的标签数据');
        }
      } else {
        console.warn('⚠️ API响应格式不正确:', response);
      }

      // 如果没有真实数据，设置空数组
      console.log('📭 没有标签数据，设置空数组');
      setCourseTags([]);

    } catch (error: any) {
      console.error('❌ 加载课程标签失败:', error);
      console.error('❌ 错误详情:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      // 发生错误时设置空数组
      setCourseTags([]);
    } finally {
      setTagsLoading(false);
    }
  };

  // 添加新课程
  const addNewCourse = () => {
    const newCourse: CourseItem = {
      id: Date.now(),
      seriesId: seriesId,
      title: `第${courseList.length + 1}课 - 新课时`,
      description: '',
      coverImage: '',
      orderIndex: courseList.length + 1,
      status: 0,
      statusLabel: '草稿',
      hasVideo: 0,
      hasDocument: 0,
      hasAudio: 0,
      videoDuration: 0,
      videoDurationLabel: '',
      videoName: '',
      firstTeachingTitle: '',
      resourcesCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setCourseList([...courseList, newCourse]);
    // 自动选中新添加的课程
    showCoursePanel(newCourse.id);
  };

  // 删除课程
  const deleteCourse = (id: number) => {
    setCourseList(courseList.filter(course => course.id !== id));
  };

  // 更新课程标题
  const updateCourseTitle = (id: number, newTitle: string) => {
    setCourseList(courseList.map(course =>
      course.id === id ? { ...course, title: newTitle } : course
    ));
  };

  // 处理课程封面上传
  const handleCoverUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        alert('请选择 JPG、PNG 或 GIF 格式的图片文件');
        return;
      }

      // 检查文件大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过 10MB');
        return;
      }

      // 创建预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        setCourseDetail(prev => ({ ...prev, coverImage: imageUrl }));

        // 同时更新课程列表中的封面
        if (selectedCourseId) {
          setCourseList(prev => prev.map(course =>
            course.id === selectedCourseId ? { ...course, coverImage: imageUrl } : course
          ));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // 处理视频上传
  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv'];
      if (!allowedTypes.includes(file.type)) {
        alert('请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件');
        return;
      }

      // 检查文件大小 (100MB)
      if (file.size > 100 * 1024 * 1024) {
        alert('视频文件大小不能超过 100MB');
        return;
      }

      // 创建视频URL
      const videoUrl = URL.createObjectURL(file);

      // 更新课程详情中的视频信息
      setCourseDetail(prev => ({
        ...prev,
        contentConfig: {
          ...prev.contentConfig,
          video: {
            url: videoUrl,
            name: file.name
          }
        }
      }));

      // 同时更新课程列表中的视频信息
      if (selectedCourseId) {
        setCourseList(prev => prev.map(course =>
          course.id === selectedCourseId ? {
            ...course,
            contentConfig: {
              ...course.contentConfig,
              video: {
                url: videoUrl,
                name: file.name
              }
            }
          } : course
        ));
      }
    }
  };

  // 触发视频文件选择
  const triggerVideoUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'video/mp4,video/avi,video/mov,video/wmv,video/flv';
    input.onchange = handleVideoUpload;
    input.click();
  };

  // 保存课程列表
  const handleSave = () => {
    const data = {
      title: editingTitle,
      courseGoals,
      courseObjectives,
      courseList
    };
    onSave(data);
    onClose();
  };

  // 发布系列课程
  const handlePublish = () => {
    // TODO: 实现发布逻辑
    alert('发布系列课程功能待实现');
  };

  // 退出编辑模式 - 保存数据并关闭
  const handleExitEdit = () => {
    handleSave();
  };

  // 显示设置面板
  const showSettingsPanel = () => {
    setRightPanelType('settings');
    setSelectedCourseId(null);
  };

  // 显示课程编辑面板
  const showCoursePanel = (courseId: number) => {
    setRightPanelType('course');
    setSelectedCourseId(courseId);

    // 获取选中的课程并更新courseDetail状态
    const selectedCourse = courseList.find(course => course.id === courseId);
    if (selectedCourse) {
      // 模拟从API获取的课程详细数据
      const mockCourseData = {
        contentConfig: {
          hasVideo: selectedCourse.hasVideo || 0,
          hasDocument: selectedCourse.hasDocument || 0,
          hasAudio: selectedCourse.hasAudio || 0,
          video: {
            url: selectedCourse.hasVideo ? "https://logicleap.oss-cn-guangzhou.aliyuncs.com/other/1753771868850.mp4" : "",
            name: selectedCourse.hasVideo ? "1a0ccd3271b0777274a06be6925fc283.mp4" : ""
          },
          document: {
            url: selectedCourse.hasDocument ? "https://example.com/document.pdf" : "",
            name: selectedCourse.hasDocument ? "课程文档.pdf" : ""
          },
          audio: {
            url: "",
            name: ""
          }
        },
        teachingInfo: [
          {
            title: "教学目标",
            content: ["醐"]
          }
        ]
      };

      setCourseDetail(prev => ({
        ...prev,
        title: selectedCourse.title,
        description: selectedCourse.description,
        coverImage: selectedCourse.coverImage || '',
        isVideoEnabled: selectedCourse.hasVideo === 1,
        isAttachmentEnabled: selectedCourse.hasDocument === 1,
        contentConfig: mockCourseData.contentConfig,
        teachingInfo: mockCourseData.teachingInfo,
        videoUrl: mockCourseData.contentConfig.video.url,
        videoName: mockCourseData.contentConfig.video.name,
        attachmentUrl: mockCourseData.contentConfig.document.url,
        attachmentName: mockCourseData.contentConfig.document.name
      }));
    }
  };

  // 获取选中的课程
  const getSelectedCourse = () => {
    return courseList.find(course => course.id === selectedCourseId);
  };

  if (!isVisible) return null;

  return (
    <div className="course-list-modal-overlay">
      <div className="course-list-modal">
        {/* 头部 */}
        <div className="course-list-header">
          <div className="course-list-title-section">
            <h2 className="course-list-title">课程列表</h2>
            <div className="course-list-actions">
              <button
                onClick={showSettingsPanel}
                className={`course-list-settings-btn ${rightPanelType === 'settings' ? 'active' : ''}`}
              >
                <Settings className="w-4 h-4" />
              </button>
              <button onClick={addNewCourse} className="course-list-add-btn">
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>
          <button onClick={onClose} className="course-list-close-btn">
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 主要内容 */}
        <div className="course-list-content">
          {/* 左侧课程列表 */}
          <div className="course-list-sidebar">
            <div className="course-list-items">
              {loading ? (
                <div className="course-list-loading">
                  <p>加载中...</p>
                </div>
              ) : courseList.length === 0 ? (
                <div className="course-list-empty">
                  <div className="course-list-empty-icon">
                    <BookOpen className="w-12 h-12 text-gray-300" />
                  </div>
                  <h3 className="course-list-empty-title">暂无课时</h3>
                  <p className="course-list-empty-description">
                    点击右上角的 + 按钮添加第一个课时
                  </p>
                  <button
                    onClick={addNewCourse}
                    className="course-list-empty-btn"
                  >
                    <Plus className="w-4 h-4" />
                    添加课时
                  </button>
                </div>
              ) : (
                courseList.map((course) => (
                  <div
                    key={course.id}
                    className={`course-list-item ${selectedCourseId === course.id ? 'active' : ''}`}
                    onClick={() => showCoursePanel(course.id)}
                  >
                    <span className="course-list-item-text">{course.title}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteCourse(course.id);
                      }}
                      className="course-list-item-delete"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 右侧编辑区域 */}
          <div className="course-list-edit-area">
            {rightPanelType === 'none' && (
              <div className="course-edit-empty">
                <div className="course-edit-empty-icon">
                  <FileText className="w-16 h-16 text-gray-300" />
                </div>
                <h3 className="course-edit-empty-title">无课程详情</h3>
                <p className="course-edit-empty-description">
                  点击左侧课程或设置按钮查看详情
                </p>
              </div>
            )}

            {rightPanelType === 'settings' && (
              <>
                {/* 系列课程封面 */}
                <div className="course-series-cover">
                  {seriesCoverImage ? (
                    <img
                      src={seriesCoverImage}
                      alt="系列课程封面"
                      className="course-series-cover-image"
                    />
                  ) : (
                    <div className="course-series-cover-placeholder">
                      <span>系列课程封面</span>
                    </div>
                  )}
                </div>

                {/* 系列设置表单 */}
                <div className="course-edit-form">
                  {/* 系列课程标题 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">系列课程标题</label>
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      className="course-edit-input"
                      placeholder="请输入系列课程标题"
                    />
                  </div>

                  {/* 课程标签 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程标签</label>
                    <Select
                      mode="multiple"
                      style={{ width: '100%' }}
                      placeholder="请选择课程标签"
                      value={selectedTags}
                      onChange={setSelectedTags}
                      loading={tagsLoading}
                      options={courseTags.map(tag => {
                        console.log('🏷️ 渲染标签选项:', tag);
                        return {
                          label: (
                            <span style={{ color: tag.color }}>
                              {tag.name}
                            </span>
                          ),
                          value: tag.id
                        };
                      })}
                    />
                    {/* 调试信息 */}
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                      调试: 当前标签数量 {courseTags.length}, 加载状态: {tagsLoading ? '是' : '否'}
                    </div>
                  </div>

                  {/* 课程项目成员 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程项目成员</label>
                    <input
                      type="text"
                      value={projectMembers}
                      onChange={(e) => setProjectMembers(e.target.value)}
                      className="course-edit-input"
                      placeholder="请输入项目成员，如：张老师、李助教、王同学"
                    />
                  </div>
                </div>
              </>
            )}

            {rightPanelType === 'course' && getSelectedCourse() && (
              <>
                {/* 课程详细编辑界面 */}
                <div className="course-detail-edit">
                  {/* 顶部区域：课程封面和基本信息 */}
                  <div className="course-detail-top">
                    <div className="course-detail-cover">
                      <div
                        className="course-cover-upload-area"
                        onClick={() => document.getElementById('cover-upload-input')?.click()}
                      >
                        {courseDetail.coverImage || getSelectedCourse()?.coverImage ? (
                          <img
                            src={courseDetail.coverImage || getSelectedCourse()?.coverImage}
                            alt="课程封面"
                            className="course-cover-image"
                          />
                        ) : (
                          <div className="course-cover-placeholder">
                            <span>点击上传课程封面</span>
                          </div>
                        )}
                      </div>
                      <input
                        id="cover-upload-input"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/gif"
                        onChange={handleCoverUpload}
                        style={{ display: 'none' }}
                      />
                    </div>
                    <div className="course-detail-basic">
                      <div className="course-detail-field">
                        <label>课程标题</label>
                        <input
                          type="text"
                          value={courseDetail.title || getSelectedCourse()?.title || ''}
                          onChange={(e) => {
                            setCourseDetail(prev => ({ ...prev, title: e.target.value }));
                            updateCourseTitle(selectedCourseId!, e.target.value);
                          }}
                          placeholder="请输入课程标题"
                        />
                      </div>
                      <div className="course-detail-field">
                        <label>课程介绍</label>
                        <textarea
                          value={courseDetail.description || getSelectedCourse()?.description || ''}
                          onChange={(e) => setCourseDetail(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="请输入课程介绍"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 课程资源区域 */}
                  <div className="course-detail-section">
                    <h3>课程资源</h3>

                    {/* 课程视频 */}
                    <div className="course-resource-item">
                      <div className="resource-header-right">
                        <span>课程视频</span>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={courseDetail.isVideoEnabled}
                            onChange={(e) => setCourseDetail(prev => ({ ...prev, isVideoEnabled: e.target.checked }))}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>
                      {courseDetail.isVideoEnabled && (
                        <div className="video-content-area">
                          {/* 显示真实视频信息 */}
                          {courseDetail.contentConfig?.video?.url ? (
                            <div className="video-info-section">
                              <div className="video-preview">
                                <video
                                  className="video-thumbnail"
                                  controls
                                  poster={courseDetail.coverImage}
                                >
                                  <source src={courseDetail.contentConfig.video.url} type="video/mp4" />
                                  您的浏览器不支持视频播放
                                </video>
                              </div>
                              <div className="video-name-centered">{courseDetail.contentConfig.video.name}</div>
                              <button className="upload-btn-horizontal" onClick={triggerVideoUpload}>
                                <span>重新上传</span>
                              </button>
                            </div>
                          ) : (
                            <div className="video-upload-section">
                              <div className="video-placeholder-centered">
                                <div className="play-icon">▶</div>
                              </div>
                              <button className="upload-btn-horizontal" onClick={triggerVideoUpload}>
                                <span>上传视频</span>
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* 课程附件 */}
                    <div className="course-resource-item">
                      <div className="resource-header-right">
                        <span>课程附件</span>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={courseDetail.isAttachmentEnabled}
                            onChange={(e) => setCourseDetail(prev => ({ ...prev, isAttachmentEnabled: e.target.checked }))}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>
                      {courseDetail.isAttachmentEnabled && (
                        <div className="attachment-content-area">
                          {/* 显示真实附件信息 */}
                          {courseDetail.contentConfig?.document?.url ? (
                            <div className="attachment-info-section">
                              <div className="attachment-preview">
                                <div className="document-icon">📄</div>
                                <div className="attachment-details">
                                  <div className="attachment-name">{courseDetail.contentConfig.document.name}</div>
                                  <div className="attachment-url">{courseDetail.contentConfig.document.url}</div>
                                </div>
                              </div>
                              <button className="upload-btn">重新上传</button>
                            </div>
                          ) : (
                            <div className="attachment-area">
                              <input
                                type="text"
                                value={courseDetail.attachmentUrl}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, attachmentUrl: e.target.value }))}
                                placeholder="第一课课后作业练习.pdf"
                              />
                              <button className="upload-btn">上传附件</button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* 教学附件 */}
                    <div className="course-resource-item">
                      <div className="resource-header-simple">
                        <span>教学附件</span>
                      </div>
                      <div className="teaching-materials">
                        <button className="add-material-btn">
                          <span>+</span>
                          <span>上传</span>
                        </button>
                        <div className="material-item">
                          <span>教案.pdf</span>
                        </div>
                        <div className="material-item">
                          <span>学习计划.pdf</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 课程内容区域 */}
                  <div className="course-detail-section">
                    <h3>课程内容</h3>
                    <div className="course-content-area">
                      {/* 显示teachingInfo数据 */}
                      {courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? (
                        courseDetail.teachingInfo.map((info, index) => (
                          <div key={index} className="teaching-info-item">
                            <div className="content-field">
                              <label>{info.title}</label>
                              <div className="teaching-content">
                                {info.content.map((contentItem, contentIndex) => (
                                  <div key={contentIndex} className="content-item">
                                    <input
                                      type="text"
                                      value={contentItem}
                                      onChange={(e) => {
                                        const newTeachingInfo = [...courseDetail.teachingInfo];
                                        newTeachingInfo[index].content[contentIndex] = e.target.value;
                                        setCourseDetail(prev => ({
                                          ...prev,
                                          teachingInfo: newTeachingInfo
                                        }));
                                      }}
                                      placeholder="请输入内容"
                                    />
                                  </div>
                                ))}
                                <button
                                  className="add-content-btn"
                                  onClick={() => {
                                    const newTeachingInfo = [...courseDetail.teachingInfo];
                                    newTeachingInfo[index].content.push('');
                                    setCourseDetail(prev => ({
                                      ...prev,
                                      teachingInfo: newTeachingInfo
                                    }));
                                  }}
                                >
                                  + 添加内容项
                                </button>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="content-field">
                          <label>教学目标</label>
                          <input
                            type="text"
                            value=""
                            onChange={(e) => {
                              setCourseDetail(prev => ({
                                ...prev,
                                teachingInfo: [{
                                  title: "教学目标",
                                  content: [e.target.value]
                                }]
                              }));
                            }}
                            placeholder="请输入教学目标"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 重新上课 */}
                  <div className="course-detail-section">
                    <div className="one-key-section">
                      <div className="one-key-item">
                        <span>重新上课</span>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={courseDetail.isOneKeyOpen}
                            onChange={(e) => setCourseDetail(prev => ({ ...prev, isOneKeyOpen: e.target.checked }))}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>

                      {courseDetail.isOneKeyOpen && (
                        <>
                          <div className="one-key-item">
                            <span>分配积木</span>
                            <label className="switch">
                              <input
                                type="checkbox"
                                checked={courseDetail.isDistributionEnabled}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, isDistributionEnabled: e.target.checked }))}
                              />
                              <span className="slider"></span>
                            </label>
                            {courseDetail.isDistributionEnabled && (
                              <div className="block-template-section">
                                <button className="select-template-btn">
                                  选择积木模板
                                </button>
                                <div className="selected-template-display">
                                  <span>{courseDetail.selectedTemplate || '选中的模板名字'}</span>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="one-key-item">
                            <span>分配能量</span>
                            <label className="switch">
                              <input
                                type="checkbox"
                                checked={courseDetail.isDistributionWater}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, isDistributionWater: e.target.checked }))}
                              />
                              <span className="slider"></span>
                            </label>
                          </div>

                          {courseDetail.isDistributionWater && (
                            <div className="energy-input-section">
                              <span>需要能量：</span>
                              <input
                                type="text"
                                value={courseDetail.requiredEnergy || ''}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, requiredEnergy: e.target.value }))}
                                placeholder="请输入需要的能量值"
                                className="energy-input"
                              />
                            </div>
                          )}

                          <div className="one-key-item">
                            <span>分配任务</span>
                            <label className="switch">
                              <input
                                type="checkbox"
                                checked={courseDetail.isDistributionLimit}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, isDistributionLimit: e.target.checked }))}
                              />
                              <span className="slider"></span>
                            </label>
                          </div>

                          {/* 任务配置表单 */}
                          {courseDetail.isDistributionLimit && (
                            <div className="task-config-form">
                              {/* 任务名称和持续天数 */}
                              <div className="task-config-row">
                                <div className="task-config-field">
                                  <label>任务名称:</label>
                                  <input
                                    type="text"
                                    value={courseDetail.taskConfig.taskName}
                                    onChange={(e) => setCourseDetail(prev => ({
                                      ...prev,
                                      taskConfig: { ...prev.taskConfig, taskName: e.target.value }
                                    }))}
                                    placeholder="请输入任务名称"
                                  />
                                </div>
                                <div className="task-config-field">
                                  <label>任务持续天数:</label>
                                  <input
                                    type="number"
                                    value={courseDetail.taskConfig.taskDuration}
                                    onChange={(e) => setCourseDetail(prev => ({
                                      ...prev,
                                      taskConfig: { ...prev.taskConfig, taskDuration: e.target.value }
                                    }))}
                                    placeholder="请输入天数"
                                  />
                                </div>
                              </div>

                              {/* 任务描述 */}
                              <div className="task-config-field task-config-full">
                                <label>任务描述:</label>
                                <textarea
                                  value={courseDetail.taskConfig.taskDescription}
                                  onChange={(e) => setCourseDetail(prev => ({
                                    ...prev,
                                    taskConfig: { ...prev.taskConfig, taskDescription: e.target.value }
                                  }))}
                                  placeholder="请输入任务描述"
                                  rows={4}
                                />
                              </div>

                              {/* 任务自评项 */}
                              <div className="task-config-field task-config-full">
                                <label>任务自评项: <span className="item-number">{courseDetail.taskConfig.selfAssessmentItems.length}</span></label>
                                {courseDetail.taskConfig.selfAssessmentItems.map((item, index) => (
                                  <div key={index} className="self-assessment-item">
                                    <input
                                      type="text"
                                      value={item}
                                      onChange={(e) => {
                                        const newItems = [...courseDetail.taskConfig.selfAssessmentItems];
                                        newItems[index] = e.target.value;
                                        setCourseDetail(prev => ({
                                          ...prev,
                                          taskConfig: { ...prev.taskConfig, selfAssessmentItems: newItems }
                                        }));
                                      }}
                                      placeholder="请输入自评项内容"
                                    />
                                  </div>
                                ))}
                                <button
                                  type="button"
                                  className="add-assessment-btn"
                                  onClick={() => setCourseDetail(prev => ({
                                    ...prev,
                                    taskConfig: {
                                      ...prev.taskConfig,
                                      selfAssessmentItems: [...prev.taskConfig.selfAssessmentItems, '']
                                    }
                                  }))}
                                >
                                  +
                                </button>
                              </div>

                              {/* 任务参考作品 */}
                              <div className="task-config-field task-config-full">
                                <label>任务参考作品:</label>
                                <div className="reference-works-section">
                                  <button type="button" className="select-works-btn">选择作品</button>
                                  <div className="reference-works-grid">
                                    {courseDetail.taskConfig.referenceWorks.map((work, index) => (
                                      <div key={index} className="reference-work-item">
                                        <span>{work.name || '作品'}</span>
                                      </div>
                                    ))}
                                    {/* 空白占位框 */}
                                    {Array.from({ length: Math.max(0, 3 - courseDetail.taskConfig.referenceWorks.length) }).map((_, index) => (
                                      <div key={`empty-${index}`} className="reference-work-item empty"></div>
                                    ))}
                                  </div>
                                </div>
                              </div>

                              {/* 任务参考资源 */}
                              <div className="task-config-field task-config-full">
                                <label>任务参考资源:</label>
                                <div className="reference-resources-section">
                                  <div className="reference-resources-grid">
                                    <button
                                      type="button"
                                      className="upload-resource-btn"
                                      onClick={() => {
                                        // 触发文件上传
                                        const input = document.createElement('input');
                                        input.type = 'file';
                                        input.accept = '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif';
                                        input.onchange = (e) => {
                                          const file = (e.target as HTMLInputElement).files?.[0];
                                          if (file) {
                                            setCourseDetail(prev => ({
                                              ...prev,
                                              taskConfig: {
                                                ...prev.taskConfig,
                                                referenceResources: [
                                                  ...prev.taskConfig.referenceResources,
                                                  { type: 'file', name: file.name }
                                                ]
                                              }
                                            }));
                                          }
                                        };
                                        input.click();
                                      }}
                                    >
                                      <Plus size={24} />
                                      上传
                                    </button>
                                    {courseDetail.taskConfig.referenceResources.map((resource, index) => (
                                      <div key={index} className="reference-resource-item">
                                        <span>{resource.name}</span>
                                        <button
                                          type="button"
                                          className="remove-resource-btn"
                                          onClick={() => {
                                            const newResources = courseDetail.taskConfig.referenceResources.filter((_, i) => i !== index);
                                            setCourseDetail(prev => ({
                                              ...prev,
                                              taskConfig: { ...prev.taskConfig, referenceResources: newResources }
                                            }));
                                          }}
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="course-list-footer">
          <div className="course-list-footer-left">
            <button onClick={handlePublish} className="course-list-btn course-list-btn-publish">
              发布系列课程
            </button>
          </div>
          <div className="course-list-footer-right">
            <button onClick={handleExitEdit} className="course-list-btn course-list-btn-exit">
              退出编辑模式
            </button>
            <button onClick={handleSave} className="course-list-btn course-list-btn-save">
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseListEditModal;
