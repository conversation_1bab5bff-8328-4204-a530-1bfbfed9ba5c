"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityAuditService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_audit_entity_1 = require("./entities/activity_audit.entity");
let ActivityAuditService = class ActivityAuditService {
    activityAuditRepository;
    constructor(activityAuditRepository) {
        this.activityAuditRepository = activityAuditRepository;
    }
    async create(createActivityAuditDto) {
        const activityAudit = this.activityAuditRepository.create(createActivityAuditDto);
        return this.activityAuditRepository.save(activityAudit);
    }
    async findAll() {
        return this.activityAuditRepository.find({
            where: { isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findOne(id) {
        const activityAudit = await this.activityAuditRepository.findOne({
            where: { id, isDelete: false }
        });
        if (!activityAudit) {
            throw new common_1.NotFoundException(`活动审核记录ID ${id} 未找到`);
        }
        return activityAudit;
    }
    async update(id, updateActivityAuditDto) {
        await this.activityAuditRepository.update(id, updateActivityAuditDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.activityAuditRepository.update(id, { isDelete: true });
    }
    async hardRemove(id) {
        await this.activityAuditRepository.delete(id);
    }
    async findByActivityId(activityId) {
        return this.activityAuditRepository.find({
            where: { activityId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByAuditorId(auditorId) {
        return this.activityAuditRepository.find({
            where: { auditorId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByResult(result) {
        return this.activityAuditRepository.find({
            where: { result, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
};
exports.ActivityAuditService = ActivityAuditService;
exports.ActivityAuditService = ActivityAuditService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_audit_entity_1.ActivityAudit)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ActivityAuditService);
//# sourceMappingURL=activity_audit.service.js.map