"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_AIBackground_tsx"],{

/***/ "(app-pages-browser)/./components/AIBackground.tsx":
/*!*************************************!*\
  !*** ./components/AIBackground.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIBackground; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AIBackground() {\n    _s();\n    var _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)();\n    const customEase = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.cubicBezier)(0.45, 0.05, 0.55, 0.95);\n    const [circles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const smallCircles = [\n            ...Array(20)\n        ].map(()=>({\n                x: Math.random() * 100,\n                y: Math.random() * 100,\n                r: Math.random() * 4 + 3,\n                delay: Math.random() * 3,\n                duration: Math.random() * 6 + 6,\n                moveRange: Math.random() * 60 + 40,\n                floatRange: Math.random() * 200 + 150\n            }));\n        const largeCircles = [\n            ...Array(8)\n        ].map(()=>({\n                x: Math.random() * 100,\n                y: Math.random() * 100,\n                r: Math.random() * 8 + 6,\n                delay: Math.random() * 3,\n                duration: Math.random() * 8 + 8,\n                moveRange: Math.random() * 80 + 60,\n                floatRange: Math.random() * 300 + 200\n            }));\n        return {\n            small: smallCircles,\n            large: largeCircles\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none z-[-1] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white\"\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                width: \"100%\",\n                height: \"100%\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                                id: \"smallGrid\",\n                                width: \"20\",\n                                height: \"20\",\n                                patternUnits: \"userSpaceOnUse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 20 0 L 0 0 0 20\",\n                                    fill: \"none\",\n                                    stroke: \"rgba(71, 102, 194, 0.12)\",\n                                    strokeWidth: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                                id: \"largeGrid\",\n                                width: \"100\",\n                                height: \"100\",\n                                patternUnits: \"userSpaceOnUse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 100 0 L 0 0 0 100\",\n                                    fill: \"none\",\n                                    stroke: \"rgba(71, 102, 194, 0.12)\",\n                                    strokeWidth: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"100%\",\n                        height: \"100%\",\n                        fill: \"url(#smallGrid)\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"100%\",\n                        height: \"100%\",\n                        fill: \"url(#largeGrid)\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    circles.small.map(_s1((circle, i)=>{\n                        _s1();\n                        const scrollY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n                            0,\n                            1\n                        ], [\n                            0,\n                            circle.moveRange\n                        ], {\n                            ease: customEase\n                        });\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.g, {\n                            style: {\n                                translateY: scrollY\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.circle, {\n                                cx: \"\".concat(circle.x, \"%\"),\n                                cy: \"\".concat(circle.y, \"%\"),\n                                r: circle.r,\n                                fill: \"#4766C2\",\n                                style: {\n                                    opacity: 0.15\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.15,\n                                        0.4,\n                                        0.15\n                                    ],\n                                    scale: [\n                                        1,\n                                        1.3,\n                                        1\n                                    ],\n                                    translateY: [\n                                        -circle.floatRange,\n                                        circle.floatRange,\n                                        -circle.floatRange\n                                    ]\n                                },\n                                transition: {\n                                    opacity: {\n                                        duration: circle.duration,\n                                        repeat: Infinity,\n                                        repeatType: \"mirror\",\n                                        ease: \"easeInOut\",\n                                        delay: circle.delay\n                                    },\n                                    scale: {\n                                        duration: circle.duration * 1.2,\n                                        repeat: Infinity,\n                                        repeatType: \"mirror\",\n                                        ease: \"easeInOut\",\n                                        delay: circle.delay\n                                    },\n                                    translateY: {\n                                        duration: circle.duration * 1.8,\n                                        repeat: Infinity,\n                                        repeatType: \"mirror\",\n                                        ease: \"easeInOut\"\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this);\n                    }, \"niIoVA1L5NBdf6VtT2eYHMt9qfo=\", false, function() {\n                        return [\n                            framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform\n                        ];\n                    })),\n                    circles.large.map(_s2((circle, i)=>{\n                        _s2();\n                        const scrollY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n                            0,\n                            1\n                        ], [\n                            0,\n                            circle.moveRange\n                        ], {\n                            ease: customEase\n                        });\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.g, {\n                            style: {\n                                translateY: scrollY\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.circle, {\n                                cx: \"\".concat(circle.x, \"%\"),\n                                cy: \"\".concat(circle.y, \"%\"),\n                                r: circle.r,\n                                fill: \"#4766C2\",\n                                style: {\n                                    opacity: 0.1\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.1,\n                                        0.3,\n                                        0.1\n                                    ],\n                                    scale: [\n                                        1,\n                                        1.4,\n                                        1\n                                    ],\n                                    translateY: [\n                                        -circle.floatRange,\n                                        circle.floatRange,\n                                        -circle.floatRange\n                                    ]\n                                },\n                                transition: {\n                                    opacity: {\n                                        duration: circle.duration,\n                                        repeat: Infinity,\n                                        repeatType: \"mirror\",\n                                        ease: \"easeInOut\",\n                                        delay: circle.delay\n                                    },\n                                    scale: {\n                                        duration: circle.duration * 1.5,\n                                        repeat: Infinity,\n                                        repeatType: \"mirror\",\n                                        ease: \"easeInOut\",\n                                        delay: circle.delay\n                                    },\n                                    translateY: {\n                                        duration: circle.duration * 2.2,\n                                        repeat: Infinity,\n                                        repeatType: \"mirror\",\n                                        ease: \"easeInOut\"\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this)\n                        }, \"large-\".concat(i), false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this);\n                    }, \"niIoVA1L5NBdf6VtT2eYHMt9qfo=\", false, function() {\n                        return [\n                            framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform\n                        ];\n                    }))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\components\\\\AIBackground.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(AIBackground, \"PyFZkhtrrpNSas4jMAkcnbU2Hfk=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll\n    ];\n});\n_c = AIBackground;\nvar _c;\n$RefreshReg$(_c, \"AIBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AIBackground.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: function() { return /* binding */ resizeElement; }\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if (target instanceof SVGElement && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    var _a;\n    (_a = resizeHandlers.get(target)) === null || _a === void 0 ? void 0 : _a.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer === null || observer === void 0 ? void 0 : observer.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.delete(handler);\n            if (!(elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.size)) {\n                observer === null || observer === void 0 ? void 0 : observer.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: function() { return /* binding */ resizeWindow; }\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const size = {\n            width: window.innerWidth,\n            height: window.innerHeight,\n        };\n        const info = {\n            target: window,\n            size,\n            contentSize: size,\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size && windowResizeHandler) {\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLXdpbmRvdy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2hhbmRsZS13aW5kb3cubWpzPzhlZjIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgd2luZG93Q2FsbGJhY2tzID0gbmV3IFNldCgpO1xubGV0IHdpbmRvd1Jlc2l6ZUhhbmRsZXI7XG5mdW5jdGlvbiBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCkge1xuICAgIHdpbmRvd1Jlc2l6ZUhhbmRsZXIgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHNpemUgPSB7XG4gICAgICAgICAgICB3aWR0aDogd2luZG93LmlubmVyV2lkdGgsXG4gICAgICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodCxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaW5mbyA9IHtcbiAgICAgICAgICAgIHRhcmdldDogd2luZG93LFxuICAgICAgICAgICAgc2l6ZSxcbiAgICAgICAgICAgIGNvbnRlbnRTaXplOiBzaXplLFxuICAgICAgICB9O1xuICAgICAgICB3aW5kb3dDYWxsYmFja3MuZm9yRWFjaCgoY2FsbGJhY2spID0+IGNhbGxiYWNrKGluZm8pKTtcbiAgICB9O1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIHdpbmRvd1Jlc2l6ZUhhbmRsZXIpO1xufVxuZnVuY3Rpb24gcmVzaXplV2luZG93KGNhbGxiYWNrKSB7XG4gICAgd2luZG93Q2FsbGJhY2tzLmFkZChjYWxsYmFjayk7XG4gICAgaWYgKCF3aW5kb3dSZXNpemVIYW5kbGVyKVxuICAgICAgICBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgd2luZG93Q2FsbGJhY2tzLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgICAgIGlmICghd2luZG93Q2FsbGJhY2tzLnNpemUgJiYgd2luZG93UmVzaXplSGFuZGxlcikge1xuICAgICAgICAgICAgd2luZG93UmVzaXplSGFuZGxlciA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgIH07XG59XG5cbmV4cG9ydCB7IHJlc2l6ZVdpbmRvdyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: function() { return /* binding */ resize; }\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUNGOztBQUVuRDtBQUNBLHFDQUFxQyxnRUFBWSxNQUFNLGtFQUFhO0FBQ3BFOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2luZGV4Lm1qcz81Yzc2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc2l6ZUVsZW1lbnQgfSBmcm9tICcuL2hhbmRsZS1lbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyByZXNpemVXaW5kb3cgfSBmcm9tICcuL2hhbmRsZS13aW5kb3cubWpzJztcblxuZnVuY3Rpb24gcmVzaXplKGEsIGIpIHtcbiAgICByZXR1cm4gdHlwZW9mIGEgPT09IFwiZnVuY3Rpb25cIiA/IHJlc2l6ZVdpbmRvdyhhKSA6IHJlc2l6ZUVsZW1lbnQoYSwgYik7XG59XG5cbmV4cG9ydCB7IHJlc2l6ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scroll: function() { return /* binding */ scroll; }\n/* harmony export */ });\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _observe_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./observe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs\");\n/* harmony import */ var _supports_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supports.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/supports.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n\n\n\n\n\nfunction scrollTimelineFallback({ source, container, axis = \"y\", }) {\n    // Support legacy source argument. Deprecate later.\n    if (source)\n        container = source;\n    // ScrollTimeline records progress as a percentage CSSUnitValue\n    const currentTime = { value: 0 };\n    const cancel = (0,_track_mjs__WEBPACK_IMPORTED_MODULE_1__.scrollInfo)((info) => {\n        currentTime.value = info[axis].progress * 100;\n    }, { container, axis });\n    return { currentTime, cancel };\n}\nconst timelineCache = new Map();\nfunction getTimeline({ source, container = document.documentElement, axis = \"y\", } = {}) {\n    // Support legacy source argument. Deprecate later.\n    if (source)\n        container = source;\n    if (!timelineCache.has(container)) {\n        timelineCache.set(container, {});\n    }\n    const elementCache = timelineCache.get(container);\n    if (!elementCache[axis]) {\n        elementCache[axis] = (0,_supports_mjs__WEBPACK_IMPORTED_MODULE_2__.supportsScrollTimeline)()\n            ? new ScrollTimeline({ source: container, axis })\n            : scrollTimelineFallback({ source: container, axis });\n    }\n    return elementCache[axis];\n}\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\n/**\n * Currently, we only support element tracking with `scrollInfo`, though in\n * the future we can also offer ViewTimeline support.\n */\nfunction needsElementTracking(options) {\n    return options && (options.target || options.offset);\n}\nfunction scrollFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll) || needsElementTracking(options)) {\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_1__.scrollInfo)((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return (0,_observe_mjs__WEBPACK_IMPORTED_MODULE_3__.observeTimeline)(onScroll, getTimeline(options));\n    }\n}\nfunction scrollAnimation(animation, options) {\n    animation.flatten();\n    if (needsElementTracking(options)) {\n        animation.pause();\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_1__.scrollInfo)((info) => {\n            animation.time = animation.duration * info[options.axis].progress;\n        }, options);\n    }\n    else {\n        const timeline = getTimeline(options);\n        if (animation.attachTimeline) {\n            return animation.attachTimeline(timeline, (valueAnimation) => {\n                valueAnimation.pause();\n                return (0,_observe_mjs__WEBPACK_IMPORTED_MODULE_3__.observeTimeline)((progress) => {\n                    valueAnimation.time = valueAnimation.duration * progress;\n                }, timeline);\n            });\n        }\n        else {\n            return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n        }\n    }\n}\nfunction scroll(onScroll, { axis = \"y\", ...options } = {}) {\n    const optionsWithDefaults = { axis, ...options };\n    return typeof onScroll === \"function\"\n        ? scrollFunction(onScroll, optionsWithDefaults)\n        : scrollAnimation(onScroll, optionsWithDefaults);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXlDO0FBQ087QUFDUTtBQUNwQjs7QUFFcEMsa0NBQWtDLGdDQUFnQztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQixtQkFBbUIsc0RBQVU7QUFDN0I7QUFDQSxLQUFLLElBQUksaUJBQWlCO0FBQzFCLGFBQWE7QUFDYjtBQUNBO0FBQ0EsdUJBQXVCLDREQUE0RCxJQUFJO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixxRUFBc0I7QUFDbkQsbUNBQW1DLHlCQUF5QjtBQUM1RCx1Q0FBdUMseUJBQXlCO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzREFBVTtBQUN6QjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZUFBZSw2REFBZTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHNEQUFVO0FBQ3pCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw2REFBZTtBQUN0QztBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBLG1CQUFtQiw4Q0FBSTtBQUN2QjtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIseUJBQXlCLElBQUk7QUFDekQsa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL2luZGV4Lm1qcz9hZDdlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNjcm9sbEluZm8gfSBmcm9tICcuL3RyYWNrLm1qcyc7XG5pbXBvcnQgeyBvYnNlcnZlVGltZWxpbmUgfSBmcm9tICcuL29ic2VydmUubWpzJztcbmltcG9ydCB7IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgfSBmcm9tICcuL3N1cHBvcnRzLm1qcyc7XG5pbXBvcnQgeyBub29wIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcblxuZnVuY3Rpb24gc2Nyb2xsVGltZWxpbmVGYWxsYmFjayh7IHNvdXJjZSwgY29udGFpbmVyLCBheGlzID0gXCJ5XCIsIH0pIHtcbiAgICAvLyBTdXBwb3J0IGxlZ2FjeSBzb3VyY2UgYXJndW1lbnQuIERlcHJlY2F0ZSBsYXRlci5cbiAgICBpZiAoc291cmNlKVxuICAgICAgICBjb250YWluZXIgPSBzb3VyY2U7XG4gICAgLy8gU2Nyb2xsVGltZWxpbmUgcmVjb3JkcyBwcm9ncmVzcyBhcyBhIHBlcmNlbnRhZ2UgQ1NTVW5pdFZhbHVlXG4gICAgY29uc3QgY3VycmVudFRpbWUgPSB7IHZhbHVlOiAwIH07XG4gICAgY29uc3QgY2FuY2VsID0gc2Nyb2xsSW5mbygoaW5mbykgPT4ge1xuICAgICAgICBjdXJyZW50VGltZS52YWx1ZSA9IGluZm9bYXhpc10ucHJvZ3Jlc3MgKiAxMDA7XG4gICAgfSwgeyBjb250YWluZXIsIGF4aXMgfSk7XG4gICAgcmV0dXJuIHsgY3VycmVudFRpbWUsIGNhbmNlbCB9O1xufVxuY29uc3QgdGltZWxpbmVDYWNoZSA9IG5ldyBNYXAoKTtcbmZ1bmN0aW9uIGdldFRpbWVsaW5lKHsgc291cmNlLCBjb250YWluZXIgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQsIGF4aXMgPSBcInlcIiwgfSA9IHt9KSB7XG4gICAgLy8gU3VwcG9ydCBsZWdhY3kgc291cmNlIGFyZ3VtZW50LiBEZXByZWNhdGUgbGF0ZXIuXG4gICAgaWYgKHNvdXJjZSlcbiAgICAgICAgY29udGFpbmVyID0gc291cmNlO1xuICAgIGlmICghdGltZWxpbmVDYWNoZS5oYXMoY29udGFpbmVyKSkge1xuICAgICAgICB0aW1lbGluZUNhY2hlLnNldChjb250YWluZXIsIHt9KTtcbiAgICB9XG4gICAgY29uc3QgZWxlbWVudENhY2hlID0gdGltZWxpbmVDYWNoZS5nZXQoY29udGFpbmVyKTtcbiAgICBpZiAoIWVsZW1lbnRDYWNoZVtheGlzXSkge1xuICAgICAgICBlbGVtZW50Q2FjaGVbYXhpc10gPSBzdXBwb3J0c1Njcm9sbFRpbWVsaW5lKClcbiAgICAgICAgICAgID8gbmV3IFNjcm9sbFRpbWVsaW5lKHsgc291cmNlOiBjb250YWluZXIsIGF4aXMgfSlcbiAgICAgICAgICAgIDogc2Nyb2xsVGltZWxpbmVGYWxsYmFjayh7IHNvdXJjZTogY29udGFpbmVyLCBheGlzIH0pO1xuICAgIH1cbiAgICByZXR1cm4gZWxlbWVudENhY2hlW2F4aXNdO1xufVxuLyoqXG4gKiBJZiB0aGUgb25TY3JvbGwgZnVuY3Rpb24gaGFzIHR3byBhcmd1bWVudHMsIGl0J3MgZXhwZWN0aW5nXG4gKiBtb3JlIHNwZWNpZmljIGluZm9ybWF0aW9uIGFib3V0IHRoZSBzY3JvbGwgZnJvbSBzY3JvbGxJbmZvLlxuICovXG5mdW5jdGlvbiBpc09uU2Nyb2xsV2l0aEluZm8ob25TY3JvbGwpIHtcbiAgICByZXR1cm4gb25TY3JvbGwubGVuZ3RoID09PSAyO1xufVxuLyoqXG4gKiBDdXJyZW50bHksIHdlIG9ubHkgc3VwcG9ydCBlbGVtZW50IHRyYWNraW5nIHdpdGggYHNjcm9sbEluZm9gLCB0aG91Z2ggaW5cbiAqIHRoZSBmdXR1cmUgd2UgY2FuIGFsc28gb2ZmZXIgVmlld1RpbWVsaW5lIHN1cHBvcnQuXG4gKi9cbmZ1bmN0aW9uIG5lZWRzRWxlbWVudFRyYWNraW5nKG9wdGlvbnMpIHtcbiAgICByZXR1cm4gb3B0aW9ucyAmJiAob3B0aW9ucy50YXJnZXQgfHwgb3B0aW9ucy5vZmZzZXQpO1xufVxuZnVuY3Rpb24gc2Nyb2xsRnVuY3Rpb24ob25TY3JvbGwsIG9wdGlvbnMpIHtcbiAgICBpZiAoaXNPblNjcm9sbFdpdGhJbmZvKG9uU2Nyb2xsKSB8fCBuZWVkc0VsZW1lbnRUcmFja2luZyhvcHRpb25zKSkge1xuICAgICAgICByZXR1cm4gc2Nyb2xsSW5mbygoaW5mbykgPT4ge1xuICAgICAgICAgICAgb25TY3JvbGwoaW5mb1tvcHRpb25zLmF4aXNdLnByb2dyZXNzLCBpbmZvKTtcbiAgICAgICAgfSwgb3B0aW9ucyk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gb2JzZXJ2ZVRpbWVsaW5lKG9uU2Nyb2xsLCBnZXRUaW1lbGluZShvcHRpb25zKSk7XG4gICAgfVxufVxuZnVuY3Rpb24gc2Nyb2xsQW5pbWF0aW9uKGFuaW1hdGlvbiwgb3B0aW9ucykge1xuICAgIGFuaW1hdGlvbi5mbGF0dGVuKCk7XG4gICAgaWYgKG5lZWRzRWxlbWVudFRyYWNraW5nKG9wdGlvbnMpKSB7XG4gICAgICAgIGFuaW1hdGlvbi5wYXVzZSgpO1xuICAgICAgICByZXR1cm4gc2Nyb2xsSW5mbygoaW5mbykgPT4ge1xuICAgICAgICAgICAgYW5pbWF0aW9uLnRpbWUgPSBhbmltYXRpb24uZHVyYXRpb24gKiBpbmZvW29wdGlvbnMuYXhpc10ucHJvZ3Jlc3M7XG4gICAgICAgIH0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgdGltZWxpbmUgPSBnZXRUaW1lbGluZShvcHRpb25zKTtcbiAgICAgICAgaWYgKGFuaW1hdGlvbi5hdHRhY2hUaW1lbGluZSkge1xuICAgICAgICAgICAgcmV0dXJuIGFuaW1hdGlvbi5hdHRhY2hUaW1lbGluZSh0aW1lbGluZSwgKHZhbHVlQW5pbWF0aW9uKSA9PiB7XG4gICAgICAgICAgICAgICAgdmFsdWVBbmltYXRpb24ucGF1c2UoKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gb2JzZXJ2ZVRpbWVsaW5lKChwcm9ncmVzcykgPT4ge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi50aW1lID0gdmFsdWVBbmltYXRpb24uZHVyYXRpb24gKiBwcm9ncmVzcztcbiAgICAgICAgICAgICAgICB9LCB0aW1lbGluZSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBub29wO1xuICAgICAgICB9XG4gICAgfVxufVxuZnVuY3Rpb24gc2Nyb2xsKG9uU2Nyb2xsLCB7IGF4aXMgPSBcInlcIiwgLi4ub3B0aW9ucyB9ID0ge30pIHtcbiAgICBjb25zdCBvcHRpb25zV2l0aERlZmF1bHRzID0geyBheGlzLCAuLi5vcHRpb25zIH07XG4gICAgcmV0dXJuIHR5cGVvZiBvblNjcm9sbCA9PT0gXCJmdW5jdGlvblwiXG4gICAgICAgID8gc2Nyb2xsRnVuY3Rpb24ob25TY3JvbGwsIG9wdGlvbnNXaXRoRGVmYXVsdHMpXG4gICAgICAgIDogc2Nyb2xsQW5pbWF0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKTtcbn1cblxuZXhwb3J0IHsgc2Nyb2xsIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: function() { return /* binding */ createScrollInfo; },\n/* harmony export */   updateScrollInfo: function() { return /* binding */ updateScrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _utils_progress_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,_utils_progress_mjs__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeTimeline: function() { return /* binding */ observeTimeline; }\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.update(onFrame, true);\n    return () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(onFrame);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2JzZXJ2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0U7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixjQUFjO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSx1REFBSztBQUNULGlCQUFpQixpRUFBVztBQUM1Qjs7QUFFMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3Njcm9sbC9vYnNlcnZlLm1qcz8yMTliIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZyYW1lLCBjYW5jZWxGcmFtZSB9IGZyb20gJy4uLy4uLy4uL2ZyYW1lbG9vcC9mcmFtZS5tanMnO1xuXG5mdW5jdGlvbiBvYnNlcnZlVGltZWxpbmUodXBkYXRlLCB0aW1lbGluZSkge1xuICAgIGxldCBwcmV2UHJvZ3Jlc3M7XG4gICAgY29uc3Qgb25GcmFtZSA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgeyBjdXJyZW50VGltZSB9ID0gdGltZWxpbmU7XG4gICAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBjdXJyZW50VGltZSA9PT0gbnVsbCA/IDAgOiBjdXJyZW50VGltZS52YWx1ZTtcbiAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSBwZXJjZW50YWdlIC8gMTAwO1xuICAgICAgICBpZiAocHJldlByb2dyZXNzICE9PSBwcm9ncmVzcykge1xuICAgICAgICAgICAgdXBkYXRlKHByb2dyZXNzKTtcbiAgICAgICAgfVxuICAgICAgICBwcmV2UHJvZ3Jlc3MgPSBwcm9ncmVzcztcbiAgICB9O1xuICAgIGZyYW1lLnVwZGF0ZShvbkZyYW1lLCB0cnVlKTtcbiAgICByZXR1cm4gKCkgPT4gY2FuY2VsRnJhbWUob25GcmFtZSk7XG59XG5cbmV4cG9ydCB7IG9ic2VydmVUaW1lbGluZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: function() { return /* binding */ namedEdges; },\n/* harmony export */   resolveEdge: function() { return /* binding */ resolveEdge; }\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: function() { return /* binding */ resolveOffsets; }\n/* harmony export */ });\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../utils/offsets/default.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,_utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition));\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = info[axis].interpolate(info[axis].current);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: function() { return /* binding */ calcInset; }\n/* harmony export */ });\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (current instanceof HTMLElement) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: function() { return /* binding */ resolveOffset; }\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: function() { return /* binding */ ScrollOffset; }\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL29mZnNldHMvcHJlc2V0cy5tanM/ODE2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: function() { return /* binding */ createOnScrollHandler; }\n/* harmony export */ });\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: () => measure(element, options.target, info),\n        update: (time) => {\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: function() { return /* binding */ scrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../resize/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.documentElement, ...options } = {}) {\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_0__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(measureAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(updateAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(notifyAll, false, true);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,_resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(listener, false, true);\n    return () => {\n        var _a;\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/transform.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: function() { return /* binding */ transform; }\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n\n\nconst isCustomValueType = (v) => {\n    return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = (v) => (isCustomValueType(v) ? v.mix : undefined);\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, {\n        mixer: getMixer(outputRange[0]),\n        ...options,\n    });\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw2REFBVztBQUNwQztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcz84YjhiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludGVycG9sYXRlIH0gZnJvbSAnLi9pbnRlcnBvbGF0ZS5tanMnO1xuXG5jb25zdCBpc0N1c3RvbVZhbHVlVHlwZSA9ICh2KSA9PiB7XG4gICAgcmV0dXJuIHYgJiYgdHlwZW9mIHYgPT09IFwib2JqZWN0XCIgJiYgdi5taXg7XG59O1xuY29uc3QgZ2V0TWl4ZXIgPSAodikgPT4gKGlzQ3VzdG9tVmFsdWVUeXBlKHYpID8gdi5taXggOiB1bmRlZmluZWQpO1xuZnVuY3Rpb24gdHJhbnNmb3JtKC4uLmFyZ3MpIHtcbiAgICBjb25zdCB1c2VJbW1lZGlhdGUgPSAhQXJyYXkuaXNBcnJheShhcmdzWzBdKTtcbiAgICBjb25zdCBhcmdPZmZzZXQgPSB1c2VJbW1lZGlhdGUgPyAwIDogLTE7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGFyZ3NbMCArIGFyZ09mZnNldF07XG4gICAgY29uc3QgaW5wdXRSYW5nZSA9IGFyZ3NbMSArIGFyZ09mZnNldF07XG4gICAgY29uc3Qgb3V0cHV0UmFuZ2UgPSBhcmdzWzIgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG9wdGlvbnMgPSBhcmdzWzMgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGludGVycG9sYXRvciA9IGludGVycG9sYXRlKGlucHV0UmFuZ2UsIG91dHB1dFJhbmdlLCB7XG4gICAgICAgIG1peGVyOiBnZXRNaXhlcihvdXRwdXRSYW5nZVswXSksXG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgfSk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: function() { return /* binding */ useCombineMotionValues; }\n/* harmony export */ });\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: function() { return /* binding */ useComputed; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    _index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(_index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    _index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDZ0I7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS91c2UtY29tcHV0ZWQubWpzPzE0YjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJy4vaW5kZXgubWpzJztcbmltcG9ydCB7IHVzZUNvbWJpbmVNb3Rpb25WYWx1ZXMgfSBmcm9tICcuL3VzZS1jb21iaW5lLXZhbHVlcy5tanMnO1xuXG5mdW5jdGlvbiB1c2VDb21wdXRlZChjb21wdXRlKSB7XG4gICAgLyoqXG4gICAgICogT3BlbiBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuIEFueSBNb3Rpb25WYWx1ZSB0aGF0IGNhbGxzIGdldCgpXG4gICAgICogd2lsbCBiZSBzYXZlZCBpbnRvIHRoaXMgYXJyYXkuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gW107XG4gICAgY29tcHV0ZSgpO1xuICAgIGNvbnN0IHZhbHVlID0gdXNlQ29tYmluZU1vdGlvblZhbHVlcyhjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQsIGNvbXB1dGUpO1xuICAgIC8qKlxuICAgICAqIFN5bmNocm9ub3VzbHkgY2xvc2Ugc2Vzc2lvbiBvZiBjb2xsZWN0TW90aW9uVmFsdWVzLlxuICAgICAqL1xuICAgIGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCA9IHVuZGVmaW5lZDtcbiAgICByZXR1cm4gdmFsdWU7XG59XG5cbmV4cG9ydCB7IHVzZUNvbXB1dGVkIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: function() { return /* binding */ useMotionValue; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: function() { return /* binding */ useScroll; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__.scroll)((_progress, { x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: function() { return /* binding */ useTransform; }\n/* harmony export */ });\n/* harmony import */ var _utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXRyYW5zZm9ybS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUQ7QUFDZTtBQUNWO0FBQ1A7O0FBRWpEO0FBQ0E7QUFDQSxlQUFlLDhEQUFXO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLFVBQVUsK0RBQVM7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixvRUFBVztBQUM5QixXQUFXLCtFQUFzQjtBQUNqQztBQUNBO0FBQ0Esd0JBQXdCLGVBQWU7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3VzZS10cmFuc2Zvcm0ubWpzPzQyNDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHJhbnNmb3JtIH0gZnJvbSAnLi4vdXRpbHMvdHJhbnNmb3JtLm1qcyc7XG5pbXBvcnQgeyB1c2VDb21iaW5lTW90aW9uVmFsdWVzIH0gZnJvbSAnLi91c2UtY29tYmluZS12YWx1ZXMubWpzJztcbmltcG9ydCB7IHVzZUNvbnN0YW50IH0gZnJvbSAnLi4vdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyc7XG5pbXBvcnQgeyB1c2VDb21wdXRlZCB9IGZyb20gJy4vdXNlLWNvbXB1dGVkLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZVRyYW5zZm9ybShpbnB1dCwgaW5wdXRSYW5nZU9yVHJhbnNmb3JtZXIsIG91dHB1dFJhbmdlLCBvcHRpb25zKSB7XG4gICAgaWYgKHR5cGVvZiBpbnB1dCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIHJldHVybiB1c2VDb21wdXRlZChpbnB1dCk7XG4gICAgfVxuICAgIGNvbnN0IHRyYW5zZm9ybWVyID0gdHlwZW9mIGlucHV0UmFuZ2VPclRyYW5zZm9ybWVyID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgPyBpbnB1dFJhbmdlT3JUcmFuc2Zvcm1lclxuICAgICAgICA6IHRyYW5zZm9ybShpbnB1dFJhbmdlT3JUcmFuc2Zvcm1lciwgb3V0cHV0UmFuZ2UsIG9wdGlvbnMpO1xuICAgIHJldHVybiBBcnJheS5pc0FycmF5KGlucHV0KVxuICAgICAgICA/IHVzZUxpc3RUcmFuc2Zvcm0oaW5wdXQsIHRyYW5zZm9ybWVyKVxuICAgICAgICA6IHVzZUxpc3RUcmFuc2Zvcm0oW2lucHV0XSwgKFtsYXRlc3RdKSA9PiB0cmFuc2Zvcm1lcihsYXRlc3QpKTtcbn1cbmZ1bmN0aW9uIHVzZUxpc3RUcmFuc2Zvcm0odmFsdWVzLCB0cmFuc2Zvcm1lcikge1xuICAgIGNvbnN0IGxhdGVzdCA9IHVzZUNvbnN0YW50KCgpID0+IFtdKTtcbiAgICByZXR1cm4gdXNlQ29tYmluZU1vdGlvblZhbHVlcyh2YWx1ZXMsICgpID0+IHtcbiAgICAgICAgbGF0ZXN0Lmxlbmd0aCA9IDA7XG4gICAgICAgIGNvbnN0IG51bVZhbHVlcyA9IHZhbHVlcy5sZW5ndGg7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtVmFsdWVzOyBpKyspIHtcbiAgICAgICAgICAgIGxhdGVzdFtpXSA9IHZhbHVlc1tpXS5nZXQoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdHJhbnNmb3JtZXIobGF0ZXN0KTtcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgdXNlVHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ })

}]);