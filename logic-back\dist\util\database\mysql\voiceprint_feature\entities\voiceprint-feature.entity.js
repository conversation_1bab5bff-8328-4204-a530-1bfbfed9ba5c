"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintFeature = void 0;
const typeorm_1 = require("typeorm");
const voiceprint_group_entity_1 = require("../../voiceprint_group/entities/voiceprint_group.entity");
let VoiceprintFeature = class VoiceprintFeature {
    id;
    groupId;
    featureId;
    userId;
    name;
    createdAt;
    updatedAt;
    group;
};
exports.VoiceprintFeature = VoiceprintFeature;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], VoiceprintFeature.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'group_id', length: 100 }),
    __metadata("design:type", String)
], VoiceprintFeature.prototype, "groupId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'feature_id', length: 100 }),
    __metadata("design:type", String)
], VoiceprintFeature.prototype, "featureId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', length: 36 }),
    __metadata("design:type", String)
], VoiceprintFeature.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name', length: 100 }),
    __metadata("design:type", String)
], VoiceprintFeature.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], VoiceprintFeature.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], VoiceprintFeature.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => voiceprint_group_entity_1.VoiceprintGroupEntity, group => group.features),
    (0, typeorm_1.JoinColumn)({ name: 'group_id', referencedColumnName: 'groupId' }),
    __metadata("design:type", voiceprint_group_entity_1.VoiceprintGroupEntity)
], VoiceprintFeature.prototype, "group", void 0);
exports.VoiceprintFeature = VoiceprintFeature = __decorate([
    (0, typeorm_1.Entity)('voiceprint_feature')
], VoiceprintFeature);
//# sourceMappingURL=voiceprint-feature.entity.js.map