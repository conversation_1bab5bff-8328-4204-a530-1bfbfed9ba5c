import { UserPointsOfflineMessageService } from './user_points_offline_message.service';
import { CreateUserPointsOfflineMessageDto } from './dto/create-user_points_offline_message.dto';
import { UpdateUserPointsOfflineMessageDto } from './dto/update-user_points_offline_message.dto';
import { UserPointsOfflineMessage } from './entities/user_points_offline_message.entity';
export declare class UserPointsOfflineMessageController {
    private readonly userPointsOfflineMessageService;
    constructor(userPointsOfflineMessageService: UserPointsOfflineMessageService);
    create(createUserPointsOfflineMessageDto: CreateUserPointsOfflineMessageDto): Promise<UserPointsOfflineMessage>;
    findAll(): Promise<UserPointsOfflineMessage[]>;
    findByUserId(userId: string): Promise<UserPointsOfflineMessage[]>;
    findPendingMessages(): Promise<UserPointsOfflineMessage[]>;
    findOne(id: string): Promise<UserPointsOfflineMessage>;
    update(id: string, updateUserPointsOfflineMessageDto: UpdateUserPointsOfflineMessageDto): Promise<UserPointsOfflineMessage>;
    markAsSent(id: string): Promise<UserPointsOfflineMessage>;
    remove(id: string): Promise<void>;
}
