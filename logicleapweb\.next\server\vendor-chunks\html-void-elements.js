"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-void-elements";
exports.ids = ["vendor-chunks/html-void-elements"];
exports.modules = {

/***/ "(ssr)/./node_modules/html-void-elements/index.js":
/*!**************************************************!*\
  !*** ./node_modules/html-void-elements/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlVoidElements: () => (/* binding */ htmlVoidElements)\n/* harmony export */ });\n/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nconst htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'keygen',\n  'link',\n  'meta',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC12b2lkLWVsZW1lbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9odG1sLXZvaWQtZWxlbWVudHMvaW5kZXguanM/Mzg3MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExpc3Qgb2YgSFRNTCB2b2lkIHRhZyBuYW1lcy5cbiAqXG4gKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IGh0bWxWb2lkRWxlbWVudHMgPSBbXG4gICdhcmVhJyxcbiAgJ2Jhc2UnLFxuICAnYmFzZWZvbnQnLFxuICAnYmdzb3VuZCcsXG4gICdicicsXG4gICdjb2wnLFxuICAnY29tbWFuZCcsXG4gICdlbWJlZCcsXG4gICdmcmFtZScsXG4gICdocicsXG4gICdpbWFnZScsXG4gICdpbWcnLFxuICAnaW5wdXQnLFxuICAna2V5Z2VuJyxcbiAgJ2xpbmsnLFxuICAnbWV0YScsXG4gICdwYXJhbScsXG4gICdzb3VyY2UnLFxuICAndHJhY2snLFxuICAnd2JyJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-void-elements/index.js\n");

/***/ })

};
;