export declare class GetTeachingRecordsQueryDto {
    page?: number;
    pageSize?: number;
    teacherId?: number;
    courseId?: number;
    classId?: number;
    status?: number;
    startDate?: string;
    endDate?: string;
}
export declare class TeachingRecordInfoDto {
    id: number;
    courseId: number;
    courseName: string;
    seriesName: string;
    classId: number;
    className: string;
    teacherId: number;
    teacherName: string;
    status: number;
    statusLabel: string;
    pointsAllocated: number;
    tasksCreated: number;
    templateApplied: number;
    lockAcquireTime: number;
    totalExecutionTime: number;
    errorMessage: string;
    createdAt: string;
    updatedAt: string;
}
export declare class PaginationInfoDto {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export declare class TeachingRecordsDataDto {
    list: TeachingRecordInfoDto[];
    pagination: PaginationInfoDto;
}
export declare class TeachingRecordsResponseDto {
    code: number;
    message: string;
    data: TeachingRecordsDataDto;
}
