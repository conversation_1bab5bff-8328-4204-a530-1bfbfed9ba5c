"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserWorkLikeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_work_like_service_1 = require("./user_work_like.service");
const create_user_work_like_dto_1 = require("./dto/create-user_work_like.dto");
const update_user_work_like_dto_1 = require("./dto/update-user_work_like.dto");
const user_work_like_entity_1 = require("./entities/user_work_like.entity");
let UserWorkLikeController = class UserWorkLikeController {
    userWorkLikeService;
    constructor(userWorkLikeService) {
        this.userWorkLikeService = userWorkLikeService;
    }
    create(createUserWorkLikeDto) {
        return this.userWorkLikeService.create(createUserWorkLikeDto);
    }
    findAll() {
        return this.userWorkLikeService.findAll();
    }
    findByUserId(userId) {
        return this.userWorkLikeService.findByUserId(+userId);
    }
    findByTargetId(targetId, targetType) {
        return this.userWorkLikeService.findByTargetId(+targetId, +targetType);
    }
    async checkLike(userId, targetId, targetType) {
        const like = await this.userWorkLikeService.findByUserIdAndTargetId(+userId, +targetId, +targetType);
        if (!like) {
            throw new common_1.NotFoundException('未找到点赞记录');
        }
        return like;
    }
    toggleLike(userId, targetId, targetType) {
        return this.userWorkLikeService.toggleLike(+userId, +targetId, +targetType);
    }
    findOne(id) {
        return this.userWorkLikeService.findOne(+id);
    }
    update(id, updateUserWorkLikeDto) {
        return this.userWorkLikeService.update(+id, updateUserWorkLikeDto);
    }
    remove(id) {
        return this.userWorkLikeService.remove(+id);
    }
};
exports.UserWorkLikeController = UserWorkLikeController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建用户作品点赞关系' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '创建成功', type: user_work_like_entity_1.UserWorkLike }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_work_like_dto_1.CreateUserWorkLikeDto]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有用户作品点赞关系' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [user_work_like_entity_1.UserWorkLike] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定用户的所有点赞记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [user_work_like_entity_1.UserWorkLike] }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('target/:targetId/type/:targetType'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定作品/图片的所有点赞记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [user_work_like_entity_1.UserWorkLike] }),
    __param(0, (0, common_1.Param)('targetId')),
    __param(1, (0, common_1.Param)('targetType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "findByTargetId", null);
__decorate([
    (0, common_1.Get)('check'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户是否点赞了指定作品/图片' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: true, description: '用户ID' }),
    (0, swagger_1.ApiQuery)({ name: 'targetId', required: true, description: '目标ID' }),
    (0, swagger_1.ApiQuery)({ name: 'targetType', required: true, description: '目标类型' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: user_work_like_entity_1.UserWorkLike }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('targetId')),
    __param(2, (0, common_1.Query)('targetType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UserWorkLikeController.prototype, "checkLike", null);
__decorate([
    (0, common_1.Post)('toggle'),
    (0, swagger_1.ApiOperation)({ summary: '切换点赞状态' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: true, description: '用户ID' }),
    (0, swagger_1.ApiQuery)({ name: 'targetId', required: true, description: '目标ID' }),
    (0, swagger_1.ApiQuery)({ name: 'targetType', required: true, description: '目标类型' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '操作成功', type: user_work_like_entity_1.UserWorkLike }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('targetId')),
    __param(2, (0, common_1.Query)('targetType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "toggleLike", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取用户作品点赞关系' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: user_work_like_entity_1.UserWorkLike }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '点赞关系不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户作品点赞关系' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: user_work_like_entity_1.UserWorkLike }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '点赞关系不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_work_like_dto_1.UpdateUserWorkLikeDto]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除用户作品点赞关系' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserWorkLikeController.prototype, "remove", null);
exports.UserWorkLikeController = UserWorkLikeController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/用户作品点赞关系(user_work_like)'),
    (0, common_1.Controller)('user-work-like'),
    __metadata("design:paramtypes", [user_work_like_service_1.UserWorkLikeService])
], UserWorkLikeController);
//# sourceMappingURL=user_work_like.controller.js.map