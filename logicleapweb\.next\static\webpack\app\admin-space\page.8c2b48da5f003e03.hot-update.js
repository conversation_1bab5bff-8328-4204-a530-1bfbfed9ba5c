"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 新的发布课程状态\n    const [publishSeriesListForModal, setPublishSeriesListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishCourseListForModal, setPublishCourseListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishFormLoading, setPublishFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取发布弹窗的系列课程列表\n    const fetchPublishSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗系列课程列表成功:\", res.data.list);\n                setPublishSeriesListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表（用于发布弹窗）\n    const fetchPublishCourseList = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗子课程列表成功:\", res.data.list);\n                setPublishCourseListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n                setPublishCourseListForModal([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n            setPublishCourseListForModal([]);\n        }\n    };\n    // 处理系列选择（发布弹窗）\n    const handlePublishSeriesChange = (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 发布弹窗选择系列ID:\", seriesId);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined);\n        setPublishCourseListForModal([]);\n        // 重置表单中的课程选择\n        publishCourseForm.setFieldsValue({\n            courseId: undefined\n        });\n        // 获取该系列下的子课程\n        if (seriesId) {\n            fetchPublishCourseList(seriesId);\n        }\n    };\n    // 处理课程选择（发布弹窗）\n    const handlePublishCourseChange = (courseId)=>{\n        console.log(\"\\uD83D\\uDCD6 发布弹窗选择课程ID:\", courseId);\n        setSelectedCourseForPublish(courseId);\n    };\n    // 重置发布课程弹窗状态\n    const resetPublishCourseModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesListForModal([]);\n        setPublishCourseListForModal([]);\n        publishCourseForm.resetFields();\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        setIsPublishCourseModalVisible(true);\n        await fetchPublishSeriesList();\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择要发布的课程\");\n                return;\n            }\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            console.log(\"\\uD83D\\uDCE4 表单数据:\", values);\n            // 按照API要求构建发布数据\n            const publishData = {\n                title: values.title,\n                description: values.description,\n                videoDuration: parseInt(values.videoDuration) || 0,\n                status: 1,\n                contentConfig: {\n                    hasVideo: values.videoUrl ? 1 : 0,\n                    hasDocument: values.hasDocument ? 1 : 0,\n                    hasAudio: 0,\n                    video: {\n                        url: values.videoUrl || \"\",\n                        name: values.videoName || \"\"\n                    }\n                }\n            };\n            console.log(\"\\uD83D\\uDCE4 发布课程数据:\", publishData);\n            // 调用发布课程API\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(selectedCourseForPublish, publishData);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishCourseModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                await fetchCourseList();\n                // 如果当前系列已展开，刷新子课程列表\n                if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {\n                    await fetchSeriesCourses(selectedSeriesForPublish);\n                }\n            } else {\n                console.error(\"❌ 发布课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理系列选择\n    const handleSeriesSelect = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 选择系列ID:\", seriesId);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined); // 重置课程选择\n        // 获取系列详情和子课程\n        try {\n            var _res_data;\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.courseList)) {\n                console.log(\"✅ 获取系列子课程成功:\", res.data.courseList);\n                setPublishSeriesCourses(res.data.courseList);\n            } else {\n                console.error(\"❌ 获取系列子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列子课程失败\");\n                setPublishSeriesCourses([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列子课程失败，请重试\");\n            setPublishSeriesCourses([]);\n        }\n    };\n    // 处理课程选择\n    const handleCourseSelect = (courseId)=>{\n        const course = publishSeriesCourses.find((c)=>c.id === courseId);\n        if (!course) {\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"课程不存在\");\n            return;\n        }\n        setSelectedCourseForPublish(course);\n        console.log(\"\\uD83D\\uDCD6 选择课程:\", course);\n        setPublishStep(3);\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 966,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 975,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 965,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 982,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 983,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 984,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1006,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1017,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1035,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1032,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1046,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1045,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1043,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1224,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1231,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1240,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1243,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1230,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1259,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1276,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1258,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1287,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1288,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1289,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1286,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1295,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1294,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1285,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1257,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1317,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1376,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1377,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1370,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1362,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1357,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1391,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1386,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1399,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1394,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1421,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1420,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1426,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1425,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1428,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1429,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1424,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1411,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1451,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1437,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1471,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1476,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1470,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1482,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1485,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1486,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1481,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1461,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1457,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1510,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1511,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1509,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1508,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1519,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1518,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1521,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1522,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1517,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1499,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1495,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1545,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1550,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1544,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1557,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1556,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1559,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1560,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1555,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1535,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1531,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1573,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1568,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1591,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1591,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1584,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1593,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1580,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1352,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1332,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1623,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1618,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1631,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1626,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1641,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1642,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1643,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1639,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1634,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1653,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1654,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1652,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1647,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1613,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1601,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1684,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1679,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1692,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1687,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1714,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1713,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1719,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1718,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1721,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1722,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1717,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1704,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1700,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1739,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1740,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1738,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1732,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1749,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1744,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1768,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1761,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1756,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1674,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1661,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1802,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1794,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1814,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1813,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1812,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1820,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1819,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1818,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1826,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1825,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1824,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1832,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1831,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1830,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1838,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1837,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1836,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1844,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1843,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1842,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1850,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1849,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1848,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1856,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1855,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1854,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1811,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1805,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1870,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1871,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1872,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1873,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1869,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1863,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1882,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1877,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1897,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1898,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1896,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1890,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1789,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1777,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1935,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1927,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1922,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1947,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1942,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1956,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1958,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1959,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1960,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1961,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1957,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1955,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1917,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1905,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishCourseModal,\n                footer: null,\n                width: 600,\n                destroyOnClose: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        form: publishCourseForm,\n                        layout: \"vertical\",\n                        onFinish: handlePublishCourse,\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"seriesId\",\n                                label: \"选择系列课程\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请选择系列课程\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    placeholder: \"请选择系列课程\",\n                                    loading: publishFormLoading,\n                                    onChange: handlePublishSeriesChange,\n                                    showSearch: true,\n                                    filterOption: (input, option)=>{\n                                        var _option_children;\n                                        return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                    },\n                                    children: publishSeriesListForModal.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                            value: series.id,\n                                            children: [\n                                                series.title,\n                                                \" (ID: \",\n                                                series.id,\n                                                \")\"\n                                            ]\n                                        }, series.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1997,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1987,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1982,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"courseId\",\n                                label: \"选择子课程\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请选择要发布的子课程\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    placeholder: \"请先选择系列课程\",\n                                    disabled: !selectedSeriesForPublish,\n                                    onChange: handlePublishCourseChange,\n                                    showSearch: true,\n                                    filterOption: (input, option)=>{\n                                        var _option_children;\n                                        return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                    },\n                                    children: publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                            value: course.id,\n                                            children: [\n                                                course.title,\n                                                \" (ID: \",\n                                                course.id,\n                                                \")\"\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2019,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2009,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2004,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"title\",\n                                label: \"课程标题\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请输入课程标题\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    placeholder: \"请输入课程标题\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2031,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2026,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"description\",\n                                label: \"课程描述\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请输入课程描述\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                    placeholder: \"请输入课程描述\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2039,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2034,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"videoDuration\",\n                                label: \"视频时长（秒）\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    type: \"number\",\n                                    placeholder: \"请输入视频时长（秒）\",\n                                    min: 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2049,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2045,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"videoUrl\",\n                                label: \"视频URL\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    placeholder: \"请输入视频URL\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2060,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2056,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                name: \"videoName\",\n                                label: \"视频文件名\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    placeholder: \"请输入视频文件名\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2067,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2063,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end gap-2 pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: resetPublishCourseModal,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2071,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        type: \"primary\",\n                                        htmlType: \"submit\",\n                                        loading: publishFormLoading,\n                                        children: \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2074,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2070,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1976,\n                        columnNumber: 9\n                    }, undefined),\n                    \">\",\n                    publishStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第一步：选择系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2088,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"请选择要发布课程的系列\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2089,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2087,\n                                columnNumber: 13\n                            }, undefined),\n                            publishLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2094,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"加载系列课程列表...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2095,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2093,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 max-h-96 overflow-y-auto\",\n                                children: seriesList.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-all\",\n                                        onClick: ()=>handleSeriesSelect(series.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-1\",\n                                                            children: series.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2107,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                            children: series.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2108,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: series.status === 1 ? \"green\" : \"orange\",\n                                                                    children: series.status === 1 ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 2110,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"blue\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        series.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 2113,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2109,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2106,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"选择\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2116,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2105,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2100,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2098,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end pt-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    onClick: resetPublishModal,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2126,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 2086,\n                        columnNumber: 11\n                    }, undefined),\n                    publishStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第二步：选择课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2137,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            '从系列 \"',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: selectedSeriesForPublish === null || selectedSeriesForPublish === void 0 ? void 0 : selectedSeriesForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2139,\n                                                columnNumber: 22\n                                            }, undefined),\n                                            '\" 中选择要发布的课程'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2136,\n                                columnNumber: 13\n                            }, undefined),\n                            publishLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2145,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"加载课程列表...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2146,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2144,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 max-h-96 overflow-y-auto\",\n                                children: publishSeriesCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-all\",\n                                        onClick: ()=>handleCourseSelect(course.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-1\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2158,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                            children: course.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2159,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: course.status === 1 ? \"green\" : \"orange\",\n                                                                    children: course.status === 1 ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 2161,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"blue\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        course.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 2164,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                course.hasVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"purple\",\n                                                                    children: \"视频\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 2165,\n                                                                    columnNumber: 47\n                                                                }, undefined),\n                                                                course.hasDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"cyan\",\n                                                                    children: \"文档\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 2166,\n                                                                    columnNumber: 50\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2160,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2157,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"选择\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2169,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2156,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2151,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2149,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: handlePrevStep,\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: resetPublishModal,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2178,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 2135,\n                        columnNumber: 11\n                    }, undefined),\n                    publishStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第三步：确认发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2193,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"确认发布课程信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2194,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2192,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"系列课程：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2200,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-blue-600\",\n                                                children: selectedSeriesForPublish === null || selectedSeriesForPublish === void 0 ? void 0 : selectedSeriesForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2201,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2199,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"课程名称：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2204,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2205,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2203,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"课程描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2207,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"当前状态：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2212,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                color: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.status) === 1 ? \"green\" : \"orange\",\n                                                className: \"ml-2\",\n                                                children: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.status) === 1 ? \"已发布\" : \"草稿\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2213,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2198,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                form: publishCourseForm,\n                                layout: \"vertical\",\n                                onFinish: handlePublishCourse,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"title\",\n                                        label: \"课程标题\",\n                                        initialValue: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.title,\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"请输入课程标题\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            placeholder: \"请输入课程标题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2231,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2225,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"description\",\n                                        label: \"课程描述\",\n                                        initialValue: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.description,\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"请输入课程描述\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                            placeholder: \"请输入课程描述\",\n                                            rows: 3,\n                                            maxLength: 500,\n                                            showCount: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2240,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2234,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"videoDuration\",\n                                        label: \"视频时长（秒）\",\n                                        initialValue: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.videoDuration) || 0,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            type: \"number\",\n                                            placeholder: \"请输入视频时长（秒）\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2253,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2220,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-blue-800 font-medium mb-2\",\n                                        children: \"发布说明：\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2258,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 发布后课程将在课程市场中公开显示\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2260,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '• 课程状态将更新为\"已发布\"'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2261,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 学员可以开始学习该课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2262,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 发布后仍可以编辑课程内容\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2263,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2259,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2257,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: handlePrevStep,\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2268,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                onClick: resetPublishModal,\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2272,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                loading: publishLoading,\n                                                onClick: ()=>publishCourseForm.submit(),\n                                                children: \"确认发布\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2271,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2267,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 2191,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1968,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"BJPzNCW7DZRrfGWDDhF5pcIAstQ=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});