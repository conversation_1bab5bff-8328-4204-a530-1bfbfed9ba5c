"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipationAuditService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const participation_audit_entity_1 = require("./entities/participation_audit.entity");
let ParticipationAuditService = class ParticipationAuditService {
    participationAuditRepository;
    constructor(participationAuditRepository) {
        this.participationAuditRepository = participationAuditRepository;
    }
    async create(createParticipationAuditDto) {
        const participationAudit = this.participationAuditRepository.create(createParticipationAuditDto);
        return await this.participationAuditRepository.save(participationAudit);
    }
    async findAll() {
        return await this.participationAuditRepository.find();
    }
    async findOne(id) {
        const participationAudit = await this.participationAuditRepository.findOne({ where: { id } });
        if (!participationAudit) {
            throw new common_1.NotFoundException(`审核记录 #${id} 未找到`);
        }
        return participationAudit;
    }
    async update(id, updateParticipationAuditDto) {
        const participationAudit = await this.findOne(id);
        Object.assign(participationAudit, updateParticipationAuditDto);
        return await this.participationAuditRepository.save(participationAudit);
    }
    async remove(id) {
        const result = await this.participationAuditRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`审核记录 #${id} 未找到`);
        }
    }
};
exports.ParticipationAuditService = ParticipationAuditService;
exports.ParticipationAuditService = ParticipationAuditService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(participation_audit_entity_1.ParticipationAudit)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ParticipationAuditService);
//# sourceMappingURL=participation_audit.service.js.map