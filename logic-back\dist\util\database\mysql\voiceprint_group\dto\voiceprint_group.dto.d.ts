export declare class CreateVoiceprintGroupDto {
    groupName: string;
    description?: string;
}
export declare class UpdateVoiceprintGroupDto {
    groupName?: string;
    description?: string;
}
export declare class VoiceprintGroupDto {
    id: string;
    userId: string;
    groupId: string;
    groupName: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare class QueryVoiceprintGroupDto {
    id?: string;
    groupId?: string;
    groupName?: string;
}
