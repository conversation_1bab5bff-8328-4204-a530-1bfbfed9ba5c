{"version": 3, "file": "passive-logout.service.js", "sourceRoot": "", "sources": ["../../../src/web/user_login_log/passive-logout.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAC5C,2DAAsD;AAO/C,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAQ/B,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,MAAc,EAAE,UAAgB;QACxE,IAAI,CAAC;YACH,MAAM,mCAAe,CAAC,SAAS,CAAC;gBAC9B,MAAM;gBACN,QAAQ,EAAE,UAAU,EAAE,QAAQ,IAAI,MAAM;gBACxC,SAAS,EAAE,SAAS;gBACpB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,aAAqB,EAAE,UAAgB;QAC/E,MAAM,MAAM,GAAG,uBAAuB,aAAa,EAAE,CAAC;QACtD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAOD,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,UAAgB;QACvD,MAAM,MAAM,GAAG,gBAAgB,CAAC;QAChC,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,OAAe,EAAE,UAAgB;QAC5E,MAAM,MAAM,GAAG,oBAAoB,OAAO,EAAE,CAAC;QAC7C,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,cAAsB,EAAE,UAAgB;QACjF,MAAM,MAAM,GAAG,kBAAkB,cAAc,EAAE,CAAC;QAClD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,iBAAyB,EAAE,UAAgB;QACvF,MAAM,MAAM,GAAG,gBAAgB,iBAAiB,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,KAAK,CAAC,yBAAyB,CAAC,MAAc,EAAE,YAAoB,EAAE,UAAgB;QACpF,MAAM,MAAM,GAAG,kBAAkB,YAAY,EAAE,CAAC;QAChD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,KAAK,CAAC,4BAA4B,CAAC,MAAc,EAAE,gBAAwB,EAAE,UAAgB;QAC3F,MAAM,MAAM,GAAG,gBAAgB,gBAAgB,EAAE,CAAC;QAClD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,KAAK,CAAC,wBAAwB,CAAC,OAAiB,EAAE,MAAc,EAAE,UAAgB;QAChF,OAAO,CAAC,GAAG,CAAC,uBAAuB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAErD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACpC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CACrD,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;CACF,CAAA;AAxHY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;GACA,oBAAoB,CAwHhC;AAKD,IAAY,mBAYX;AAZD,WAAY,mBAAmB;IAC7B,qFAA2B,CAAA;IAC3B,0DAAyB,CAAA;IACzB,iFAAuB,CAAA;IACvB,+EAA0B,CAAA;IAC1B,sEAA2B,CAAA;IAC3B,qFAAgC,CAAA;IAChC,qEAA0B,CAAA;IAC1B,mEAAwB,CAAA;IACxB,sFAAiC,CAAA;IACjC,wDAAuB,CAAA;IACvB,mFAA8B,CAAA;AAChC,CAAC,EAZW,mBAAmB,mCAAnB,mBAAmB,QAY9B;AAMD,MAAa,iBAAiB;IACpB,MAAM,CAAC,OAAO,CAAuB;IAK7C,MAAM,CAAC,UAAU,CAAC,OAA6B;QAC7C,iBAAiB,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,aAAqB,EAAE,UAAgB;QACtF,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC9B,MAAM,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QACzF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,UAAgB;QAC9D,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC9B,MAAM,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,OAAe,EAAE,UAAgB;QACnF,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC9B,MAAM,iBAAiB,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACtF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,cAAsB,EAAE,UAAgB;QACxF,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC9B,MAAM,iBAAiB,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;QAC3F,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;CACF;AArDD,8CAqDC"}