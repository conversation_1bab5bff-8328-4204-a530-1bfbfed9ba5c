"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityTagController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const activity_tag_service_1 = require("./activity_tag.service");
const create_activity_tag_dto_1 = require("./dto/create-activity_tag.dto");
const update_activity_tag_dto_1 = require("./dto/update-activity_tag.dto");
const activity_tag_entity_1 = require("./entities/activity_tag.entity");
let ActivityTagController = class ActivityTagController {
    activityTagService;
    constructor(activityTagService) {
        this.activityTagService = activityTagService;
    }
    create(createActivityTagDto) {
        return this.activityTagService.create(createActivityTagDto);
    }
    findAll() {
        return this.activityTagService.findAll();
    }
    findByActivityId(activityId) {
        return this.activityTagService.findByActivityId(+activityId);
    }
    findByTagId(tagId) {
        return this.activityTagService.findByTagId(+tagId);
    }
    removeByActivityId(activityId) {
        return this.activityTagService.removeByActivityId(+activityId);
    }
    findOne(id) {
        return this.activityTagService.findOne(+id);
    }
    update(id, updateActivityTagDto) {
        return this.activityTagService.update(+id, updateActivityTagDto);
    }
    remove(id) {
        return this.activityTagService.remove(+id);
    }
    hardRemove(id) {
        return this.activityTagService.hardRemove(+id);
    }
};
exports.ActivityTagController = ActivityTagController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建活动标签关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '创建成功', type: activity_tag_entity_1.ActivityTag }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_tag_dto_1.CreateActivityTagDto]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有活动标签关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_tag_entity_1.ActivityTag] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定活动的所有标签关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_tag_entity_1.ActivityTag] }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "findByActivityId", null);
__decorate([
    (0, common_1.Get)('tag/:tagId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定标签的所有活动关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_tag_entity_1.ActivityTag] }),
    __param(0, (0, common_1.Param)('tagId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "findByTagId", null);
__decorate([
    (0, common_1.Delete)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '删除指定活动的所有标签关联（软删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "removeByActivityId", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取活动标签关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: activity_tag_entity_1.ActivityTag }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '活动标签关联不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动标签关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: activity_tag_entity_1.ActivityTag }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '活动标签关联不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_activity_tag_dto_1.UpdateActivityTagDto]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除活动标签关联（软删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)(':id/hard'),
    (0, swagger_1.ApiOperation)({ summary: '彻底删除活动标签关联（硬删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityTagController.prototype, "hardRemove", null);
exports.ActivityTagController = ActivityTagController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/活动标签关联(activity_tag)'),
    (0, common_1.Controller)('activity-tag'),
    __metadata("design:paramtypes", [activity_tag_service_1.ActivityTagService])
], ActivityTagController);
//# sourceMappingURL=activity_tag.controller.js.map