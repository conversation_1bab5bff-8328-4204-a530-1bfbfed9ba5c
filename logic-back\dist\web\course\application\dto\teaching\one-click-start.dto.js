"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OneClickStartResponseDto = exports.OneClickStartDataDto = exports.ExecutionDetailsDto = exports.CreatedTaskInfo = exports.OneClickStartDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class OneClickStartDto {
    courseId;
    classId;
}
exports.OneClickStartDto = OneClickStartDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程ID',
        example: 25,
        minimum: 1
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '课程ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '课程ID必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '课程ID必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], OneClickStartDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '班级ID',
        example: 63,
        minimum: 1
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '班级ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '班级ID必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '班级ID必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], OneClickStartDto.prototype, "classId", void 0);
class CreatedTaskInfo {
    taskId;
    taskName;
    startDate;
    endDate;
    assignedStudents;
}
exports.CreatedTaskInfo = CreatedTaskInfo;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务ID', example: 101 }),
    __metadata("design:type", Number)
], CreatedTaskInfo.prototype, "taskId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称', example: 'Node.js基础练习' }),
    __metadata("design:type", String)
], CreatedTaskInfo.prototype, "taskName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间', example: '2024-01-26T15:30:00Z' }),
    __metadata("design:type", String)
], CreatedTaskInfo.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间', example: '2024-02-02T23:59:59Z' }),
    __metadata("design:type", String)
], CreatedTaskInfo.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分配学生数量', example: 30 }),
    __metadata("design:type", Number)
], CreatedTaskInfo.prototype, "assignedStudents", void 0);
class ExecutionDetailsDto {
    courseName;
    seriesName;
    className;
    studentCount;
    pointsPerStudent;
    templateName;
    createdTasks;
    failedOperations;
    warningMessages;
}
exports.ExecutionDetailsDto = ExecutionDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程名称', example: '第一课：Node.js基础入门' }),
    __metadata("design:type", String)
], ExecutionDetailsDto.prototype, "courseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列名称', example: 'Node.js后端开发系列' }),
    __metadata("design:type", String)
], ExecutionDetailsDto.prototype, "seriesName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级名称', example: '软件工程2024-1班' }),
    __metadata("design:type", String)
], ExecutionDetailsDto.prototype, "className", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生数量', example: 30 }),
    __metadata("design:type", Number)
], ExecutionDetailsDto.prototype, "studentCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每学生积分', example: 100 }),
    __metadata("design:type", Number)
], ExecutionDetailsDto.prototype, "pointsPerStudent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '模板名称', example: 'Node.js开发环境模板' }),
    __metadata("design:type", String)
], ExecutionDetailsDto.prototype, "templateName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建的任务列表',
        type: [CreatedTaskInfo],
        example: [
            {
                taskId: 101,
                taskName: 'Node.js基础练习',
                startDate: '2024-01-26T15:30:00Z',
                endDate: '2024-02-02T23:59:59Z',
                assignedStudents: 30
            }
        ]
    }),
    __metadata("design:type", Array)
], ExecutionDetailsDto.prototype, "createdTasks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '失败的操作列表',
        type: [String],
        example: [],
        required: false
    }),
    __metadata("design:type", Array)
], ExecutionDetailsDto.prototype, "failedOperations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '警告信息列表',
        type: [String],
        example: [],
        required: false
    }),
    __metadata("design:type", Array)
], ExecutionDetailsDto.prototype, "warningMessages", void 0);
class OneClickStartDataDto {
    success;
    teachingRecordId;
    pointsAllocated;
    tasksCreated;
    templateApplied;
    executionTime;
    lockAcquireTime;
    totalExecutionTime;
    details;
}
exports.OneClickStartDataDto = OneClickStartDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否成功', example: true }),
    __metadata("design:type", Boolean)
], OneClickStartDataDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教学记录ID', example: 15 }),
    __metadata("design:type", Number)
], OneClickStartDataDto.prototype, "teachingRecordId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分配的总积分', example: 3000 }),
    __metadata("design:type", Number)
], OneClickStartDataDto.prototype, "pointsAllocated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建的任务数量', example: 2 }),
    __metadata("design:type", Number)
], OneClickStartDataDto.prototype, "tasksCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否应用了模板', example: true }),
    __metadata("design:type", Boolean)
], OneClickStartDataDto.prototype, "templateApplied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作执行时间', example: '2024-01-26T15:30:00Z' }),
    __metadata("design:type", String)
], OneClickStartDataDto.prototype, "executionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '获取锁耗时(毫秒)', example: 45 }),
    __metadata("design:type", Number)
], OneClickStartDataDto.prototype, "lockAcquireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总执行耗时(毫秒)', example: 2350 }),
    __metadata("design:type", Number)
], OneClickStartDataDto.prototype, "totalExecutionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '详细信息', type: ExecutionDetailsDto }),
    __metadata("design:type", ExecutionDetailsDto)
], OneClickStartDataDto.prototype, "details", void 0);
class OneClickStartResponseDto {
    code;
    message;
    data;
}
exports.OneClickStartResponseDto = OneClickStartResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], OneClickStartResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: '课程已成功开启！' }),
    __metadata("design:type", String)
], OneClickStartResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '执行结果数据', type: OneClickStartDataDto }),
    __metadata("design:type", OneClickStartDataDto)
], OneClickStartResponseDto.prototype, "data", void 0);
//# sourceMappingURL=one-click-start.dto.js.map