"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentDetailDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PaymentDetailDto {
    success;
    orderNo;
    channel;
    amount;
    status;
    paymentTime;
    createTime;
    subject;
    description;
    userId;
    paymentId;
    qrCode;
    errorMessage;
    extraData;
}
exports.PaymentDetailDto = PaymentDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否成功', example: true }),
    __metadata("design:type", Boolean)
], PaymentDetailDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单号', example: 'P202307250001' }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "orderNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', example: 'alipay' }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 99.99 }),
    __metadata("design:type", Number)
], PaymentDetailDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单状态', example: 'success' }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false }),
    __metadata("design:type", Date)
], PaymentDetailDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentDetailDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品标题', example: 'iPhone 15手机壳' }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述', required: false, example: '透明防摔手机保护壳' }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 'user123456' }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方支付订单号', required: false }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付二维码链接', required: false }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "qrCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误信息', required: false }),
    __metadata("design:type", String)
], PaymentDetailDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '扩展数据', required: false }),
    __metadata("design:type", Object)
], PaymentDetailDto.prototype, "extraData", void 0);
//# sourceMappingURL=payment-response.dto.js.map