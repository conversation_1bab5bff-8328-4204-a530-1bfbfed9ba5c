import { AiExtentService } from '../util/AiExtent.service';
import { Job } from 'bullmq';
import { QueueService } from 'src/util/queue/queue.service';
import { ScratchConfigService } from '../config/scratch.config.service';
import { WebSocketService } from 'src/util/web_socket/web_socket.service';
import { WebPointService } from 'src/web/web_point/web_point.service';
import { XunfeiVoiceprintRecognitionService } from 'src/util/ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.service';
export declare class AiVoiceprintRecognitionService extends AiExtentService {
    protected readonly queueService: QueueService;
    private readonly scratchConfigService;
    private readonly webSocketService;
    private readonly webPointService;
    private readonly voiceprintService;
    constructor(queueService: QueueService, scratchConfigService: ScratchConfigService, webSocketService: WebSocketService, webPointService: WebPointService, voiceprintService: XunfeiVoiceprintRecognitionService);
    createVoiceprintGroup(data: {
        groupId: string;
        groupName: string;
        groupInfo: string;
        userId: string;
        tabId: string;
    }): Promise<any>;
    createVoiceprintFeature(data: {
        groupId: string;
        featureId: string;
        featureInfo: string;
        audioBase64: string;
        userId: string;
        tabId: string;
    }): Promise<any>;
    compareVoiceprint(data: {
        groupId: string;
        dstFeatureId: string;
        audioBase64: string;
        userId: string;
        tabId: string;
    }): Promise<any>;
    searchVoiceprint(data: {
        groupId: string;
        audioBase64: string;
        topK: number;
        userId: string;
        tabId: string;
    }): Promise<any>;
    queryVoiceprintFeatureList(data: {
        groupId: string;
        userId: string;
        tabId: string;
    }): Promise<any>;
    deleteVoiceprintFeature(data: {
        groupId: string;
        featureId: string;
        userId: string;
        tabId: string;
    }): Promise<any>;
    updateVoiceprintFeature(data: {
        groupId: string;
        featureId: string;
        featureInfo: string;
        audioBase64: string;
        userId: string;
        tabId: string;
    }): Promise<any>;
    jobFunction(job: Job): Promise<{
        success: boolean;
        result: any;
        timestamp: number;
    }>;
    successFunction(job: Job): Promise<void>;
    singleFailFunction(job: Job, err: Error): void;
    completeFailFunction(job: Job, err: Error): void;
    insertAddJobCenter(data: any, job: Job): void;
    getJobStatus(jobId: string): Promise<any>;
}
