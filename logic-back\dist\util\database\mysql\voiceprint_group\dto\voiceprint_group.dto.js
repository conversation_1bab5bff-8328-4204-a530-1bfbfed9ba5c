"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryVoiceprintGroupDto = exports.VoiceprintGroupDto = exports.UpdateVoiceprintGroupDto = exports.CreateVoiceprintGroupDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateVoiceprintGroupDto {
    groupName;
    description;
}
exports.CreateVoiceprintGroupDto = CreateVoiceprintGroupDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '声纹特征库名称', example: '我的声纹库' }),
    (0, class_validator_1.IsNotEmpty)({ message: '名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '名称长度必须在1-100之间' }),
    __metadata("design:type", String)
], CreateVoiceprintGroupDto.prototype, "groupName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '描述信息', example: '用于个人声纹识别的特征库', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '描述长度必须在0-500之间' }),
    __metadata("design:type", String)
], CreateVoiceprintGroupDto.prototype, "description", void 0);
class UpdateVoiceprintGroupDto {
    groupName;
    description;
}
exports.UpdateVoiceprintGroupDto = UpdateVoiceprintGroupDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '声纹特征库名称', example: '我的声纹库（已更新）', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '名称长度必须在1-100之间' }),
    __metadata("design:type", String)
], UpdateVoiceprintGroupDto.prototype, "groupName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '描述信息', example: '用于个人声纹识别的特征库（已更新）', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '描述长度必须在0-500之间' }),
    __metadata("design:type", String)
], UpdateVoiceprintGroupDto.prototype, "description", void 0);
class VoiceprintGroupDto {
    id;
    userId;
    groupId;
    groupName;
    description;
    createdAt;
    updatedAt;
}
exports.VoiceprintGroupDto = VoiceprintGroupDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID' }),
    __metadata("design:type", String)
], VoiceprintGroupDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], VoiceprintGroupDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '讯飞声纹特征库ID' }),
    __metadata("design:type", String)
], VoiceprintGroupDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '声纹特征库名称' }),
    __metadata("design:type", String)
], VoiceprintGroupDto.prototype, "groupName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '描述信息' }),
    __metadata("design:type", String)
], VoiceprintGroupDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], VoiceprintGroupDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], VoiceprintGroupDto.prototype, "updatedAt", void 0);
class QueryVoiceprintGroupDto {
    id;
    groupId;
    groupName;
}
exports.QueryVoiceprintGroupDto = QueryVoiceprintGroupDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryVoiceprintGroupDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '讯飞声纹特征库ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryVoiceprintGroupDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '声纹特征库名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryVoiceprintGroupDto.prototype, "groupName", void 0);
//# sourceMappingURL=voiceprint_group.dto.js.map