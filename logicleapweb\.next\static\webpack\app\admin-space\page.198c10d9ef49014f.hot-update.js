"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    var _publishSeriesListForModal_find, _publishCourseListForModal_find;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 新的发布课程状态\n    const [publishSeriesListForModal, setPublishSeriesListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishCourseListForModal, setPublishCourseListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishFormLoading, setPublishFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取发布弹窗的系列课程列表\n    const fetchPublishSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗系列课程列表成功:\", res.data.list);\n                setPublishSeriesListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表（用于发布弹窗）\n    const fetchPublishCourseList = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的子课程列表，系列ID:\", seriesId);\n            // 使用系列详情API获取子课程\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.courseList)) {\n                console.log(\"✅ 获取发布弹窗子课程列表成功:\", res.data.courseList);\n                setPublishCourseListForModal(res.data.courseList);\n            } else {\n                console.error(\"❌ 获取发布弹窗子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n                setPublishCourseListForModal([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n            setPublishCourseListForModal([]);\n        }\n    };\n    // 处理系列选择（发布弹窗）\n    const handlePublishSeriesChange = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 发布弹窗选择系列ID:\", seriesId);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined);\n        setPublishCourseListForModal([]);\n        // 重置表单中的课程选择\n        publishCourseForm.setFieldsValue({\n            courseId: undefined\n        });\n        // 获取该系列下的子课程\n        if (seriesId) {\n            setPublishFormLoading(true);\n            await fetchPublishCourseList(seriesId);\n            setPublishFormLoading(false);\n        }\n    };\n    // 处理课程选择（发布弹窗）\n    const handlePublishCourseChange = (courseId)=>{\n        console.log(\"\\uD83D\\uDCD6 发布弹窗选择课程ID:\", courseId);\n        setSelectedCourseForPublish(courseId);\n    };\n    // 重置发布课程弹窗状态\n    const resetPublishCourseModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesListForModal([]);\n        setPublishCourseListForModal([]);\n        publishCourseForm.resetFields();\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        setIsPublishCourseModalVisible(true);\n        await fetchPublishSeriesList();\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择要发布的课程\");\n                return;\n            }\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            console.log(\"\\uD83D\\uDCE4 表单数据:\", values);\n            // 调用发布课程API - 使用PUT方法更新课程状态为已发布\n            console.log(\"\\uD83D\\uDCE4 发布课程，将状态设置为已发布\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(selectedCourseForPublish, {\n                status: 1 // 设置为已发布状态\n            });\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishCourseModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                await fetchCourseList();\n                // 如果当前系列已展开，刷新子课程列表\n                if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {\n                    await fetchSeriesCourses(selectedSeriesForPublish);\n                }\n            } else {\n                console.error(\"❌ 发布课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 925,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 955,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 966,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 965,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 992,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1173,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1183,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1186,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1189,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1171,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1208,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1216,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1225,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1223,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1215,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1237,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1238,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1244,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1243,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1266,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1325,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1326,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1319,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1318,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1311,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1306,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1340,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1335,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1348,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1370,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1369,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1375,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1374,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1377,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1378,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1373,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1360,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1356,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1400,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1386,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1420,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1425,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1431,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1434,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1435,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1430,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1410,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1406,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1459,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1458,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1457,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1468,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1467,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1470,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1471,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1466,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1448,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1444,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1494,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1499,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1493,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1506,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1505,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1508,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1509,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1504,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1484,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1480,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1522,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1517,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1540,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1540,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1533,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1542,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1529,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1301,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1281,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1572,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1567,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1580,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1575,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1589,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1590,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1591,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1592,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1588,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1583,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1602,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1603,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1601,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1596,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1562,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1550,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1633,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1628,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1641,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1636,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1663,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1662,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1668,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1667,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1670,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1671,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1666,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1653,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1688,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1689,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1687,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1681,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1698,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1693,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1717,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1716,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1710,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1705,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1623,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1610,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1751,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1743,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1763,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1762,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1761,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1769,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1768,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1773,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1781,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1780,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1779,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1787,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1786,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1785,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1793,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1792,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1791,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1799,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1798,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1797,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1805,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1804,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1803,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1760,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1754,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1820,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1821,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1822,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1818,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1812,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1831,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1826,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1846,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1847,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1845,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1839,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1738,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1726,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1884,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1876,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1871,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1896,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1891,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1905,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1907,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1908,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1909,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1910,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1906,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1904,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1866,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1854,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishCourseModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                loading: publishFormLoading,\n                                onChange: handlePublishSeriesChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: publishSeriesListForModal.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (ID: \",\n                                            series.id,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1947,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1937,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1932,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择子课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的子课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: selectedSeriesForPublish ? \"请选择要发布的子课程\" : \"请先选择系列课程\",\n                                disabled: !selectedSeriesForPublish,\n                                onChange: handlePublishCourseChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                notFoundContent: selectedSeriesForPublish ? \"该系列暂无子课程\" : \"请先选择系列课程\",\n                                children: publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1973,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            color: course.status === 1 ? \"green\" : course.status === 0 ? \"orange\" : \"red\",\n                                                            className: \"text-xs\",\n                                                            children: course.status === 1 ? \"已发布\" : course.status === 0 ? \"草稿\" : \"已归档\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1975,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-xs\",\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1978,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1974,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1972,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1971,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1960,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1955,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedCourseForPublish && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"即将发布的课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1989,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程ID: \",\n                                                selectedCourseForPublish\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1991,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"所属系列: \",\n                                                (_publishSeriesListForModal_find = publishSeriesListForModal.find((s)=>s.id === selectedSeriesForPublish)) === null || _publishSeriesListForModal_find === void 0 ? void 0 : _publishSeriesListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1992,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程名称: \",\n                                                (_publishCourseListForModal_find = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish)) === null || _publishCourseListForModal_find === void 0 ? void 0 : _publishCourseListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1993,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-blue-600 font-medium\",\n                                            children: '点击\"发布此课程\"将把该课程状态设置为已发布'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1994,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1990,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1988,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: selectedCourseForPublish ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✓ 已选择课程，可以发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2002,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"请先选择系列课程和子课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2004,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2000,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: resetPublishCourseModal,\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2008,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: publishFormLoading,\n                                            disabled: !selectedCourseForPublish,\n                                            className: selectedCourseForPublish ? \"bg-green-600 hover:bg-green-700 border-green-600\" : \"\",\n                                            children: publishFormLoading ? \"发布中...\" : \"发布此课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2011,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2007,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1999,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1925,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1917,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"BJPzNCW7DZRrfGWDDhF5pcIAstQ=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});