"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintGroupService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const voiceprint_group_entity_1 = require("./entities/voiceprint_group.entity");
const xunfei_voiceprint_recognition_service_1 = require("../../../ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.service");
let VoiceprintGroupService = class VoiceprintGroupService {
    voiceprintGroupRepository;
    xunfeiVoiceprintService;
    constructor(voiceprintGroupRepository, xunfeiVoiceprintService) {
        this.voiceprintGroupRepository = voiceprintGroupRepository;
        this.xunfeiVoiceprintService = xunfeiVoiceprintService;
    }
    async create(userId, createDto) {
        const groupId = `group_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        const result = await this.xunfeiVoiceprintService.createGroup(groupId, createDto.groupName, createDto.description || `声纹库 ${createDto.groupName} 由用户 ${userId} 创建`);
        console.log('测试保存到数据库', result);
        const voiceprintGroup = new voiceprint_group_entity_1.VoiceprintGroupEntity();
        voiceprintGroup.userId = userId;
        voiceprintGroup.groupId = groupId;
        voiceprintGroup.groupName = createDto.groupName;
        voiceprintGroup.description = createDto.description || '';
        return this.voiceprintGroupRepository.save(voiceprintGroup);
    }
    async findByUserId(userId, queryDto) {
        const whereConditions = { userId };
        if (queryDto) {
            if (queryDto.id)
                whereConditions.id = queryDto.id;
            if (queryDto.groupId)
                whereConditions.groupId = queryDto.groupId;
            if (queryDto.groupName)
                whereConditions.groupName = queryDto.groupName;
        }
        return this.voiceprintGroupRepository.find({
            where: whereConditions,
            order: { createdAt: 'DESC' }
        });
    }
    async findOne(id) {
        return this.voiceprintGroupRepository.findOne({ where: { id } });
    }
    async findByGroupId(groupId) {
        return this.voiceprintGroupRepository.findOne({ where: { groupId } });
    }
    async update(id, updateDto) {
        const voiceprintGroup = await this.findOne(id);
        if (!voiceprintGroup) {
            throw new Error('声纹特征库不存在');
        }
        if (updateDto.groupName)
            voiceprintGroup.groupName = updateDto.groupName;
        if (updateDto.description !== undefined)
            voiceprintGroup.description = updateDto.description;
        return this.voiceprintGroupRepository.save(voiceprintGroup);
    }
    async delete(id) {
        const voiceprintGroup = await this.findOne(id);
        if (!voiceprintGroup) {
            return false;
        }
        try {
            await this.xunfeiVoiceprintService.deleteGroup(voiceprintGroup.groupId);
            await this.voiceprintGroupRepository.delete(id);
            return true;
        }
        catch (error) {
            console.error('删除声纹特征库失败:', error);
            throw error;
        }
    }
};
exports.VoiceprintGroupService = VoiceprintGroupService;
exports.VoiceprintGroupService = VoiceprintGroupService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(voiceprint_group_entity_1.VoiceprintGroupEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        xunfei_voiceprint_recognition_service_1.XunfeiVoiceprintRecognitionService])
], VoiceprintGroupService);
//# sourceMappingURL=voiceprint_group.service.js.map