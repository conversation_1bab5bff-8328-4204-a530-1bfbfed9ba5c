"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityEventsTaskModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const activity_events_task_service_1 = require("./activity_events_task.service");
const activity_events_task_controller_1 = require("./activity_events_task.controller");
const activity_events_task_entity_1 = require("./entities/activity_events_task.entity");
let ActivityEventsTaskModule = class ActivityEventsTaskModule {
};
exports.ActivityEventsTaskModule = ActivityEventsTaskModule;
exports.ActivityEventsTaskModule = ActivityEventsTaskModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([activity_events_task_entity_1.ActivityEventsTask])],
        controllers: [activity_events_task_controller_1.ActivityEventsTaskController],
        providers: [activity_events_task_service_1.ActivityEventsTaskService],
        exports: [activity_events_task_service_1.ActivityEventsTaskService],
    })
], ActivityEventsTaskModule);
//# sourceMappingURL=activity_events_task.module.js.map