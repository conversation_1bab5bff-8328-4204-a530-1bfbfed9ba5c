"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityTagModule = void 0;
const common_1 = require("@nestjs/common");
const web_activity_tag_controller_1 = require("./web_activity_tag.controller");
const web_activity_tag_service_1 = require("./web_activity_tag.service");
const activity_tag_module_1 = require("../../util/database/mysql/activity_tag/activity_tag.module");
const http_response_result_module_1 = require("../http_response_result/http_response_result.module");
let WebActivityTagModule = class WebActivityTagModule {
};
exports.WebActivityTagModule = WebActivityTagModule;
exports.WebActivityTagModule = WebActivityTagModule = __decorate([
    (0, common_1.Module)({
        imports: [
            activity_tag_module_1.ActivityTagModule,
            http_response_result_module_1.HttpResponseResultModule,
        ],
        controllers: [web_activity_tag_controller_1.WebActivityTagController],
        providers: [web_activity_tag_service_1.WebActivityTagService],
        exports: [web_activity_tag_service_1.WebActivityTagService],
    })
], WebActivityTagModule);
//# sourceMappingURL=web_activity_tag.module.js.map