"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkAuditModule = void 0;
const common_1 = require("@nestjs/common");
const work_audit_service_1 = require("./work_audit.service");
const work_audit_controller_1 = require("./work_audit.controller");
const typeorm_1 = require("@nestjs/typeorm");
const work_audit_entity_1 = require("./entities/work_audit.entity");
let WorkAuditModule = class WorkAuditModule {
};
exports.WorkAuditModule = WorkAuditModule;
exports.WorkAuditModule = WorkAuditModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([work_audit_entity_1.WorkAudit])],
        controllers: [work_audit_controller_1.WorkAuditController],
        providers: [work_audit_service_1.WorkAuditService],
        exports: [work_audit_service_1.WorkAuditService],
    })
], WorkAuditModule);
//# sourceMappingURL=work_audit.module.js.map