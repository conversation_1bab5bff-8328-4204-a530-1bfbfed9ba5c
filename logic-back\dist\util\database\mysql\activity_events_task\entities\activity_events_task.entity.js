"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityEventsTask = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const activity_entity_1 = require("../../activity/entities/activity.entity");
let ActivityEventsTask = class ActivityEventsTask {
    id;
    userId;
    activityId;
    eventName;
    startTime;
    endTime;
    workId;
    workFile;
    workDescription;
    instructorName;
    schoolName;
    contactPerson;
    contactPhone;
    realName;
    idNumber;
    affiliatedSchool;
    organization;
    instructorPhone;
    competitionGroup;
    registrationFormFile;
    status;
    creatorId;
    createTime;
    updateTime;
    isDelete;
    remark;
    activity;
};
exports.ActivityEventsTask = ActivityEventsTask;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], ActivityEventsTask.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], ActivityEventsTask.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动ID' }),
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    __metadata("design:type", Number)
], ActivityEventsTask.prototype, "activityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '赛事名称' }),
    (0, swagger_1.ApiProperty)({ description: '赛事名称' }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "eventName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '开始时间', type: 'datetime' }),
    (0, swagger_1.ApiProperty)({ description: '开始时间' }),
    __metadata("design:type", Date)
], ActivityEventsTask.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '结束时间', type: 'datetime' }),
    (0, swagger_1.ApiProperty)({ description: '结束时间' }),
    __metadata("design:type", Date)
], ActivityEventsTask.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品ID', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '作品ID', required: false }),
    __metadata("design:type", Number)
], ActivityEventsTask.prototype, "workId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品文件路径', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '作品文件路径', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "workFile", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品介绍', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '作品介绍', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "workDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '指导老师', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '指导老师', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "instructorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '学校名称', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '学校名称', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "schoolName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '联系人', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '联系人', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "contactPerson", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '联系电话', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '联系电话', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "contactPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '真实姓名', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '真实姓名', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "realName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '证件号', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '证件号', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "idNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '所属学校', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '所属学校', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "affiliatedSchool", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '机构单位', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '机构单位', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '指导老师电话', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '指导老师电话', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "instructorPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '参赛组别', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '参赛组别', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "competitionGroup", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '报名表文件URL', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '报名表文件URL', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "registrationFormFile", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '任务状态：0-待开始 1-进行中 2-已提交 3-已审核 4-审核不通过', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '任务状态：0-待开始 1-进行中 2-已提交 3-已审核 4-审核不通过', default: 0 }),
    __metadata("design:type", Number)
], ActivityEventsTask.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建者ID', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '创建者ID', required: false }),
    __metadata("design:type", Number)
], ActivityEventsTask.prototype, "creatorId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ActivityEventsTask.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ActivityEventsTask.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否删除', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false }),
    __metadata("design:type", Boolean)
], ActivityEventsTask.prototype, "isDelete", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '备注信息', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    __metadata("design:type", String)
], ActivityEventsTask.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => activity_entity_1.Activity, activity => activity.id),
    (0, typeorm_1.JoinColumn)({ name: 'activityId' }),
    __metadata("design:type", activity_entity_1.Activity)
], ActivityEventsTask.prototype, "activity", void 0);
exports.ActivityEventsTask = ActivityEventsTask = __decorate([
    (0, typeorm_1.Entity)('activity_events_task')
], ActivityEventsTask);
//# sourceMappingURL=activity_events_task.entity.js.map