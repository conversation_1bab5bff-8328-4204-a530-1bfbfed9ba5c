"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRoleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_role_entity_1 = require("../../util/database/mysql/user_role/entities/user_role.entity");
const typeorm_2 = require("typeorm");
let UserRoleService = class UserRoleService {
    userRoleRepository;
    constructor(userRoleRepository) {
        this.userRoleRepository = userRoleRepository;
    }
    async create(createUserRoleDto) {
        const existingName = await this.userRoleRepository.findOne({
            where: { name: createUserRoleDto.name }
        });
        if (existingName) {
            throw new common_1.ConflictException(`角色名称 "${createUserRoleDto.name}" 已存在`);
        }
        const existingCode = await this.userRoleRepository.findOne({
            where: { code: createUserRoleDto.code }
        });
        if (existingCode) {
            throw new common_1.ConflictException(`角色编码 "${createUserRoleDto.code}" 已存在`);
        }
        if (createUserRoleDto.isDefault === 1) {
            await this.clearDefaultRole();
        }
        const userRole = this.userRoleRepository.create(createUserRoleDto);
        return await this.userRoleRepository.save(userRole);
    }
    async findAll() {
        return await this.userRoleRepository.find({
            order: { sort: 'DESC', id: 'ASC' }
        });
    }
    async findOne(id) {
        const userRole = await this.userRoleRepository.findOne({
            where: { id }
        });
        if (!userRole) {
            throw new common_1.NotFoundException(`角色ID为${id}的记录不存在`);
        }
        return userRole;
    }
    async findByCode(code) {
        const userRole = await this.userRoleRepository.findOne({
            where: { code }
        });
        if (!userRole) {
            throw new common_1.NotFoundException(`角色编码为${code}的记录不存在`);
        }
        return userRole;
    }
    async findDefault() {
        return await this.userRoleRepository.findOne({
            where: { isDefault: 1 }
        });
    }
    async findActive() {
        return await this.userRoleRepository.find({
            where: { status: 1 },
            order: { sort: 'DESC', id: 'ASC' }
        });
    }
    async update(id, updateUserRoleDto) {
        const userRole = await this.findOne(id);
        if (updateUserRoleDto.name && updateUserRoleDto.name !== userRole.name) {
            const existingName = await this.userRoleRepository.findOne({
                where: { name: updateUserRoleDto.name }
            });
            if (existingName) {
                throw new common_1.ConflictException(`角色名称 "${updateUserRoleDto.name}" 已存在`);
            }
        }
        if (updateUserRoleDto.code && updateUserRoleDto.code !== userRole.code) {
            const existingCode = await this.userRoleRepository.findOne({
                where: { code: updateUserRoleDto.code }
            });
            if (existingCode) {
                throw new common_1.ConflictException(`角色编码 "${updateUserRoleDto.code}" 已存在`);
            }
        }
        if (updateUserRoleDto.isDefault === 1 && userRole.isDefault !== 1) {
            await this.clearDefaultRole();
        }
        if (userRole.type === 1) {
            if (updateUserRoleDto.type === 2) {
                throw new common_1.BadRequestException('系统内置角色不能修改为自定义角色');
            }
        }
        Object.assign(userRole, updateUserRoleDto);
        return await this.userRoleRepository.save(userRole);
    }
    async remove(id) {
        const userRole = await this.findOne(id);
        if (userRole.type === 1) {
            throw new common_1.BadRequestException('系统内置角色不能删除');
        }
        if (userRole.isDefault === 1) {
            throw new common_1.BadRequestException('默认角色不能删除');
        }
        await this.userRoleRepository.remove(userRole);
    }
    async updateStatus(id, status) {
        const userRole = await this.findOne(id);
        if (userRole.type === 1 && status === 0) {
            throw new common_1.BadRequestException('系统内置角色不能禁用');
        }
        if (userRole.isDefault === 1 && status === 0) {
            throw new common_1.BadRequestException('默认角色不能禁用');
        }
        userRole.status = status;
        return await this.userRoleRepository.save(userRole);
    }
    async setDefault(id) {
        const userRole = await this.findOne(id);
        if (userRole.status === 0) {
            throw new common_1.BadRequestException('禁用状态的角色不能设为默认角色');
        }
        if (userRole.isDefault === 1) {
            return userRole;
        }
        await this.clearDefaultRole();
        userRole.isDefault = 1;
        return await this.userRoleRepository.save(userRole);
    }
    async clearDefaultRole() {
        const defaultRole = await this.findDefault();
        if (defaultRole) {
            defaultRole.isDefault = 0;
            await this.userRoleRepository.save(defaultRole);
        }
    }
    async findList(params = {}) {
        const { keyword, status } = params;
        const qb = this.userRoleRepository.createQueryBuilder('role');
        if (keyword) {
            qb.andWhere('role.name LIKE :keyword OR role.code LIKE :keyword', { keyword: `%${keyword}%` });
        }
        if (typeof status === 'number') {
            qb.andWhere('role.status = :status', { status });
        }
        qb.orderBy('role.sort', 'DESC').addOrderBy('role.id', 'ASC');
        return await qb.getMany();
    }
};
exports.UserRoleService = UserRoleService;
exports.UserRoleService = UserRoleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_role_entity_1.UserRole)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserRoleService);
//# sourceMappingURL=user_role.service.js.map