"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.XunfeiVoiceprintRecognitionController = void 0;
const common_1 = require("@nestjs/common");
const xunfei_voiceprint_recognition_service_1 = require("./xunfei_voiceprint_recognition.service");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateGroupDto {
    groupId;
    groupName;
    groupInfo;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID，自定义，用于后续操作', example: 'my_voiceprint_group' }),
    (0, class_validator_1.IsString)({ message: 'groupId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupId不能为空' }),
    __metadata("design:type", String)
], CreateGroupDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库名称', example: '我的声纹特征库' }),
    (0, class_validator_1.IsString)({ message: 'groupName必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupName不能为空' }),
    __metadata("design:type", String)
], CreateGroupDto.prototype, "groupName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库附加信息', example: '用于测试的声纹特征库' }),
    (0, class_validator_1.IsString)({ message: 'groupInfo必须是字符串' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateGroupDto.prototype, "groupInfo", void 0);
class CreateFeatureDto {
    groupId;
    featureId;
    featureInfo;
    audioBase64;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID', example: 'my_voiceprint_group' }),
    (0, class_validator_1.IsString)({ message: 'groupId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupId不能为空' }),
    __metadata("design:type", String)
], CreateFeatureDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征ID，自定义，用于后续操作', example: 'feature_001' }),
    (0, class_validator_1.IsString)({ message: 'featureId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'featureId不能为空' }),
    __metadata("design:type", String)
], CreateFeatureDto.prototype, "featureId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征附加信息', example: '张三的声纹样本' }),
    (0, class_validator_1.IsString)({ message: 'featureInfo必须是字符串' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateFeatureDto.prototype, "featureInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Base64编码的音频数据', required: true }),
    (0, class_validator_1.IsString)({ message: 'audioBase64必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'audioBase64不能为空' }),
    __metadata("design:type", String)
], CreateFeatureDto.prototype, "audioBase64", void 0);
class CompareFeatureDto {
    groupId;
    dstFeatureId;
    audioBase64;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID', example: 'my_voiceprint_group' }),
    (0, class_validator_1.IsString)({ message: 'groupId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupId不能为空' }),
    __metadata("design:type", String)
], CompareFeatureDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '目标特征ID', example: 'feature_001' }),
    (0, class_validator_1.IsString)({ message: 'dstFeatureId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'dstFeatureId不能为空' }),
    __metadata("design:type", String)
], CompareFeatureDto.prototype, "dstFeatureId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Base64编码的音频数据', required: true }),
    (0, class_validator_1.IsString)({ message: 'audioBase64必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'audioBase64不能为空' }),
    __metadata("design:type", String)
], CompareFeatureDto.prototype, "audioBase64", void 0);
class SearchFeatureDto {
    groupId;
    audioBase64;
    topK;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID', example: 'my_voiceprint_group' }),
    (0, class_validator_1.IsString)({ message: 'groupId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupId不能为空' }),
    __metadata("design:type", String)
], SearchFeatureDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Base64编码的音频数据', required: true }),
    (0, class_validator_1.IsString)({ message: 'audioBase64必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'audioBase64不能为空' }),
    __metadata("design:type", String)
], SearchFeatureDto.prototype, "audioBase64", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '返回结果数量，默认为3', example: 3, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'topK必须是数字' }),
    (0, class_validator_1.IsPositive)({ message: 'topK必须是正数' }),
    __metadata("design:type", Number)
], SearchFeatureDto.prototype, "topK", void 0);
class GroupIdDto {
    groupId;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID', example: 'my_voiceprint_group' }),
    (0, class_validator_1.IsString)({ message: 'groupId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupId不能为空' }),
    __metadata("design:type", String)
], GroupIdDto.prototype, "groupId", void 0);
class DeleteFeatureDto {
    groupId;
    featureId;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征库ID', example: 'my_voiceprint_group' }),
    (0, class_validator_1.IsString)({ message: 'groupId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'groupId不能为空' }),
    __metadata("design:type", String)
], DeleteFeatureDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征ID', example: 'feature_001' }),
    (0, class_validator_1.IsString)({ message: 'featureId必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'featureId不能为空' }),
    __metadata("design:type", String)
], DeleteFeatureDto.prototype, "featureId", void 0);
class UpdateFeatureDto extends CreateFeatureDto {
}
class ResponseDto {
    success;
    result;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '接口是否调用成功', example: true }),
    __metadata("design:type", Boolean)
], ResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '返回结果', example: {} }),
    __metadata("design:type", Object)
], ResponseDto.prototype, "result", void 0);
class ErrorResponseDto {
    success;
    error;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '接口是否调用成功', example: false }),
    __metadata("design:type", Boolean)
], ErrorResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误信息', example: '声纹识别失败' }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "error", void 0);
let XunfeiVoiceprintRecognitionController = class XunfeiVoiceprintRecognitionController {
    xunfeiVoiceprintRecognitionService;
    constructor(xunfeiVoiceprintRecognitionService) {
        this.xunfeiVoiceprintRecognitionService = xunfeiVoiceprintRecognitionService;
    }
    async createGroup(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.createGroup(body.groupId, body.groupName, body.groupInfo || '');
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '创建声纹特征库失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createFeature(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.createFeature(body.groupId, body.featureId, body.featureInfo || '', body.audioBase64);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '添加音频特征失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async compareFeature(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.compareFeature(body.groupId, body.dstFeatureId, body.audioBase64);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '声纹比对失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async searchFeature(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.searchFeature(body.groupId, body.audioBase64, body.topK || 3);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '声纹检索失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async queryFeatureList(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.queryFeatureList(body.groupId);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '查询特征列表失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateFeature(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.updateFeature(body.groupId, body.featureId, body.featureInfo || '', body.audioBase64);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '更新音频特征失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteFeature(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.deleteFeature(body.groupId, body.featureId);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '删除特征失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteGroup(body) {
        try {
            const result = await this.xunfeiVoiceprintRecognitionService.deleteGroup(body.groupId);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                error: error.message || '删除特征库失败'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.XunfeiVoiceprintRecognitionController = XunfeiVoiceprintRecognitionController;
__decorate([
    (0, common_1.Post)('create-group'),
    (0, swagger_1.ApiOperation)({ summary: '创建声纹特征库', description: '创建一个新的声纹特征库' }),
    (0, swagger_1.ApiBody)({ type: CreateGroupDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '创建成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CreateGroupDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "createGroup", null);
__decorate([
    (0, common_1.Post)('create-feature'),
    (0, swagger_1.ApiOperation)({ summary: '添加音频特征', description: '将声纹样本添加到特征库' }),
    (0, swagger_1.ApiBody)({ type: CreateFeatureDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CreateFeatureDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "createFeature", null);
__decorate([
    (0, common_1.Post)('compare-feature'),
    (0, swagger_1.ApiOperation)({ summary: '声纹比对(1:1)', description: '将声纹样本与指定的特征进行1:1比对' }),
    (0, swagger_1.ApiBody)({ type: CompareFeatureDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '比对成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CompareFeatureDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "compareFeature", null);
__decorate([
    (0, common_1.Post)('search-feature'),
    (0, swagger_1.ApiOperation)({ summary: '声纹检索(1:N)', description: '将声纹样本在特征库中进行1:N检索' }),
    (0, swagger_1.ApiBody)({ type: SearchFeatureDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检索成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [SearchFeatureDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "searchFeature", null);
__decorate([
    (0, common_1.Post)('query-feature-list'),
    (0, swagger_1.ApiOperation)({ summary: '查询特征列表', description: '查询特定特征库中的所有特征' }),
    (0, swagger_1.ApiBody)({ type: GroupIdDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [GroupIdDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "queryFeatureList", null);
__decorate([
    (0, common_1.Post)('update-feature'),
    (0, swagger_1.ApiOperation)({ summary: '更新音频特征', description: '更新已有的声纹特征' }),
    (0, swagger_1.ApiBody)({ type: UpdateFeatureDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [UpdateFeatureDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "updateFeature", null);
__decorate([
    (0, common_1.Post)('delete-feature'),
    (0, swagger_1.ApiOperation)({ summary: '删除指定特征', description: '从特征库中删除指定的特征' }),
    (0, swagger_1.ApiBody)({ type: DeleteFeatureDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [DeleteFeatureDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "deleteFeature", null);
__decorate([
    (0, common_1.Post)('delete-group'),
    (0, swagger_1.ApiOperation)({ summary: '删除特征库', description: '删除整个声纹特征库' }),
    (0, swagger_1.ApiBody)({ type: GroupIdDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功', type: ResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误', type: ErrorResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器错误', type: ErrorResponseDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [GroupIdDto]),
    __metadata("design:returntype", Promise)
], XunfeiVoiceprintRecognitionController.prototype, "deleteGroup", null);
exports.XunfeiVoiceprintRecognitionController = XunfeiVoiceprintRecognitionController = __decorate([
    (0, swagger_1.ApiTags)('外部服务/讯飞声纹识别'),
    (0, common_1.Controller)('xunfei-voiceprint-recognition'),
    __metadata("design:paramtypes", [xunfei_voiceprint_recognition_service_1.XunfeiVoiceprintRecognitionService])
], XunfeiVoiceprintRecognitionController);
//# sourceMappingURL=xunfei_voiceprint_recognition.controller.js.map