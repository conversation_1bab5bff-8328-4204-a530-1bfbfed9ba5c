"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-entities";
exports.ids = ["vendor-chunks/parse-entities"];
exports.modules = {

/***/ "(ssr)/./node_modules/parse-entities/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/parse-entities/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseEntities: () => (/* binding */ parseEntities)\n/* harmony export */ });\n/* harmony import */ var character_entities_legacy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! character-entities-legacy */ \"(ssr)/./node_modules/character-entities-legacy/index.js\");\n/* harmony import */ var character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! character-reference-invalid */ \"(ssr)/./node_modules/character-reference-invalid/index.js\");\n/* harmony import */ var is_decimal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-decimal */ \"(ssr)/./node_modules/is-decimal/index.js\");\n/* harmony import */ var is_hexadecimal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-hexadecimal */ \"(ssr)/./node_modules/is-hexadecimal/index.js\");\n/* harmony import */ var is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-alphanumerical */ \"(ssr)/./node_modules/is-alphanumerical/index.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/**\n * @import {Point} from 'unist'\n * @import {Options} from '../index.js'\n */\n\n\n\n\n\n\n\n\n// Warning messages.\nconst messages = [\n  '',\n  /* 1: Non terminated (named) */\n  'Named character references must be terminated by a semicolon',\n  /* 2: Non terminated (numeric) */\n  'Numeric character references must be terminated by a semicolon',\n  /* 3: Empty (named) */\n  'Named character references cannot be empty',\n  /* 4: Empty (numeric) */\n  'Numeric character references cannot be empty',\n  /* 5: Unknown (named) */\n  'Named character references must be known',\n  /* 6: Disallowed (numeric) */\n  'Numeric character references cannot be disallowed',\n  /* 7: Prohibited (numeric) */\n  'Numeric character references cannot be outside the permissible Unicode range'\n]\n\n/**\n * Parse HTML character references.\n *\n * @param {string} value\n * @param {Readonly<Options> | null | undefined} [options]\n */\nfunction parseEntities(value, options) {\n  const settings = options || {}\n  const additional =\n    typeof settings.additional === 'string'\n      ? settings.additional.charCodeAt(0)\n      : settings.additional\n  /** @type {Array<string>} */\n  const result = []\n  let index = 0\n  let lines = -1\n  let queue = ''\n  /** @type {Point | undefined} */\n  let point\n  /** @type {Array<number>|undefined} */\n  let indent\n\n  if (settings.position) {\n    if ('start' in settings.position || 'indent' in settings.position) {\n      // @ts-expect-error: points don’t have indent.\n      indent = settings.position.indent\n      // @ts-expect-error: points don’t have indent.\n      point = settings.position.start\n    } else {\n      point = settings.position\n    }\n  }\n\n  let line = (point ? point.line : 0) || 1\n  let column = (point ? point.column : 0) || 1\n\n  // Cache the current point.\n  let previous = now()\n  /** @type {number|undefined} */\n  let character\n\n  // Ensure the algorithm walks over the first character (inclusive).\n  index--\n\n  while (++index <= value.length) {\n    // If the previous character was a newline.\n    if (character === 10 /* `\\n` */) {\n      column = (indent ? indent[lines] : 0) || 1\n    }\n\n    character = value.charCodeAt(index)\n\n    if (character === 38 /* `&` */) {\n      const following = value.charCodeAt(index + 1)\n\n      // The behavior depends on the identity of the next character.\n      if (\n        following === 9 /* `\\t` */ ||\n        following === 10 /* `\\n` */ ||\n        following === 12 /* `\\f` */ ||\n        following === 32 /* ` ` */ ||\n        following === 38 /* `&` */ ||\n        following === 60 /* `<` */ ||\n        Number.isNaN(following) ||\n        (additional && following === additional)\n      ) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += String.fromCharCode(character)\n        column++\n        continue\n      }\n\n      const start = index + 1\n      let begin = start\n      let end = start\n      /** @type {string} */\n      let type\n\n      if (following === 35 /* `#` */) {\n        // Numerical reference.\n        end = ++begin\n\n        // The behavior further depends on the next character.\n        const following = value.charCodeAt(end)\n\n        if (following === 88 /* `X` */ || following === 120 /* `x` */) {\n          // ASCII hexadecimal digits.\n          type = 'hexadecimal'\n          end = ++begin\n        } else {\n          // ASCII decimal digits.\n          type = 'decimal'\n        }\n      } else {\n        // Named reference.\n        type = 'named'\n      }\n\n      let characterReferenceCharacters = ''\n      let characterReference = ''\n      let characters = ''\n      // Each type of character reference accepts different characters.\n      // This test is used to detect whether a reference has ended (as the semicolon\n      // is not strictly needed).\n      const test =\n        type === 'named'\n          ? is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__.isAlphanumerical\n          : type === 'decimal'\n            ? is_decimal__WEBPACK_IMPORTED_MODULE_1__.isDecimal\n            : is_hexadecimal__WEBPACK_IMPORTED_MODULE_2__.isHexadecimal\n\n      end--\n\n      while (++end <= value.length) {\n        const following = value.charCodeAt(end)\n\n        if (!test(following)) {\n          break\n        }\n\n        characters += String.fromCharCode(following)\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === 'named' && character_entities_legacy__WEBPACK_IMPORTED_MODULE_3__.characterEntitiesLegacy.includes(characters)) {\n          characterReferenceCharacters = characters\n          // @ts-expect-error: always able to decode.\n          characterReference = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__.decodeNamedCharacterReference)(characters)\n        }\n      }\n\n      let terminated = value.charCodeAt(end) === 59 /* `;` */\n\n      if (terminated) {\n        end++\n\n        const namedReference =\n          type === 'named' ? (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__.decodeNamedCharacterReference)(characters) : false\n\n        if (namedReference) {\n          characterReferenceCharacters = characters\n          characterReference = namedReference\n        }\n      }\n\n      let diff = 1 + end - start\n      let reference = ''\n\n      if (!terminated && settings.nonTerminated === false) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) reference is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== 'named') {\n          warning(4 /* Empty (numeric) */, diff)\n        }\n      } else if (type === 'named') {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !characterReference) {\n          warning(5 /* Unknown (named) */, 1)\n        } else {\n          // If there’s something after an named reference which is not known,\n          // cap the reference.\n          if (characterReferenceCharacters !== characters) {\n            end = begin + characterReferenceCharacters.length\n            diff = 1 + end - begin\n            terminated = false\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            const reason = characterReferenceCharacters\n              ? 1 /* Non terminated (named) */\n              : 3 /* Empty (named) */\n\n            if (settings.attribute) {\n              const following = value.charCodeAt(end)\n\n              if (following === 61 /* `=` */) {\n                warning(reason, diff)\n                characterReference = ''\n              } else if ((0,is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__.isAlphanumerical)(following)) {\n                characterReference = ''\n              } else {\n                warning(reason, diff)\n              }\n            } else {\n              warning(reason, diff)\n            }\n          }\n        }\n\n        reference = characterReference\n      } else {\n        if (!terminated) {\n          // All nonterminated numeric references are not rendered, and emit a\n          // warning.\n          warning(2 /* Non terminated (numeric) */, diff)\n        }\n\n        // When terminated and numerical, parse as either hexadecimal or\n        // decimal.\n        let referenceCode = Number.parseInt(\n          characters,\n          type === 'hexadecimal' ? 16 : 10\n        )\n\n        // Emit a warning when the parsed number is prohibited, and replace with\n        // replacement character.\n        if (prohibited(referenceCode)) {\n          warning(7 /* Prohibited (numeric) */, diff)\n          reference = String.fromCharCode(65533 /* `�` */)\n        } else if (referenceCode in character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__.characterReferenceInvalid) {\n          // Emit a warning when the parsed number is disallowed, and replace by\n          // an alternative.\n          warning(6 /* Disallowed (numeric) */, diff)\n          reference = character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__.characterReferenceInvalid[referenceCode]\n        } else {\n          // Parse the number.\n          let output = ''\n\n          // Emit a warning when the parsed number should not be used.\n          if (disallowed(referenceCode)) {\n            warning(6 /* Disallowed (numeric) */, diff)\n          }\n\n          // Serialize the number.\n          if (referenceCode > 0xffff) {\n            referenceCode -= 0x10000\n            output += String.fromCharCode(\n              (referenceCode >>> (10 & 0x3ff)) | 0xd800\n            )\n            referenceCode = 0xdc00 | (referenceCode & 0x3ff)\n          }\n\n          reference = output + String.fromCharCode(referenceCode)\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat a reference.\n      if (reference) {\n        flush()\n\n        previous = now()\n        index = end - 1\n        column += end - start + 1\n        result.push(reference)\n        const next = now()\n        next.offset++\n\n        if (settings.reference) {\n          settings.reference.call(\n            settings.referenceContext || undefined,\n            reference,\n            {start: previous, end: next},\n            value.slice(start - 1, end)\n          )\n        }\n\n        previous = next\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end)\n        queue += characters\n        column += characters.length\n        index = end - 1\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (character === 10 /* `\\n` */) {\n        line++\n        lines++\n        column = 0\n      }\n\n      if (Number.isNaN(character)) {\n        flush()\n      } else {\n        queue += String.fromCharCode(character)\n        column++\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('')\n\n  // Get current position.\n  function now() {\n    return {\n      line,\n      column,\n      offset: index + ((point ? point.offset : 0) || 0)\n    }\n  }\n\n  /**\n   * Handle the warning.\n   *\n   * @param {1|2|3|4|5|6|7} code\n   * @param {number} offset\n   */\n  function warning(code, offset) {\n    /** @type {ReturnType<now>} */\n    let position\n\n    if (settings.warning) {\n      position = now()\n      position.column += offset\n      position.offset += offset\n\n      settings.warning.call(\n        settings.warningContext || undefined,\n        messages[code],\n        position,\n        code\n      )\n    }\n  }\n\n  /**\n   * Flush `queue` (normal text).\n   * Macro invoked before each reference and at the end of `value`.\n   * Does nothing when `queue` is empty.\n   */\n  function flush() {\n    if (queue) {\n      result.push(queue)\n\n      if (settings.text) {\n        settings.text.call(settings.textContext || undefined, queue, {\n          start: previous,\n          end: now()\n        })\n      }\n\n      queue = ''\n    }\n  }\n}\n\n/**\n * Check if `character` is outside the permissible unicode range.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction prohibited(code) {\n  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff\n}\n\n/**\n * Check if `character` is disallowed.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction disallowed(code) {\n  return (\n    (code >= 0x0001 && code <= 0x0008) ||\n    code === 0x000b ||\n    (code >= 0x000d && code <= 0x001f) ||\n    (code >= 0x007f && code <= 0x009f) ||\n    (code >= 0xfdd0 && code <= 0xfdef) ||\n    (code & 0xffff) === 0xffff ||\n    (code & 0xffff) === 0xfffe\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parse-entities/lib/index.js\n");

/***/ })

};
;