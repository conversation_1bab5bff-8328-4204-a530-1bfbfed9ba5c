"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.XunfeiVoiceprintRecognitionModule = void 0;
const common_1 = require("@nestjs/common");
const xunfei_voiceprint_recognition_service_1 = require("./xunfei_voiceprint_recognition.service");
const xunfei_voiceprint_recognition_controller_1 = require("./xunfei_voiceprint_recognition.controller");
const ai_providers_config_service_1 = require("../config/ai_providers-config.service");
let XunfeiVoiceprintRecognitionModule = class XunfeiVoiceprintRecognitionModule {
};
exports.XunfeiVoiceprintRecognitionModule = XunfeiVoiceprintRecognitionModule;
exports.XunfeiVoiceprintRecognitionModule = XunfeiVoiceprintRecognitionModule = __decorate([
    (0, common_1.Module)({
        controllers: [xunfei_voiceprint_recognition_controller_1.XunfeiVoiceprintRecognitionController],
        providers: [xunfei_voiceprint_recognition_service_1.XunfeiVoiceprintRecognitionService, ai_providers_config_service_1.AiProvidersConfigService],
        exports: [xunfei_voiceprint_recognition_service_1.XunfeiVoiceprintRecognitionService]
    })
], XunfeiVoiceprintRecognitionModule);
//# sourceMappingURL=xunfei_voiceprint_recognition.module.js.map