"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StudentSelfAssessmentSubmissionModule = void 0;
const common_1 = require("@nestjs/common");
const student_self_assessment_submission_service_1 = require("./student_self_assessment_submission.service");
const student_self_assessment_submission_controller_1 = require("./student_self_assessment_submission.controller");
const typeorm_1 = require("@nestjs/typeorm");
const student_self_assessment_submission_entity_1 = require("./entities/student_self_assessment_submission.entity");
const task_self_assessment_item_module_1 = require("../task_self_assessment_item/task_self_assessment_item.module");
const task_self_assessment_item_entity_1 = require("../task_self_assessment_item/entities/task_self_assessment_item.entity");
const user_info_entity_1 = require("../user_info/entities/user_info.entity");
let StudentSelfAssessmentSubmissionModule = class StudentSelfAssessmentSubmissionModule {
};
exports.StudentSelfAssessmentSubmissionModule = StudentSelfAssessmentSubmissionModule;
exports.StudentSelfAssessmentSubmissionModule = StudentSelfAssessmentSubmissionModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([student_self_assessment_submission_entity_1.StudentSelfAssessmentSubmission, task_self_assessment_item_entity_1.TaskSelfAssessmentItem, user_info_entity_1.UserInfo]),
            task_self_assessment_item_module_1.TaskSelfAssessmentItemModule,
        ],
        controllers: [student_self_assessment_submission_controller_1.StudentSelfAssessmentSubmissionController],
        providers: [student_self_assessment_submission_service_1.StudentSelfAssessmentSubmissionService],
        exports: [student_self_assessment_submission_service_1.StudentSelfAssessmentSubmissionService],
    })
], StudentSelfAssessmentSubmissionModule);
//# sourceMappingURL=student_self_assessment_submission.module.js.map