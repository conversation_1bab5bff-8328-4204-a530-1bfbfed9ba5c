"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityTagService = void 0;
const common_1 = require("@nestjs/common");
const activity_tag_service_1 = require("../../util/database/mysql/activity_tag/activity_tag.service");
let WebActivityTagService = class WebActivityTagService {
    activityTagService;
    constructor(activityTagService) {
        this.activityTagService = activityTagService;
    }
    async addActivityTags(activityId, tagIds) {
        return await this.activityTagService.addActivityTags(activityId, tagIds);
    }
    async updateActivityTags(activityId, tagIds) {
        return await this.activityTagService.updateActivityTags(activityId, tagIds);
    }
    async deleteActivityTags(activityId) {
        return await this.activityTagService.deleteActivityTags(activityId);
    }
    async getActivityTagIds(activityId) {
        return await this.activityTagService.getActivityTagIds(activityId);
    }
    async getTagActivityIds(tagId) {
        return await this.activityTagService.getTagActivityIds(tagId);
    }
};
exports.WebActivityTagService = WebActivityTagService;
exports.WebActivityTagService = WebActivityTagService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [activity_tag_service_1.ActivityTagService])
], WebActivityTagService);
//# sourceMappingURL=web_activity_tag.service.js.map