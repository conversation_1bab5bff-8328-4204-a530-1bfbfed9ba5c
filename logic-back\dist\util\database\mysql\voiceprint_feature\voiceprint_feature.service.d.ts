import { Repository } from 'typeorm';
import { VoiceprintFeature } from './entities/voiceprint-feature.entity';
import { CreateVoiceprintFeatureDto } from './dto/create-voiceprint-feature.dto';
import { UpdateVoiceprintFeatureDto } from './dto/update-voiceprint-feature.dto';
export declare class VoiceprintFeatureService {
    private voiceprintFeatureRepository;
    constructor(voiceprintFeatureRepository: Repository<VoiceprintFeature>);
    create(createVoiceprintFeatureDto: CreateVoiceprintFeatureDto): Promise<VoiceprintFeature>;
    findAll(): Promise<VoiceprintFeature[]>;
    findOne(id: number): Promise<VoiceprintFeature>;
    findByUserId(userId: string): Promise<VoiceprintFeature[]>;
    findByGroupId(groupId: string): Promise<VoiceprintFeature[]>;
    findByFeatureIdAndGroupId(featureId: string, groupId: string): Promise<VoiceprintFeature>;
    update(id: number, updateVoiceprintFeatureDto: UpdateVoiceprintFeatureDto): Promise<VoiceprintFeature>;
    remove(id: number): Promise<void>;
    removeByGroupId(groupId: string): Promise<void>;
}
