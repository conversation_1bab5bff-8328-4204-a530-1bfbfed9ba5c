"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-parse";
exports.ids = ["vendor-chunks/rehype-parse"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-parse/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-parse/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeParse)\n/* harmony export */ });\n/* harmony import */ var hast_util_from_html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-from-html */ \"(ssr)/./node_modules/hast-util-from-html/lib/index.js\");\n/**\n * @import {Root} from 'hast'\n * @import {Options as FromHtmlOptions} from 'hast-util-from-html'\n * @import {Parser, Processor} from 'unified'\n */\n\n/**\n * @typedef {Omit<FromHtmlOptions, 'onerror'> & RehypeParseFields} Options\n *   Configuration.\n *\n * @typedef RehypeParseFields\n *   Extra fields.\n * @property {boolean | null | undefined} [emitParseErrors=false]\n *   Whether to emit parse errors while parsing (default: `false`).\n *\n *   > 👉 **Note**: parse errors are currently being added to HTML.\n *   > Not all errors emitted by parse5 (or us) are specced yet.\n *   > Some documentation may still be missing.\n */\n\n\n\n/**\n * Plugin to add support for parsing from HTML.\n *\n * > 👉 **Note**: this is not an XML parser.\n * > It supports SVG as embedded in HTML.\n * > It does not support the features available in XML.\n * > Passing SVG files might break but fragments of modern SVG should be fine.\n * > Use [`xast-util-from-xml`][xast-util-from-xml] to parse XML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction rehypeParse(options) {\n  /** @type {Processor<Root>} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n  const {emitParseErrors, ...settings} = {...self.data('settings'), ...options}\n\n  self.parser = parser\n\n  /**\n   * @type {Parser<Root>}\n   */\n  function parser(document, file) {\n    return (0,hast_util_from_html__WEBPACK_IMPORTED_MODULE_0__.fromHtml)(document, {\n      ...settings,\n      onerror: emitParseErrors\n        ? function (message) {\n            if (file.path) {\n              message.name = file.path + ':' + message.name\n              message.file = file.path\n            }\n\n            file.messages.push(message)\n          }\n        : undefined\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-parse/lib/index.js\n");

/***/ })

};
;