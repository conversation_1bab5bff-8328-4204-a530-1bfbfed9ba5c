export declare class EncryptExampleController {
    getPublicData(): {
        success: boolean;
        data: {
            message: string;
            timestamp: string;
        };
    };
    getSecureData(): {
        success: boolean;
        data: {
            message: string;
            secretInfo: string;
            timestamp: string;
        };
    };
    getPartialSecureData(id: string): {
        code: number;
        data: {
            success: boolean;
            data: {
                id: string;
                publicInfo: string;
                secretInfo: {
                    cardNo: string;
                    cvv: string;
                };
                sensitiveData: string;
                timestamp: string;
            };
        };
    };
    getSecureWithDecrypt(): {
        success: boolean;
        data: {
            message: string;
            timestamp: string;
        };
    };
    testRequestBodyEncryption(body: any): {
        success: boolean;
        data: {
            message: string;
            receivedData: any;
            timestamp: string;
        };
    };
    getSimplePartialSecureData(id: string): {
        id: string;
        publicInfo: string;
        secretInfo: {
            cardNo: string;
            cvv: string;
        };
        sensitiveData: string;
        timestamp: string;
    };
}
