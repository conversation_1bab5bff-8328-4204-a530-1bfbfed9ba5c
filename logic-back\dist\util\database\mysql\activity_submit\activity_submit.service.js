"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitySubmitService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_submit_entity_1 = require("./entities/activity_submit.entity");
const activity_entity_1 = require("../activity/entities/activity.entity");
let ActivitySubmitService = class ActivitySubmitService {
    activitySubmitRepository;
    activityRepository;
    constructor(activitySubmitRepository, activityRepository) {
        this.activitySubmitRepository = activitySubmitRepository;
        this.activityRepository = activityRepository;
    }
    async create(createActivitySubmitDto) {
        const activity = await this.activityRepository.findOne({
            where: { id: createActivitySubmitDto.activityId, isDelete: false }
        });
        if (!activity) {
            throw new common_1.NotFoundException(`活动ID ${createActivitySubmitDto.activityId} 未找到`);
        }
        const activeSubmit = await this.activitySubmitRepository.findOne({
            where: {
                activityId: createActivitySubmitDto.activityId,
                userId: createActivitySubmitDto.userId,
                isDelete: false,
                status: 0
            }
        });
        if (activeSubmit) {
            throw new common_1.BadRequestException('您已经报名过该活动');
        }
        const existingSubmit = await this.findUserSubmitForActivityAllStatus(createActivitySubmitDto.activityId, createActivitySubmitDto.userId);
        if (existingSubmit && existingSubmit.status === 1) {
            console.log(`用户 ${createActivitySubmitDto.userId} 重新报名活动 ${createActivitySubmitDto.activityId}，更新已取消的记录`);
            return this.updateExistingSubmit(existingSubmit, createActivitySubmitDto);
        }
        const activitySubmit = this.activitySubmitRepository.create(createActivitySubmitDto);
        return this.activitySubmitRepository.save(activitySubmit);
    }
    async findAll() {
        return this.activitySubmitRepository.find({
            where: { isDelete: false },
            relations: ['activity'],
            order: { createTime: 'DESC' },
        });
    }
    async findByActivity(activityId) {
        return this.activitySubmitRepository.find({
            where: { activityId, isDelete: false },
            relations: ['activity'],
            order: { createTime: 'DESC' },
        });
    }
    async findByUser(userId) {
        return this.activitySubmitRepository.find({
            where: { userId, isDelete: false },
            relations: ['activity'],
            order: { createTime: 'DESC' },
        });
    }
    async findUserSubmitForActivity(activityId, userId) {
        return this.activitySubmitRepository.findOne({
            where: {
                activityId,
                userId,
                isDelete: false,
                status: 0
            },
            relations: ['activity'],
        });
    }
    async findUserSubmitForActivityAllStatus(activityId, userId) {
        return this.activitySubmitRepository.findOne({
            where: {
                activityId,
                userId,
                isDelete: false
            },
            relations: ['activity'],
            order: { updateTime: 'DESC' }
        });
    }
    async updateExistingSubmit(existingSubmit, updateData) {
        Object.assign(existingSubmit, {
            ...updateData,
            status: 0,
            updateTime: new Date()
        });
        return this.activitySubmitRepository.save(existingSubmit);
    }
    async findOne(id) {
        const activitySubmit = await this.activitySubmitRepository.findOne({
            where: { id, isDelete: false },
            relations: ['activity'],
        });
        if (!activitySubmit) {
            throw new common_1.NotFoundException(`报名记录ID ${id} 未找到`);
        }
        return activitySubmit;
    }
    async update(id, updateActivitySubmitDto) {
        const activitySubmit = await this.findOne(id);
        if (updateActivitySubmitDto.status !== undefined && updateActivitySubmitDto.status !== activitySubmit.status) {
            updateActivitySubmitDto.reviewTime = new Date();
        }
        await this.activitySubmitRepository.update(id, updateActivitySubmitDto);
        return this.findOne(id);
    }
    async cancel(id) {
        const activitySubmit = await this.findOne(id);
        if (activitySubmit.status !== 0) {
            throw new common_1.BadRequestException('只能取消已报名状态的记录');
        }
        await this.activitySubmitRepository.update(id, { status: 1 });
        return this.findOne(id);
    }
    async cancelByUser(activityId, userId) {
        const activitySubmit = await this.activitySubmitRepository.findOne({
            where: {
                activityId,
                userId,
                isDelete: false,
                status: 0
            }
        });
        if (!activitySubmit) {
            throw new common_1.NotFoundException('未找到可取消的报名记录');
        }
        await this.activitySubmitRepository.update(activitySubmit.id, { status: 1 });
        return this.findOne(activitySubmit.id);
    }
    async remove(id) {
        await this.activitySubmitRepository.update(id, { isDelete: true });
    }
    async getSubmitStatistics(activityId) {
        const submits = await this.activitySubmitRepository.find({
            where: { activityId, isDelete: false }
        });
        return {
            total: submits.length,
            submitted: submits.filter(s => s.status === 0).length,
            cancelled: submits.filter(s => s.status === 1).length,
            approved: submits.filter(s => s.status === 2).length,
            rejected: submits.filter(s => s.status === 3).length,
        };
    }
};
exports.ActivitySubmitService = ActivitySubmitService;
exports.ActivitySubmitService = ActivitySubmitService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_submit_entity_1.ActivitySubmit)),
    __param(1, (0, typeorm_1.InjectRepository)(activity_entity_1.Activity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], ActivitySubmitService);
//# sourceMappingURL=activity_submit.service.js.map