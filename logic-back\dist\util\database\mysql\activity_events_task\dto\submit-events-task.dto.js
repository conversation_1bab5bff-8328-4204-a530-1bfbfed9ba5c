"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmitEventsTaskDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SubmitEventsTaskDto {
    workId;
    workFile;
    workDescription;
    instructorName;
    schoolName;
    contactPerson;
    contactPhone;
    realName;
    idNumber;
    affiliatedSchool;
    organization;
    instructorPhone;
    competitionGroup;
    registrationFormFile;
    remark;
    isResubmit;
}
exports.SubmitEventsTaskDto = SubmitEventsTaskDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '作品ID必须是数字' }),
    __metadata("design:type", Number)
], SubmitEventsTaskDto.prototype, "workId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品文件路径', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '作品文件路径必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "workFile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品介绍', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '作品介绍必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "workDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '指导老师', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '指导老师姓名必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "instructorName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学校名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '学校名称必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "schoolName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '联系人', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '联系人姓名必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '联系电话', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '联系电话必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真实姓名', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '真实姓名必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "realName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '证件号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '证件号必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "idNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属学校', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '所属学校必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "affiliatedSchool", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '机构单位', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '机构单位必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '指导老师电话', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '指导老师电话必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "instructorPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '参赛组别', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '参赛组别必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "competitionGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '报名表文件URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '报名表文件URL必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "registrationFormFile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注信息必须是字符串' }),
    __metadata("design:type", String)
], SubmitEventsTaskDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为重新提交', default: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], SubmitEventsTaskDto.prototype, "isResubmit", void 0);
//# sourceMappingURL=submit-events-task.dto.js.map