"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachingRecordsResponseDto = exports.TeachingRecordsDataDto = exports.PaginationInfoDto = exports.TeachingRecordInfoDto = exports.GetTeachingRecordsQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class GetTeachingRecordsQueryDto {
    page = 1;
    pageSize = 10;
    teacherId;
    courseId;
    classId;
    status;
    startDate;
    endDate;
}
exports.GetTeachingRecordsQueryDto = GetTeachingRecordsQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码，默认1',
        example: 1,
        required: false,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetTeachingRecordsQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量，默认10',
        example: 10,
        required: false,
        minimum: 1,
        maximum: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每页数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetTeachingRecordsQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '教师ID筛选',
        example: 100,
        required: false,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '教师ID必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '教师ID必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetTeachingRecordsQueryDto.prototype, "teacherId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程ID筛选',
        example: 25,
        required: false,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '课程ID必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '课程ID必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetTeachingRecordsQueryDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '班级ID筛选',
        example: 10,
        required: false,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '班级ID必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '班级ID必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetTeachingRecordsQueryDto.prototype, "classId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态筛选：0=进行中，1=成功，2=失败',
        example: 1,
        required: false,
        enum: [0, 1, 2]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '状态必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetTeachingRecordsQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '开始日期筛选',
        example: '2024-01-01',
        required: false,
        format: 'date'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '开始日期必须是字符串' }),
    __metadata("design:type", String)
], GetTeachingRecordsQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '结束日期筛选',
        example: '2024-01-31',
        required: false,
        format: 'date'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '结束日期必须是字符串' }),
    __metadata("design:type", String)
], GetTeachingRecordsQueryDto.prototype, "endDate", void 0);
class TeachingRecordInfoDto {
    id;
    courseId;
    courseName;
    seriesName;
    classId;
    className;
    teacherId;
    teacherName;
    status;
    statusLabel;
    pointsAllocated;
    tasksCreated;
    templateApplied;
    lockAcquireTime;
    totalExecutionTime;
    errorMessage;
    createdAt;
    updatedAt;
}
exports.TeachingRecordInfoDto = TeachingRecordInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '记录ID', example: 15 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 25 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程名称', example: '第一课：Node.js基础入门' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "courseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列名称', example: 'Node.js后端开发系列' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "seriesName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级ID', example: 10 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "classId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级名称', example: '软件工程2024-1班' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "className", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教师ID', example: 100 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "teacherId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教师姓名', example: '张老师' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "teacherName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：0=进行中，1=成功，2=失败', example: 1 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态标签', example: '成功' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分配的积分总数', example: 3000 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "pointsAllocated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建的任务数量', example: 2 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "tasksCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否应用了模板', example: 1 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "templateApplied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '获取锁耗时(毫秒)', example: 45 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "lockAcquireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总执行耗时(毫秒)', example: 2350 }),
    __metadata("design:type", Number)
], TeachingRecordInfoDto.prototype, "totalExecutionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误信息(如果有)', example: null, required: false }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-26T15:30:00Z' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2024-01-26T15:32:00Z' }),
    __metadata("design:type", String)
], TeachingRecordInfoDto.prototype, "updatedAt", void 0);
class PaginationInfoDto {
    page;
    pageSize;
    total;
    totalPages;
    hasNext;
    hasPrev;
}
exports.PaginationInfoDto = PaginationInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码', example: 1 }),
    __metadata("design:type", Number)
], PaginationInfoDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    __metadata("design:type", Number)
], PaginationInfoDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总记录数', example: 1 }),
    __metadata("design:type", Number)
], PaginationInfoDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数', example: 1 }),
    __metadata("design:type", Number)
], PaginationInfoDto.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有下一页', example: false }),
    __metadata("design:type", Boolean)
], PaginationInfoDto.prototype, "hasNext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有上一页', example: false }),
    __metadata("design:type", Boolean)
], PaginationInfoDto.prototype, "hasPrev", void 0);
class TeachingRecordsDataDto {
    list;
    pagination;
}
exports.TeachingRecordsDataDto = TeachingRecordsDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教学记录列表', type: [TeachingRecordInfoDto] }),
    __metadata("design:type", Array)
], TeachingRecordsDataDto.prototype, "list", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分页信息', type: PaginationInfoDto }),
    __metadata("design:type", PaginationInfoDto)
], TeachingRecordsDataDto.prototype, "pagination", void 0);
class TeachingRecordsResponseDto {
    code;
    message;
    data;
}
exports.TeachingRecordsResponseDto = TeachingRecordsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], TeachingRecordsResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], TeachingRecordsResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应数据', type: TeachingRecordsDataDto }),
    __metadata("design:type", TeachingRecordsDataDto)
], TeachingRecordsResponseDto.prototype, "data", void 0);
//# sourceMappingURL=teaching-records.dto.js.map