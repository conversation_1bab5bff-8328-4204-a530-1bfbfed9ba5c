declare class AttachmentDto {
    title: string;
    url: string;
    type: string;
}
declare class SelfAssessmentItemDto {
    content: string;
    sequence: number;
}
export declare class CreateTaskTemplateDto {
    taskName: string;
    taskDescription: string;
    durationDays: number;
    attachments?: AttachmentDto[];
    workIdsStr?: string;
    selfAssessmentItems?: SelfAssessmentItemDto[];
}
export declare class TaskTemplateResponseDto {
    id: number;
    courseId: number;
    taskName: string;
    taskDescription: string;
    durationDays: number;
    attachments: any[];
    workIdsStr: string;
    selfAssessmentItems: any[];
    status: number;
    attachmentsCount: number;
    assessmentItemsCount: number;
    firstAttachmentType: string;
    createdAt: Date;
    updatedAt: Date;
}
export {};
