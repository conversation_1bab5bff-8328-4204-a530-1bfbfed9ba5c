"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchoolRelationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_school_relation_entity_1 = require("./entities/user_school_relation.entity");
const user_info_entity_1 = require("../user_info/entities/user_info.entity");
let UserSchoolRelationService = class UserSchoolRelationService {
    relationRepository;
    userInfoRepository;
    constructor(relationRepository, userInfoRepository) {
        this.relationRepository = relationRepository;
        this.userInfoRepository = userInfoRepository;
    }
    async create(createDto) {
        const existingRelation = await this.relationRepository.findOne({
            where: {
                userId: createDto.userId,
                schoolId: createDto.schoolId
            }
        });
        if (existingRelation) {
            throw new common_1.ConflictException('该用户已关联此学校');
        }
        const relation = this.relationRepository.create(createDto);
        return await this.relationRepository.save(relation);
    }
    async findAll() {
        return await this.relationRepository.find();
    }
    async findOne(id) {
        const relation = await this.relationRepository.findOne({ where: { id } });
        if (!relation) {
            throw new common_1.NotFoundException(`ID为${id}的用户-学校关联记录不存在`);
        }
        return relation;
    }
    async findByUser(userId) {
        return await this.relationRepository.find({
            where: { userId }
        });
    }
    async findBySchool(schoolId) {
        return await this.relationRepository.find({
            where: { schoolId }
        });
    }
    async findByUserAndSchool(userId, schoolId) {
        return await this.relationRepository.findOne({
            where: {
                userId,
                schoolId
            }
        });
    }
    async findStudentsOfSchool(schoolId) {
        return await this.relationRepository.find({
            where: {
                schoolId,
                roleType: 1
            }
        });
    }
    async findTeachersOfSchool(schoolId) {
        const relations = await this.relationRepository.find({
            where: {
                schoolId,
                roleType: 2
            }
        });
        const userIds = relations.map(r => r.userId);
        const teacherInfos = await this.userInfoRepository.find({
            where: { id: (0, typeorm_2.In)(userIds) }
        });
        return teacherInfos;
    }
    async update(id, updateDto) {
        const relation = await this.findOne(id);
        if ((updateDto.userId !== undefined && updateDto.userId !== relation.userId) ||
            (updateDto.schoolId !== undefined && updateDto.schoolId !== relation.schoolId)) {
            const existingRelation = await this.relationRepository.findOne({
                where: {
                    userId: updateDto.userId !== undefined ? updateDto.userId : relation.userId,
                    schoolId: updateDto.schoolId !== undefined ? updateDto.schoolId : relation.schoolId
                }
            });
            if (existingRelation && existingRelation.id !== id) {
                throw new common_1.ConflictException('该用户已关联此学校');
            }
        }
        Object.assign(relation, updateDto);
        return await this.relationRepository.save(relation);
    }
    async updateRoleType(id, roleType) {
        const relation = await this.findOne(id);
        relation.roleType = roleType;
        return await this.relationRepository.save(relation);
    }
    async remove(id) {
        const result = await this.relationRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`ID为${id}的用户-学校关联记录不存在`);
        }
    }
    async removeByUserAndSchool(userId, schoolId) {
        const result = await this.relationRepository.delete({
            userId,
            schoolId
        });
        if (result.affected === 0) {
            throw new common_1.NotFoundException('该用户未关联此学校');
        }
    }
    async removeAllByUser(userId) {
        await this.relationRepository.delete({ userId });
    }
    async removeAllBySchool(schoolId) {
        await this.relationRepository.delete({ schoolId });
    }
};
exports.UserSchoolRelationService = UserSchoolRelationService;
exports.UserSchoolRelationService = UserSchoolRelationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_school_relation_entity_1.UserSchoolRelation)),
    __param(1, (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfo)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], UserSchoolRelationService);
//# sourceMappingURL=user_school_relation.service.js.map