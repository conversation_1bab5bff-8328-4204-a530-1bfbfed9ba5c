"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchoolRelationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_school_relation_service_1 = require("./user_school_relation.service");
const create_user_school_relation_dto_1 = require("./dto/create-user_school_relation.dto");
const update_user_school_relation_dto_1 = require("./dto/update-user_school_relation.dto");
const user_school_relation_entity_1 = require("./entities/user_school_relation.entity");
let UserSchoolRelationController = class UserSchoolRelationController {
    relationService;
    constructor(relationService) {
        this.relationService = relationService;
    }
    async create(createDto) {
        return await this.relationService.create(createDto);
    }
    async findAll() {
        return await this.relationService.findAll();
    }
    async findByUser(userId) {
        return await this.relationService.findByUser(+userId);
    }
    async findBySchool(schoolId) {
        return await this.relationService.findBySchool(+schoolId);
    }
    async findStudentsOfSchool(schoolId) {
        return await this.relationService.findStudentsOfSchool(+schoolId);
    }
    async findTeachersOfSchool(schoolId) {
        return await this.relationService.findTeachersOfSchool(+schoolId);
    }
    async checkUserSchoolRelation(userId, schoolId) {
        return await this.relationService.findByUserAndSchool(+userId, +schoolId);
    }
    async findOne(id) {
        return await this.relationService.findOne(+id);
    }
    async update(id, updateDto) {
        return await this.relationService.update(+id, updateDto);
    }
    async updateRoleType(id, roleType) {
        return await this.relationService.updateRoleType(+id, +roleType);
    }
    async remove(id) {
        await this.relationService.remove(+id);
    }
    async removeByUserAndSchool(userId, schoolId) {
        await this.relationService.removeByUserAndSchool(+userId, +schoolId);
    }
    async removeAllByUser(userId) {
        await this.relationService.removeAllByUser(+userId);
    }
    async removeAllBySchool(schoolId) {
        await this.relationService.removeAllBySchool(+schoolId);
    }
};
exports.UserSchoolRelationController = UserSchoolRelationController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建用户-学校关联' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: user_school_relation_entity_1.UserSchoolRelation }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '该用户已关联此学校' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_school_relation_dto_1.CreateUserSchoolRelationDto]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有用户-学校关联' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_school_relation_entity_1.UserSchoolRelation] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取关联的学校' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_school_relation_entity_1.UserSchoolRelation] }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "findByUser", null);
__decorate([
    (0, common_1.Get)('school/:schoolId'),
    (0, swagger_1.ApiOperation)({ summary: '根据学校ID获取关联的用户' }),
    (0, swagger_1.ApiParam)({ name: 'schoolId', description: '学校ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_school_relation_entity_1.UserSchoolRelation] }),
    __param(0, (0, common_1.Param)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "findBySchool", null);
__decorate([
    (0, common_1.Get)('school/:schoolId/students'),
    (0, swagger_1.ApiOperation)({ summary: '获取学校的学生' }),
    (0, swagger_1.ApiParam)({ name: 'schoolId', description: '学校ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_school_relation_entity_1.UserSchoolRelation] }),
    __param(0, (0, common_1.Param)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "findStudentsOfSchool", null);
__decorate([
    (0, common_1.Get)('school/:schoolId/teachers'),
    (0, swagger_1.ApiOperation)({ summary: '获取学校的教师' }),
    (0, swagger_1.ApiParam)({ name: 'schoolId', description: '学校ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_school_relation_entity_1.UserSchoolRelation] }),
    __param(0, (0, common_1.Param)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "findTeachersOfSchool", null);
__decorate([
    (0, common_1.Get)('check'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户是否关联了特定学校' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'schoolId', description: '学校ID', required: true }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_school_relation_entity_1.UserSchoolRelation }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "checkUserSchoolRelation", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取用户-学校关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_school_relation_entity_1.UserSchoolRelation }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联记录不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户-学校关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_school_relation_entity_1.UserSchoolRelation }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联记录不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '该用户已关联此学校' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_school_relation_dto_1.UpdateUserSchoolRelationDto]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/role-type/:roleType'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户在学校的角色类型' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiParam)({ name: 'roleType', description: '角色类型：1-学生, 2-教师' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_school_relation_entity_1.UserSchoolRelation }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联记录不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('roleType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "updateRoleType", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除用户-学校关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联记录不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)('user/:userId/school/:schoolId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除特定用户和学校的关联' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiParam)({ name: 'schoolId', description: '学校ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '该用户未关联此学校' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "removeByUserAndSchool", null);
__decorate([
    (0, common_1.Delete)('user/:userId/all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除用户的所有学校关联' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "removeAllByUser", null);
__decorate([
    (0, common_1.Delete)('school/:schoolId/all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除学校的所有用户关联' }),
    (0, swagger_1.ApiParam)({ name: 'schoolId', description: '学校ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    __param(0, (0, common_1.Param)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserSchoolRelationController.prototype, "removeAllBySchool", null);
exports.UserSchoolRelationController = UserSchoolRelationController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/用户-学校关联(user_school_relation)'),
    (0, common_1.Controller)('user-school-relation'),
    __metadata("design:paramtypes", [user_school_relation_service_1.UserSchoolRelationService])
], UserSchoolRelationController);
//# sourceMappingURL=user_school_relation.controller.js.map