"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [publishStep, setPublishStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // 1: 选择系列, 2: 选择课程, 3: 确认发布\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 发布课程\n    const handlePublishCourse = async (courseData)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程数据:\", courseData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(selectedCourseForPublish.id, {\n                ...courseData,\n                status: 1 // 设置为已发布状态\n            });\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                fetchCourseList();\n            } else {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布课程失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setPublishStep(1);\n        setSelectedSeriesForPublish(null);\n        setSelectedCourseForPublish(null);\n        setPublishSeriesCourses([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理系列选择\n    const handleSeriesSelect = async (seriesId)=>{\n        const series = seriesList.find((s)=>s.id === seriesId);\n        if (!series) {\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"系列课程不存在\");\n            return;\n        }\n        setSelectedSeriesForPublish(series);\n        console.log(\"\\uD83D\\uDCDA 选择系列课程:\", series);\n        // 获取系列详情和子课程\n        const seriesDetail = await fetchSeriesDetailForPublish(seriesId);\n        if (seriesDetail && seriesDetail.courseList) {\n            setPublishSeriesCourses(seriesDetail.courseList);\n            setPublishStep(2);\n        }\n    };\n    // 处理课程选择\n    const handleCourseSelect = (courseId)=>{\n        const course = publishSeriesCourses.find((c)=>c.id === courseId);\n        if (!course) {\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"课程不存在\");\n            return;\n        }\n        setSelectedCourseForPublish(course);\n        console.log(\"\\uD83D\\uDCD6 选择课程:\", course);\n        setPublishStep(3);\n    };\n    // 上一步\n    const handlePrevStep = ()=>{\n        if (publishStep === 2) {\n            setPublishStep(1);\n            setSelectedSeriesForPublish(null);\n            setPublishSeriesCourses([]);\n        } else if (publishStep === 3) {\n            setPublishStep(2);\n            setSelectedCourseForPublish(null);\n        }\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        // 获取系列课程列表\n        const series = await fetchSeriesForPublish();\n        if (series.length > 0) {\n            setSeriesList(series);\n            setIsPublishCourseModalVisible(true);\n            setPublishStep(1);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 868,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 869,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 858,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 874,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 899,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 910,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 909,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 928,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 925,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 956,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 924,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1117,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1136,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1123,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1152,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1167,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1159,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1180,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1180,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1181,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1182,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1179,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1188,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1187,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1178,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1210,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1270,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1262,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1255,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1250,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1284,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1292,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1287,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1314,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1313,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1318,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1321,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1322,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1317,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1300,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1344,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1330,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1364,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1369,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1363,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1376,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1375,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1378,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1379,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1374,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1354,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1350,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1403,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1402,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1401,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1412,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1411,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1414,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1415,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1410,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1388,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1438,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1443,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1437,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1450,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1449,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1452,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1453,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1448,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1428,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1424,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1466,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1461,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1484,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1484,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1477,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1486,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1473,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1245,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1225,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1516,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1511,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1524,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1519,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1533,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1534,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1535,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1536,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1532,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1527,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1545,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1540,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1506,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1494,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1577,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1572,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1585,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1580,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1607,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1606,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1612,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1611,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1614,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1615,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1610,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1597,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1593,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1632,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1633,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1631,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1625,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1642,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1637,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1661,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1660,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1654,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1567,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1554,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1695,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1687,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1707,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1706,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1705,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1713,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1712,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1711,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1719,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1718,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1717,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1725,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1724,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1723,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1731,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1730,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1729,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1735,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1743,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1742,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1741,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1749,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1748,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1747,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1704,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1698,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1763,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1764,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1765,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1766,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1762,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1756,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1775,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1770,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1790,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1791,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1789,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1783,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1682,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1670,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1828,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1820,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1815,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1840,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1835,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1849,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1851,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1852,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1853,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1854,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1850,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1848,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1810,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1798,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1864,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            color: \"blue\",\n                            children: [\n                                \"步骤 \",\n                                publishStep,\n                                \"/3\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1865,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1863,\n                    columnNumber: 11\n                }, void 0),\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: [\n                    publishStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第一步：选择系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1880,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"请选择要发布课程的系列\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1881,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1879,\n                                columnNumber: 13\n                            }, undefined),\n                            publishLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1886,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"加载系列课程列表...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1887,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1885,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 max-h-96 overflow-y-auto\",\n                                children: seriesList.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-all\",\n                                        onClick: ()=>handleSeriesSelect(series.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-1\",\n                                                            children: series.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1899,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                            children: series.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1900,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: series.status === 1 ? \"green\" : \"orange\",\n                                                                    children: series.status === 1 ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1902,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"blue\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        series.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1905,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1901,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1898,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"选择\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1908,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1897,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1892,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1890,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end pt-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    onClick: resetPublishModal,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1918,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1917,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1878,\n                        columnNumber: 11\n                    }, undefined),\n                    publishStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第二步：选择课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1929,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            '从系列 \"',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: selectedSeriesForPublish === null || selectedSeriesForPublish === void 0 ? void 0 : selectedSeriesForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1931,\n                                                columnNumber: 22\n                                            }, undefined),\n                                            '\" 中选择要发布的课程'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1930,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1928,\n                                columnNumber: 13\n                            }, undefined),\n                            publishLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1937,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"加载课程列表...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1938,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1936,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 max-h-96 overflow-y-auto\",\n                                children: publishSeriesCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-all\",\n                                        onClick: ()=>handleCourseSelect(course.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-1\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1950,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                            children: course.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1951,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: course.status === 1 ? \"green\" : \"orange\",\n                                                                    children: course.status === 1 ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1953,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"blue\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        course.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1956,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                course.hasVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"purple\",\n                                                                    children: \"视频\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1957,\n                                                                    columnNumber: 47\n                                                                }, undefined),\n                                                                course.hasDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"cyan\",\n                                                                    children: \"文档\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1958,\n                                                                    columnNumber: 50\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1952,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1949,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"选择\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1961,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1948,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1943,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1941,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: handlePrevStep,\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1971,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: resetPublishModal,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1974,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1970,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1927,\n                        columnNumber: 11\n                    }, undefined),\n                    publishStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第三步：确认发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1985,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"确认发布课程信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1986,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1984,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"系列课程：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1992,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-blue-600\",\n                                                children: selectedSeriesForPublish === null || selectedSeriesForPublish === void 0 ? void 0 : selectedSeriesForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1993,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1991,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"课程名称：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1996,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1997,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1995,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"课程描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2000,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2001,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1999,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"当前状态：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2004,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                color: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.status) === 1 ? \"green\" : \"orange\",\n                                                className: \"ml-2\",\n                                                children: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.status) === 1 ? \"已发布\" : \"草稿\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2005,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2003,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1990,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                form: publishCourseForm,\n                                layout: \"vertical\",\n                                onFinish: handlePublishCourse,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"title\",\n                                        label: \"课程标题\",\n                                        initialValue: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.title,\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"请输入课程标题\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            placeholder: \"请输入课程标题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2023,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2017,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"description\",\n                                        label: \"课程描述\",\n                                        initialValue: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.description,\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"请输入课程描述\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                            placeholder: \"请输入课程描述\",\n                                            rows: 3,\n                                            maxLength: 500,\n                                            showCount: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2032,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2026,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"videoDuration\",\n                                        label: \"视频时长（秒）\",\n                                        initialValue: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.videoDuration) || 0,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            type: \"number\",\n                                            placeholder: \"请输入视频时长（秒）\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2045,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2040,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2012,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-blue-800 font-medium mb-2\",\n                                        children: \"发布说明：\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2050,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 发布后课程将在课程市场中公开显示\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2052,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '• 课程状态将更新为\"已发布\"'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2053,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 学员可以开始学习该课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2054,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 发布后仍可以编辑课程内容\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2055,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2051,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2049,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: handlePrevStep,\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2060,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                onClick: resetPublishModal,\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2064,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                loading: publishLoading,\n                                                onClick: ()=>publishCourseForm.submit(),\n                                                children: \"确认发布\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2067,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2063,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2059,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1983,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1861,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"xlOnTnfjm6TI3J3yVrA4uD3tFbg=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});