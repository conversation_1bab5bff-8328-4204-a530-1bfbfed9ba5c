import { CreateUserRoleDto } from 'src/util/database/mysql/user_role/dto/create-user_role.dto';
import { UpdateUserRoleDto } from 'src/util/database/mysql/user_role/dto/update-user_role.dto';
import { UserRole } from 'src/util/database/mysql/user_role/entities/user_role.entity';
import { Repository } from 'typeorm';
export declare class UserRoleService {
    private userRoleRepository;
    constructor(userRoleRepository: Repository<UserRole>);
    create(createUserRoleDto: CreateUserRoleDto): Promise<UserRole>;
    findAll(): Promise<UserRole[]>;
    findOne(id: number): Promise<UserRole>;
    findByCode(code: string): Promise<UserRole>;
    findDefault(): Promise<UserRole | null>;
    findActive(): Promise<UserRole[]>;
    update(id: number, updateUserRoleDto: UpdateUserRoleDto): Promise<UserRole>;
    remove(id: number): Promise<void>;
    updateStatus(id: number, status: number): Promise<UserRole>;
    setDefault(id: number): Promise<UserRole>;
    private clearDefaultRole;
    findList(params?: {
        keyword?: string;
        status?: number;
    }): Promise<UserRole[]>;
}
