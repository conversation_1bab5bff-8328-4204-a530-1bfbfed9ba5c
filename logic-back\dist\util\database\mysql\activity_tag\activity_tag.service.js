"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityTagService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_tag_entity_1 = require("./entities/activity_tag.entity");
let ActivityTagService = class ActivityTagService {
    activityTagRepository;
    constructor(activityTagRepository) {
        this.activityTagRepository = activityTagRepository;
    }
    async create(createActivityTagDto) {
        const activityTag = this.activityTagRepository.create(createActivityTagDto);
        return this.activityTagRepository.save(activityTag);
    }
    async findAll() {
        return this.activityTagRepository.find({
            where: { isDelete: false },
        });
    }
    async findOne(id) {
        const activityTag = await this.activityTagRepository.findOne({
            where: { id, isDelete: false }
        });
        if (!activityTag) {
            throw new common_1.NotFoundException(`活动标签ID ${id} 未找到`);
        }
        return activityTag;
    }
    async update(id, updateActivityTagDto) {
        await this.activityTagRepository.update(id, updateActivityTagDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.activityTagRepository.update(id, { isDelete: true });
    }
    async hardRemove(id) {
        await this.activityTagRepository.delete(id);
    }
    async findByActivityId(activityId) {
        return this.activityTagRepository.find({
            where: { activityId, isDelete: false },
        });
    }
    async findByTagId(tagId) {
        return this.activityTagRepository.find({
            where: { tagId, isDelete: false },
        });
    }
    async removeByActivityId(activityId) {
        await this.activityTagRepository.update({ activityId }, { isDelete: true });
    }
    async addActivityTags(activityId, tagIds) {
        await this.removeByActivityId(activityId);
        const activityTags = tagIds.map(tagId => ({
            activityId,
            tagId,
            isDelete: false,
        }));
        await this.activityTagRepository.save(activityTags);
        return true;
    }
    async updateActivityTags(activityId, tagIds) {
        return await this.addActivityTags(activityId, tagIds);
    }
    async deleteActivityTags(activityId) {
        await this.removeByActivityId(activityId);
        return true;
    }
    async getActivityTagIds(activityId) {
        const activityTags = await this.findByActivityId(activityId);
        return activityTags.map(tag => tag.tagId);
    }
    async getTagActivityIds(tagId) {
        const activityTags = await this.findByTagId(tagId);
        return activityTags.map(tag => tag.activityId);
    }
};
exports.ActivityTagService = ActivityTagService;
exports.ActivityTagService = ActivityTagService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_tag_entity_1.ActivityTag)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ActivityTagService);
//# sourceMappingURL=activity_tag.service.js.map