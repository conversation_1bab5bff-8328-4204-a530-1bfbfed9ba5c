"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseSeriesTag = void 0;
const typeorm_1 = require("typeorm");
const course_series_entity_1 = require("../management/course-series.entity");
const course_tag_entity_1 = require("./course-tag.entity");
let CourseSeriesTag = class CourseSeriesTag {
    seriesId;
    tagId;
    createdAt;
    series;
    tag;
};
exports.CourseSeriesTag = CourseSeriesTag;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'int', name: 'series_id', comment: '课程系列ID' }),
    __metadata("design:type", Number)
], CourseSeriesTag.prototype, "seriesId", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'int', name: 'tag_id', comment: '标签ID' }),
    __metadata("design:type", Number)
], CourseSeriesTag.prototype, "tagId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '关联创建时间' }),
    __metadata("design:type", Date)
], CourseSeriesTag.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_series'),
    (0, typeorm_1.ManyToOne)(() => course_series_entity_1.CourseSeries, series => series.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'series_id' }),
    __metadata("design:type", course_series_entity_1.CourseSeries)
], CourseSeriesTag.prototype, "series", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_tag'),
    (0, typeorm_1.ManyToOne)(() => course_tag_entity_1.CourseTag, tag => tag.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'tag_id' }),
    __metadata("design:type", course_tag_entity_1.CourseTag)
], CourseSeriesTag.prototype, "tag", void 0);
exports.CourseSeriesTag = CourseSeriesTag = __decorate([
    (0, typeorm_1.Entity)('course_series_tags')
], CourseSeriesTag);
//# sourceMappingURL=course-series-tag.entity.js.map