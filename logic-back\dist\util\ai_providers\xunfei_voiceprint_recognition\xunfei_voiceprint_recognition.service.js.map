{"version": 3, "file": "xunfei_voiceprint_recognition.service.js", "sourceRoot": "", "sources": ["../../../../src/util/ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,uFAAiF;AACjF,iCAAiC;AAU1B,IAAM,kCAAkC,0CAAxC,MAAM,kCAAkC;IAOzB;IANH,MAAM,GAAG,IAAI,eAAM,CAAC,oCAAkC,CAAC,IAAI,CAAC,CAAC;IACtE,KAAK,CAAS;IACd,MAAM,CAAS;IACf,SAAS,CAAS;IAClB,MAAM,GAAW,6CAA6C,CAAC;IAEvE,YAAoB,aAAuC;QAAvC,kBAAa,GAAb,aAAa,CAA0B;QACzD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACpE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;QACtE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;QAE5E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IASD,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,SAAiB,EAAE,SAAiB;QACrE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,OAAO,SAAS,SAAS,EAAE,CAAC,CAAC;QAEzD,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,SAAS;oBACpB,SAAS,EAAE,SAAS;oBACpB,cAAc,EAAE;wBACd,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUD,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,SAAiB,EAAE,WAAmB,EAAE,WAAmB;QAC9F,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,SAAS,SAAS,OAAO,EAAE,CAAC,CAAC;QAExD,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,SAAS;oBACpB,WAAW,EAAE,WAAW;oBACxB,gBAAgB,EAAE;wBAChB,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,WAAW;iBACnB;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASD,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,YAAoB,EAAE,WAAmB;QAC7E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,YAAY,SAAS,OAAO,EAAE,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,OAAO;oBAChB,YAAY,EAAE,YAAY;oBAC1B,iBAAiB,EAAE;wBACjB,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,WAAW;iBACnB;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASD,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,WAAmB,EAAE,OAAe,CAAC;QACxE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,OAAO,SAAS,IAAI,MAAM,CAAC,CAAC;QAE/D,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,IAAI;oBACV,YAAY,EAAE;wBACZ,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,WAAW;iBACnB;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,OAAO,QAAQ,CAAC,CAAC;QAE1C,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,OAAO;oBAChB,mBAAmB,EAAE;wBACnB,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUD,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,SAAiB,EAAE,WAAmB,EAAE,WAAmB;QAC9F,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,SAAS,SAAS,OAAO,EAAE,CAAC,CAAC;QAExD,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,SAAS;oBACpB,WAAW,EAAE,WAAW;oBACxB,gBAAgB,EAAE;wBAChB,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,WAAW;iBACnB;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,SAAiB;QACpD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,SAAS,SAAS,OAAO,EAAE,CAAC,CAAC;QAExD,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,SAAS;oBACpB,gBAAgB,EAAE;wBAChB,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,WAAW,CAAC,OAAe;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG;YACb,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,MAAM,EAAE,CAAC;aACV;YACD,SAAS,EAAE;gBACT,SAAS,EAAE;oBACT,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,OAAO;oBAChB,cAAc,EAAE;wBACd,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,MAAM,EAAE,MAAM;qBACf;iBACF;aACF;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAQO,KAAK,CAAC,YAAY,CAAC,MAAW;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aAC7B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,gBAAgB,QAAQ,CAAC,MAAM,WAAW,SAAS,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOO,eAAe;QAErB,MAAM,IAAI,GAAG,gBAAgB,CAAC;QAC9B,MAAM,IAAI,GAAG,uBAAuB,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QAC7B,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QAG7B,MAAM,eAAe,GAAG,SAAS,IAAI;QACjC,IAAI;OACL,IAAI,WAAW,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,oBAAoB,eAAe,EAAE,CAAC,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAE5C,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;aAC1D,MAAM,CAAC,eAAe,CAAC;aACvB,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,cAAc,SAAS,EAAE,CAAC,CAAC;QAGvC,MAAM,mBAAmB,GAAG,YAAY,IAAI,CAAC,MAAM,4EAA4E,SAAS,GAAG,CAAC;QAC5I,OAAO,CAAC,GAAG,CAAC,wBAAwB,mBAAmB,EAAE,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,kBAAkB,aAAa,EAAE,CAAC,CAAC;QAI/C,MAAM,WAAW,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC3E,MAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClE,MAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAElE,MAAM,UAAU,GAAG,GAAG,IAAI,CAAC,MAAM,kBAAkB,aAAa,SAAS,IAAI,SAAS,IAAI,EAAE,CAAC;QAC7F,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAElC,OAAO,UAAU,CAAC;IACpB,CAAC;IASO,gBAAgB,CAAC,QAAa,EAAE,QAAgB;QACtD,IAAI,CAAC;YACH,IAAI,QAAQ;gBACR,QAAQ,CAAC,MAAM;gBACf,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;gBAC1B,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAG/B,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;gBACnD,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;gBACvC,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACpE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAEpC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACnC,CAAC;iBAAM,CAAC;gBAEN,MAAM,SAAS,GAAG,QAAQ,EAAE,MAAM,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;gBAC/C,MAAM,QAAQ,GAAG,QAAQ,EAAE,MAAM,EAAE,OAAO,IAAI,MAAM,CAAC;gBACrD,MAAM,IAAI,KAAK,CAAC,UAAU,SAAS,KAAK,QAAQ,EAAE,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAvdY,gFAAkC;6CAAlC,kCAAkC;IAD9C,IAAA,mBAAU,GAAE;qCAQwB,sDAAwB;GAPhD,kCAAkC,CAud9C"}