"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EncryptionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptionService = exports.SessionType = void 0;
const common_1 = require("@nestjs/common");
const crypto = require("crypto");
const key_management_service_1 = require("./key-management/key-management.service");
const redis_session_service_1 = require("./session/redis-session.service");
var SessionType;
(function (SessionType) {
    SessionType["STANDARD"] = "standard";
    SessionType["SECURE"] = "secure";
})(SessionType || (exports.SessionType = SessionType = {}));
let EncryptionService = EncryptionService_1 = class EncryptionService {
    keyManagementService;
    redisSessionService;
    logger = new common_1.Logger(EncryptionService_1.name);
    rsaPrivateKey;
    rsaPublicKey;
    sessionCache = new Map();
    standardSessionTTL = 30 * 60 * 1000;
    secureSessionTTL = 5 * 60 * 1000;
    constructor(keyManagementService, redisSessionService) {
        this.keyManagementService = keyManagementService;
        this.redisSessionService = redisSessionService;
        this.logger.log('【初始化】加密服务开始初始化...');
        try {
            const rsaKeyPair = this.keyManagementService.getRsaKeyPair();
            this.rsaPrivateKey = rsaKeyPair.privateKey;
            this.rsaPublicKey = rsaKeyPair.publicKey;
            this.logger.log(`【初始化】成功获取RSA密钥对，公钥指纹: ${this.getKeyFingerprint(this.rsaPublicKey)}`);
        }
        catch (error) {
            this.logger.warn(`【初始化】获取RSA密钥对失败: ${error.message}，服务将在首次请求时重试`);
        }
        this.logger.log('【初始化】加密服务初始化完成');
    }
    getKeyFingerprint(key) {
        try {
            const buffer = typeof key === 'string' ? Buffer.from(key) : key;
            const hash = crypto.createHash('sha256').update(buffer).digest('hex');
            return hash.substring(0, 8);
        }
        catch (error) {
            return 'unknown';
        }
    }
    getPublicKey() {
        this.logger.log('【密钥交换】客户端请求RSA公钥');
        try {
            return this.keyManagementService.getPublicKeyWithVersion();
        }
        catch (error) {
            this.logger.error(`【密钥交换】获取公钥失败: ${error.message}`);
            const rsaKeyPair = this.keyManagementService.getRsaKeyPair();
            this.rsaPublicKey = rsaKeyPair.publicKey;
            const fallbackId = crypto.randomUUID();
            this.logger.warn(`【密钥交换】回退到旧方式获取公钥，使用临时ID: ${fallbackId}`);
            return {
                keyId: fallbackId,
                publicKey: this.rsaPublicKey
            };
        }
    }
    async createSession(sessionId, encryptedAesKey, keyId = '', sessionType = SessionType.STANDARD, clientInfo) {
        try {
            this.logger.log(`【会话创建】开始为会话 ${sessionId} 创建${sessionType === SessionType.SECURE ? '安全' : '标准'}密钥`);
            this.logger.log(`【会话创建】接收到加密的AES密钥数据，长度: ${encryptedAesKey.length}, 密钥ID: ${keyId || '未提供'}`);
            let decryptedAesKey;
            let needsKeyUpdate = false;
            try {
                if (keyId) {
                    this.logger.log(`【会话创建】使用指定密钥ID ${keyId} 解密AES密钥...`);
                    const result = this.keyManagementService.decryptWithRsaPrivateKeyById(encryptedAesKey, keyId);
                    decryptedAesKey = result.data;
                    needsKeyUpdate = result.needsUpdate;
                    if (needsKeyUpdate) {
                        this.logger.log(`【会话创建】密钥ID ${keyId} 已过期或即将过期，客户端需要更新公钥`);
                    }
                }
                else {
                    this.logger.log(`【会话创建】未提供密钥ID，使用当前活跃密钥解密AES密钥...`);
                    decryptedAesKey = this.keyManagementService.decryptWithRsaPrivateKey(encryptedAesKey);
                    needsKeyUpdate = true;
                    this.logger.log(`【会话创建】客户端使用的是旧版本API，建议更新以支持密钥版本`);
                }
            }
            catch (decryptError) {
                if (decryptError.message.includes('请客户端重新获取公钥') ||
                    decryptError.message.includes('RSA密钥已更新') ||
                    decryptError.message.includes('RSA解密失败')) {
                    this.logger.warn(`【会话创建】RSA解密失败，可能是由于客户端使用了过期的RSA公钥: ${decryptError.message}`);
                    return {
                        success: false,
                        needsKeyUpdate: true,
                        errorMessage: '请重新获取RSA公钥'
                    };
                }
                throw decryptError;
            }
            let aesKeyData;
            try {
                aesKeyData = JSON.parse(decryptedAesKey.toString());
                this.logger.log(`【会话创建】成功解析AES密钥数据`);
            }
            catch (jsonError) {
                this.logger.error(`【会话创建】解析AES密钥JSON数据失败: ${jsonError.message}`);
                throw new Error('AES密钥数据格式无效');
            }
            if (!aesKeyData.key || !aesKeyData.iv) {
                this.logger.error(`【会话创建】AES密钥数据缺少必要字段`);
                throw new Error('AES密钥数据不完整');
            }
            const expirationTime = sessionType === SessionType.SECURE ?
                this.secureSessionTTL : this.standardSessionTTL;
            const sessionKey = {
                aesKey: Buffer.from(aesKeyData.key, 'base64'),
                iv: Buffer.from(aesKeyData.iv, 'base64'),
                expiresAt: Date.now() + expirationTime,
                sessionType: sessionType,
                clientInfo: clientInfo
            };
            this.logger.log(`【会话创建】生成${sessionType === SessionType.SECURE ? '安全' : '标准'}会话密钥，AES密钥指纹: ${this.getKeyFingerprint(sessionKey.aesKey)}, IV指纹: ${this.getKeyFingerprint(sessionKey.iv)}`);
            this.logger.log(`【会话创建】会话将于 ${new Date(sessionKey.expiresAt).toISOString()} 过期`);
            this.sessionCache.set(sessionId, sessionKey);
            const redisSessionData = {
                aesKey: sessionKey.aesKey.toString('base64'),
                iv: sessionKey.iv.toString('base64'),
                expiresAt: sessionKey.expiresAt,
                sessionType: sessionKey.sessionType,
                clientInfo: clientInfo,
                createdAt: Date.now()
            };
            try {
                await this.redisSessionService.storeSession(sessionId, redisSessionData, sessionType);
            }
            catch (redisError) {
                this.logger.error(`【会话创建】存储会话到Redis失败: ${redisError.message}，但会话已存储在内存缓存中`);
            }
            this.logger.log(`【会话创建】${sessionType === SessionType.SECURE ? '安全' : '标准'}会话 ${sessionId} 创建成功`);
            return { success: true, needsKeyUpdate };
        }
        catch (error) {
            this.logger.error(`【会话创建】创建会话密钥失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message
            };
        }
    }
    async createSecureSession(sessionId, encryptedAesKey, keyId = '', clientInfo) {
        return this.createSession(sessionId, encryptedAesKey, keyId, SessionType.SECURE, clientInfo);
    }
    async encrypt(data, sessionId) {
        try {
            const sessionKey = await this.getSessionKey(sessionId);
            const cipher = crypto.createCipheriv('aes-256-cbc', sessionKey.aesKey, sessionKey.iv);
            let encrypted = cipher.update(data, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            return encrypted;
        }
        catch (error) {
            this.logger.error(`【加密】加密数据失败: ${error.message}`, error.stack);
            throw new Error('数据加密失败');
        }
    }
    async decrypt(encryptedData, sessionId) {
        try {
            this.logger.log(`【解密】为会话 ${sessionId} 解密数据，加密数据长度: ${encryptedData.length}`);
            const sessionKey = await this.getSessionKey(sessionId);
            this.logger.log(`【解密】成功获取会话密钥，AES密钥指纹: ${this.getKeyFingerprint(sessionKey.aesKey)}`);
            this.logger.log(`【解密】使用AES-256-CBC算法解密...`);
            const decipher = crypto.createDecipheriv('aes-256-cbc', sessionKey.aesKey, sessionKey.iv);
            let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
            decrypted += decipher.final('utf8');
            this.logger.log(`【解密】数据解密成功，解密后数据长度: ${decrypted.length}`);
            return decrypted;
        }
        catch (error) {
            this.logger.error(`【解密】解密数据失败: ${error.message}`, error.stack);
            throw new Error('数据解密失败');
        }
    }
    encryptWithSystemKey(data) {
        this.logger.log(`【系统加密】使用系统密钥加密数据，数据长度: ${data.length}`);
        const encrypted = this.keyManagementService.encryptWithSystemKey(data);
        this.logger.log(`【系统加密】系统密钥加密成功，加密后数据长度: ${encrypted.length}`);
        return encrypted;
    }
    decryptWithSystemKey(encryptedData) {
        this.logger.log(`【系统解密】使用系统密钥解密数据，加密数据长度: ${encryptedData.length}`);
        const decrypted = this.keyManagementService.decryptWithSystemKey(encryptedData);
        this.logger.log(`【系统解密】系统密钥解密成功，解密后数据长度: ${decrypted.length}`);
        return decrypted;
    }
    async getSessionKey(sessionId) {
        try {
            if (this.sessionCache.has(sessionId)) {
                const cachedSession = this.sessionCache.get(sessionId);
                if (cachedSession.expiresAt > Date.now()) {
                    return this.parseSessionKey(cachedSession);
                }
                else {
                    this.sessionCache.delete(sessionId);
                }
            }
            const redisSession = await this.redisSessionService.getSession(sessionId);
            if (!redisSession) {
                this.logger.error(`【会话】会话ID不存在或已过期: ${sessionId}`);
                throw new Error(`会话ID不存在或已过期: ${sessionId}`);
            }
            const sessionKey = {
                aesKey: Buffer.from(redisSession.aesKey, 'base64'),
                iv: Buffer.from(redisSession.iv, 'base64'),
                expiresAt: redisSession.expiresAt,
                sessionType: redisSession.sessionType,
                clientInfo: redisSession.clientInfo
            };
            this.sessionCache.set(sessionId, sessionKey);
            return sessionKey;
        }
        catch (error) {
            this.logger.error(`【会话】获取会话密钥失败: ${error.message}`, error.stack);
            throw new Error('获取会话密钥失败');
        }
    }
    parseSessionKey(sessionKey) {
        if (sessionKey.protected) {
            try {
                const decrypted = this.keyManagementService.decryptWithSystemKey(sessionKey.data);
                const parsed = JSON.parse(decrypted);
                return {
                    aesKey: Buffer.from(parsed.key, 'base64'),
                    iv: Buffer.from(parsed.iv, 'base64'),
                    expiresAt: parsed.expiresAt,
                    sessionType: parsed.sessionType,
                    clientInfo: parsed.clientInfo
                };
            }
            catch (error) {
                this.logger.error(`解析受保护会话密钥失败: ${error.message}`);
                throw new Error('解析会话密钥失败');
            }
        }
        return sessionKey;
    }
    async getSessionType(sessionId) {
        try {
            if (this.sessionCache.has(sessionId)) {
                const session = this.sessionCache.get(sessionId);
                if (session.sessionType) {
                    return session.sessionType;
                }
            }
            const session = await this.redisSessionService.getSession(sessionId);
            return session?.sessionType;
        }
        catch (error) {
            this.logger.error(`【会话】获取会话类型失败: ${error.message}`);
            return undefined;
        }
    }
    async isSecureSession(sessionId) {
        const type = await this.getSessionType(sessionId);
        return type === SessionType.SECURE;
    }
    async deleteSession(sessionId) {
        try {
            this.logger.log(`【会话删除】删除会话: ${sessionId}`);
            this.sessionCache.delete(sessionId);
            const result = await this.redisSessionService.removeSession(sessionId);
            this.logger.log(`【会话删除】会话 ${sessionId} ${result ? '删除成功' : '删除失败'}`);
            return result;
        }
        catch (error) {
            this.logger.error(`【会话删除】删除会话失败: ${error.message}`, error.stack);
            return false;
        }
    }
    async cleanupExpiredSessions() {
        try {
            this.logger.log('【会话清理】开始清理过期会话...');
            const now = Date.now();
            let expiredCount = 0;
            for (const [sessionId, session] of this.sessionCache.entries()) {
                if (session.expiresAt < now) {
                    this.sessionCache.delete(sessionId);
                    expiredCount++;
                }
            }
            this.logger.log(`【会话清理】已清理内存中的 ${expiredCount} 个过期会话`);
            await this.logSessionStats();
        }
        catch (error) {
            this.logger.error(`【会话清理】清理过期会话失败: ${error.message}`, error.stack);
        }
    }
    async logSessionStats() {
        try {
            let standardCount = 0;
            let secureCount = 0;
            for (const session of this.sessionCache.values()) {
                if (session.sessionType === SessionType.SECURE) {
                    secureCount++;
                }
                else {
                    standardCount++;
                }
            }
            const redisSessionCount = await this.redisSessionService.getSessionCount();
            this.logger.log('【会话统计】当前会话状态:');
            this.logger.log(`【会话统计】内存中会话总数: ${this.sessionCache.size} (标准: ${standardCount}, 安全: ${secureCount})`);
            this.logger.log(`【会话统计】Redis中会话总数: ${redisSessionCount}`);
        }
        catch (error) {
            this.logger.error(`【会话统计】获取会话统计信息失败: ${error.message}`, error.stack);
        }
    }
    async createSessionDebug(sessionId, aesKeyBase64, aesIvBase64) {
        try {
            this.logger.warn(`【调试】创建调试会话: ${sessionId}`);
            if (!aesKeyBase64 || !aesIvBase64) {
                throw new Error('调试会话需要提供AES密钥和IV');
            }
            const sessionKey = {
                aesKey: Buffer.from(aesKeyBase64, 'base64'),
                iv: Buffer.from(aesIvBase64, 'base64'),
                expiresAt: Date.now() + this.standardSessionTTL,
                sessionType: SessionType.STANDARD
            };
            this.sessionCache.set(sessionId, sessionKey);
            const redisSessionData = {
                aesKey: aesKeyBase64,
                iv: aesIvBase64,
                expiresAt: sessionKey.expiresAt,
                sessionType: sessionKey.sessionType,
                createdAt: Date.now()
            };
            await this.redisSessionService.storeSession(sessionId, redisSessionData, SessionType.STANDARD);
            this.logger.warn(`【调试】调试会话创建成功: ${sessionId}, 密钥指纹: ${this.getKeyFingerprint(sessionKey.aesKey)}`);
        }
        catch (error) {
            this.logger.error(`【调试】创建调试会话失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.EncryptionService = EncryptionService;
exports.EncryptionService = EncryptionService = EncryptionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [key_management_service_1.KeyManagementService,
        redis_session_service_1.RedisSessionService])
], EncryptionService);
//# sourceMappingURL=encryption.service.js.map