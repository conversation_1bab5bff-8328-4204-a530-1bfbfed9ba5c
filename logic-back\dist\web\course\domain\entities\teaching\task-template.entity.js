"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskTemplate = void 0;
const typeorm_1 = require("typeorm");
const course_entity_1 = require("../management/course.entity");
let TaskTemplate = class TaskTemplate {
    id;
    courseId;
    taskName;
    taskDescription;
    durationDays;
    attachments;
    workIdsStr;
    selfAssessmentItems;
    status;
    attachmentsCount;
    assessmentItemsCount;
    firstAttachmentType;
    createdAt;
    updatedAt;
    course;
};
exports.TaskTemplate = TaskTemplate;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '任务模板ID' }),
    __metadata("design:type", Number)
], TaskTemplate.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_course'),
    (0, typeorm_1.Index)('idx_course_status'),
    (0, typeorm_1.Column)({ type: 'int', name: 'course_id', comment: '所属课程ID' }),
    __metadata("design:type", Number)
], TaskTemplate.prototype, "courseId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'task_name', length: 100, comment: '任务名称' }),
    __metadata("design:type", String)
], TaskTemplate.prototype, "taskName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', name: 'task_description', nullable: true, comment: '任务描述' }),
    __metadata("design:type", String)
], TaskTemplate.prototype, "taskDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'duration_days', default: 7, comment: '任务持续天数' }),
    __metadata("design:type", Number)
], TaskTemplate.prototype, "durationDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true, comment: '附件/资源链接' }),
    __metadata("design:type", Object)
], TaskTemplate.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'work_ids_str', length: 255, nullable: true, comment: '关联作品ID字符串' }),
    __metadata("design:type", String)
], TaskTemplate.prototype, "workIdsStr", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', name: 'self_assessment_items', nullable: true, comment: '自评项列表' }),
    __metadata("design:type", Object)
], TaskTemplate.prototype, "selfAssessmentItems", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Index)('idx_course_status'),
    (0, typeorm_1.Column)({ type: 'tinyint', default: 1, comment: '模板状态：0=禁用，1=启用' }),
    __metadata("design:type", Number)
], TaskTemplate.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_attachments_count'),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'attachments_count',
        nullable: true,
        comment: '附件数量',
        select: false,
        insert: false,
        update: false,
    }),
    __metadata("design:type", Number)
], TaskTemplate.prototype, "attachmentsCount", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_assessment_items_count'),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'assessment_items_count',
        nullable: true,
        comment: '自评项数量',
        select: false,
        insert: false,
        update: false,
    }),
    __metadata("design:type", Number)
], TaskTemplate.prototype, "assessmentItemsCount", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_first_attachment_type'),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        name: 'first_attachment_type',
        nullable: true,
        comment: '第一个附件类型',
        select: false,
        insert: false,
        update: false,
    }),
    __metadata("design:type", String)
], TaskTemplate.prototype, "firstAttachmentType", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], TaskTemplate.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], TaskTemplate.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => course_entity_1.Course, course => course.id),
    (0, typeorm_1.JoinColumn)({ name: 'course_id' }),
    __metadata("design:type", course_entity_1.Course)
], TaskTemplate.prototype, "course", void 0);
exports.TaskTemplate = TaskTemplate = __decorate([
    (0, typeorm_1.Entity)('course_class_task_templates')
], TaskTemplate);
//# sourceMappingURL=task-template.entity.js.map