"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateActivitySubmitDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_activity_submit_dto_1 = require("./create-activity-submit.dto");
const swagger_2 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateActivitySubmitDto extends (0, swagger_1.PartialType)(create_activity_submit_dto_1.CreateActivitySubmitDto) {
    status;
    reviewReason;
    reviewerId;
    reviewTime;
}
exports.UpdateActivitySubmitDto = UpdateActivitySubmitDto;
__decorate([
    (0, swagger_2.ApiProperty)({ description: '报名状态：0-已报名 1-已取消 2-已审核通过 3-已审核拒绝', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '报名状态必须是数字' }),
    __metadata("design:type", Number)
], UpdateActivitySubmitDto.prototype, "status", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({ description: '审核原因', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '审核原因必须是字符串' }),
    __metadata("design:type", String)
], UpdateActivitySubmitDto.prototype, "reviewReason", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({ description: '审核人ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '审核人ID必须是数字' }),
    __metadata("design:type", Number)
], UpdateActivitySubmitDto.prototype, "reviewerId", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({ description: '审核时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)({ message: '审核时间必须是有效日期' }),
    __metadata("design:type", Date)
], UpdateActivitySubmitDto.prototype, "reviewTime", void 0);
//# sourceMappingURL=update-activity-submit.dto.js.map