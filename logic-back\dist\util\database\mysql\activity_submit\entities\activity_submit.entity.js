"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitySubmit = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const activity_entity_1 = require("../../activity/entities/activity.entity");
let ActivitySubmit = class ActivitySubmit {
    id;
    activityId;
    userId;
    userName;
    userPhone;
    agreementAccepted;
    parentConsentAccepted;
    parentSignaturePath;
    signatureTime;
    signatureIp;
    status;
    reviewReason;
    reviewerId;
    reviewTime;
    createTime;
    updateTime;
    isDelete;
    remark;
    activity;
};
exports.ActivitySubmit = ActivitySubmit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], ActivitySubmit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动ID' }),
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    __metadata("design:type", Number)
], ActivitySubmit.prototype, "activityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], ActivitySubmit.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '用户姓名' }),
    (0, swagger_1.ApiProperty)({ description: '用户姓名' }),
    __metadata("design:type", String)
], ActivitySubmit.prototype, "userName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '用户手机号', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '用户手机号', required: false }),
    __metadata("design:type", String)
], ActivitySubmit.prototype, "userPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否同意参赛协议', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否同意参赛协议', default: false }),
    __metadata("design:type", Boolean)
], ActivitySubmit.prototype, "agreementAccepted", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否同意家长知情同意书', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否同意家长知情同意书', default: false }),
    __metadata("design:type", Boolean)
], ActivitySubmit.prototype, "parentConsentAccepted", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '家长签名文件路径', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '家长签名文件路径', required: false }),
    __metadata("design:type", String)
], ActivitySubmit.prototype, "parentSignaturePath", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '签名时间', type: 'datetime', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '签名时间', required: false }),
    __metadata("design:type", Date)
], ActivitySubmit.prototype, "signatureTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '签名IP地址', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '签名IP地址', required: false }),
    __metadata("design:type", String)
], ActivitySubmit.prototype, "signatureIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '报名状态：0-已报名 1-已取消 2-已审核通过 3-已审核拒绝', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '报名状态：0-已报名 1-已取消 2-已审核通过 3-已审核拒绝', default: 0 }),
    __metadata("design:type", Number)
], ActivitySubmit.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核原因', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '审核原因', required: false }),
    __metadata("design:type", String)
], ActivitySubmit.prototype, "reviewReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核人ID', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '审核人ID', required: false }),
    __metadata("design:type", Number)
], ActivitySubmit.prototype, "reviewerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核时间', type: 'datetime', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '审核时间', required: false }),
    __metadata("design:type", Date)
], ActivitySubmit.prototype, "reviewTime", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '报名时间' }),
    (0, swagger_1.ApiProperty)({ description: '报名时间' }),
    __metadata("design:type", Date)
], ActivitySubmit.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ActivitySubmit.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否删除', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false }),
    __metadata("design:type", Boolean)
], ActivitySubmit.prototype, "isDelete", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '备注信息', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    __metadata("design:type", String)
], ActivitySubmit.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => activity_entity_1.Activity, activity => activity.id),
    (0, typeorm_1.JoinColumn)({ name: 'activityId' }),
    __metadata("design:type", activity_entity_1.Activity)
], ActivitySubmit.prototype, "activity", void 0);
exports.ActivitySubmit = ActivitySubmit = __decorate([
    (0, typeorm_1.Entity)('activity_submit')
], ActivitySubmit);
//# sourceMappingURL=activity_submit.entity.js.map