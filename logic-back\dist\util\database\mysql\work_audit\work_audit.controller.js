"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkAuditController = void 0;
const common_1 = require("@nestjs/common");
const work_audit_service_1 = require("./work_audit.service");
const create_work_audit_dto_1 = require("./dto/create-work_audit.dto");
const update_work_audit_dto_1 = require("./dto/update-work_audit.dto");
const swagger_1 = require("@nestjs/swagger");
const work_audit_entity_1 = require("./entities/work_audit.entity");
let WorkAuditController = class WorkAuditController {
    workAuditService;
    constructor(workAuditService) {
        this.workAuditService = workAuditService;
    }
    create(createWorkAuditDto) {
        return this.workAuditService.create(createWorkAuditDto);
    }
    findAll() {
        return this.workAuditService.findAll();
    }
    findOne(id) {
        return this.workAuditService.findOne(+id);
    }
    update(id, updateWorkAuditDto) {
        return this.workAuditService.update(+id, updateWorkAuditDto);
    }
    remove(id) {
        return this.workAuditService.remove(+id);
    }
};
exports.WorkAuditController = WorkAuditController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建作品审核记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: work_audit_entity_1.WorkAudit }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_work_audit_dto_1.CreateWorkAuditDto]),
    __metadata("design:returntype", void 0)
], WorkAuditController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有作品审核记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [work_audit_entity_1.WorkAudit] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], WorkAuditController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取单个作品审核记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '审核记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: work_audit_entity_1.WorkAudit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '审核记录未找到' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WorkAuditController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新作品审核记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '审核记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: work_audit_entity_1.WorkAudit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '审核记录未找到' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_work_audit_dto_1.UpdateWorkAuditDto]),
    __metadata("design:returntype", void 0)
], WorkAuditController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除作品审核记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '审核记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '审核记录未找到' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WorkAuditController.prototype, "remove", null);
exports.WorkAuditController = WorkAuditController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/作品审核管理(work_audit)'),
    (0, common_1.Controller)('work-audit'),
    __metadata("design:paramtypes", [work_audit_service_1.WorkAuditService])
], WorkAuditController);
//# sourceMappingURL=work_audit.controller.js.map