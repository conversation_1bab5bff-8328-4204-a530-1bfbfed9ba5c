import { Activity } from '../../activity/entities/activity.entity';
export declare class ActivityEventsTask {
    id: number;
    userId: number;
    activityId: number;
    eventName: string;
    startTime: Date;
    endTime: Date;
    workId: number;
    workFile: string;
    workDescription: string;
    instructorName: string;
    schoolName: string;
    contactPerson: string;
    contactPhone: string;
    realName: string;
    idNumber: string;
    affiliatedSchool: string;
    organization: string;
    instructorPhone: string;
    competitionGroup: string;
    registrationFormFile: string;
    status: number;
    creatorId: number;
    createTime: Date;
    updateTime: Date;
    isDelete: boolean;
    remark: string;
    activity: Activity;
}
