"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var XunfeiVoiceprintRecognitionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.XunfeiVoiceprintRecognitionService = void 0;
const common_1 = require("@nestjs/common");
const ai_providers_config_service_1 = require("../config/ai_providers-config.service");
const crypto = require("crypto");
let XunfeiVoiceprintRecognitionService = XunfeiVoiceprintRecognitionService_1 = class XunfeiVoiceprintRecognitionService {
    configService;
    logger = new common_1.Logger(XunfeiVoiceprintRecognitionService_1.name);
    appId;
    apiKey;
    apiSecret;
    apiUrl = 'https://api.xf-yun.com/v1/private/s782b4996';
    constructor(configService) {
        this.configService = configService;
        this.appId = this.configService.getXunfeiConfig().voiceprint.app.id;
        this.apiKey = this.configService.getXunfeiConfig().voiceprint.api.key;
        this.apiSecret = this.configService.getXunfeiConfig().voiceprint.api.secret;
        this.logger.log(`初始化讯飞声纹识别服务`);
        this.logger.log(`AppID: ${this.appId.substring(0, 4)}****`);
        this.logger.log(`API URL: ${this.apiUrl}`);
    }
    async createGroup(groupId, groupName, groupInfo) {
        this.logger.log(`创建声纹特征库: ${groupId}, 名称: ${groupName}`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "createGroup",
                    groupId: groupId,
                    groupName: groupName,
                    groupInfo: groupInfo,
                    createGroupRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'createGroupRes');
        }
        catch (error) {
            this.logger.error(`创建声纹特征库失败: ${error.message}`);
            throw error;
        }
    }
    async createFeature(groupId, featureId, featureInfo, audioBase64) {
        this.logger.log(`添加音频特征: ${featureId} 到特征库 ${groupId}`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "createFeature",
                    groupId: groupId,
                    featureId: featureId,
                    featureInfo: featureInfo,
                    createFeatureRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            },
            payload: {
                resource: {
                    encoding: "lame",
                    sample_rate: 16000,
                    channels: 1,
                    bit_depth: 16,
                    status: 3,
                    audio: audioBase64
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'createFeatureRes');
        }
        catch (error) {
            this.logger.error(`添加音频特征失败: ${error.message}`);
            throw error;
        }
    }
    async compareFeature(groupId, dstFeatureId, audioBase64) {
        this.logger.log(`声纹特征1:1比对: ${dstFeatureId} 在特征库 ${groupId}`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "searchScoreFea",
                    groupId: groupId,
                    dstFeatureId: dstFeatureId,
                    searchScoreFeaRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            },
            payload: {
                resource: {
                    encoding: "lame",
                    sample_rate: 16000,
                    channels: 1,
                    bit_depth: 16,
                    status: 3,
                    audio: audioBase64
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'searchScoreFeaRes');
        }
        catch (error) {
            this.logger.error(`声纹特征1:1比对失败: ${error.message}`);
            throw error;
        }
    }
    async searchFeature(groupId, audioBase64, topK = 3) {
        this.logger.log(`声纹特征1:N检索: 在特征库 ${groupId} 中检索前 ${topK} 条结果`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "searchFea",
                    groupId: groupId,
                    topK: topK,
                    searchFeaRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            },
            payload: {
                resource: {
                    encoding: "lame",
                    sample_rate: 16000,
                    channels: 1,
                    bit_depth: 16,
                    status: 3,
                    audio: audioBase64
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'searchFeaRes');
        }
        catch (error) {
            this.logger.error(`声纹特征1:N检索失败: ${error.message}`);
            throw error;
        }
    }
    async queryFeatureList(groupId) {
        this.logger.log(`查询特征库 ${groupId} 的特征列表`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "queryFeatureList",
                    groupId: groupId,
                    queryFeatureListRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'queryFeatureListRes');
        }
        catch (error) {
            this.logger.error(`查询特征列表失败: ${error.message}`);
            throw error;
        }
    }
    async updateFeature(groupId, featureId, featureInfo, audioBase64) {
        this.logger.log(`更新音频特征: ${featureId} 在特征库 ${groupId}`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "updateFeature",
                    groupId: groupId,
                    featureId: featureId,
                    featureInfo: featureInfo,
                    updateFeatureRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            },
            payload: {
                resource: {
                    encoding: "lame",
                    sample_rate: 16000,
                    channels: 1,
                    bit_depth: 16,
                    status: 3,
                    audio: audioBase64
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'updateFeatureRes');
        }
        catch (error) {
            this.logger.error(`更新音频特征失败: ${error.message}`);
            throw error;
        }
    }
    async deleteFeature(groupId, featureId) {
        this.logger.log(`删除音频特征: ${featureId} 从特征库 ${groupId}`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "deleteFeature",
                    groupId: groupId,
                    featureId: featureId,
                    deleteFeatureRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'deleteFeatureRes');
        }
        catch (error) {
            this.logger.error(`删除音频特征失败: ${error.message}`);
            throw error;
        }
    }
    async deleteGroup(groupId) {
        this.logger.log(`删除声纹特征库: ${groupId}`);
        const params = {
            header: {
                app_id: this.appId,
                status: 3
            },
            parameter: {
                s782b4996: {
                    func: "deleteGroup",
                    groupId: groupId,
                    deleteGroupRes: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            }
        };
        try {
            const result = await this.doApiRequest(params);
            return this.parseApiResponse(result, 'deleteGroupRes');
        }
        catch (error) {
            this.logger.error(`删除声纹特征库失败: ${error.message}`);
            throw error;
        }
    }
    async doApiRequest(params) {
        const url = this.buildRequestUrl();
        try {
            this.logger.log(`发送请求到: ${this.apiUrl}`);
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API请求失败，状态码: ${response.status}, 错误信息: ${errorText}`);
            }
            return await response.json();
        }
        catch (error) {
            this.logger.error(`API请求失败: ${error.message}`);
            throw error;
        }
    }
    buildRequestUrl() {
        const host = 'api.xf-yun.com';
        const path = '/v1/private/s782b4996';
        console.log(`host: ${host}`);
        console.log(`path: ${path}`);
        const date = new Date().toUTCString();
        console.log(`date: ${date}`);
        const signatureOrigin = `host: ${host}
date: ${date}
POST ${path} HTTP/1.1`;
        console.log(`signatureOrigin: ${signatureOrigin}`);
        console.log(`apiSecret: ${this.apiSecret}`);
        const signature = crypto.createHmac('sha256', this.apiSecret)
            .update(signatureOrigin)
            .digest('base64');
        console.log(`signature: ${signature}`);
        const authorizationOrigin = `api_key="${this.apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature}"`;
        console.log(`authorizationOrigin: ${authorizationOrigin}`);
        const authorization = Buffer.from(authorizationOrigin).toString('base64');
        console.log(`authorization: ${authorization}`);
        const encodedAuth = encodeURIComponent(authorization).replace(/%20/g, '+');
        const encodedHost = encodeURIComponent(host).replace(/%20/g, '+');
        const encodedDate = encodeURIComponent(date).replace(/%20/g, '+');
        const requestUrl = `${this.apiUrl}?authorization=${authorization}&host=${host}&date=${date}`;
        console.log('完整URL:', requestUrl);
        return requestUrl;
    }
    parseApiResponse(response, resField) {
        try {
            if (response &&
                response.header &&
                response.header.code === 0 &&
                response.payload &&
                response.payload[resField]) {
                const base64Text = response.payload[resField].text;
                if (!base64Text) {
                    return { success: true, result: {} };
                }
                const jsonText = Buffer.from(base64Text, 'base64').toString('utf8');
                const result = JSON.parse(jsonText);
                return { success: true, result };
            }
            else {
                const errorCode = response?.header?.code || -1;
                const errorMsg = response?.header?.message || '未知错误';
                throw new Error(`API错误: ${errorCode}, ${errorMsg}`);
            }
        }
        catch (error) {
            this.logger.error(`解析API响应失败: ${error.message}`);
            throw error;
        }
    }
};
exports.XunfeiVoiceprintRecognitionService = XunfeiVoiceprintRecognitionService;
exports.XunfeiVoiceprintRecognitionService = XunfeiVoiceprintRecognitionService = XunfeiVoiceprintRecognitionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ai_providers_config_service_1.AiProvidersConfigService])
], XunfeiVoiceprintRecognitionService);
//# sourceMappingURL=xunfei_voiceprint_recognition.service.js.map