import { WebEventsTaskService } from './web_events_task.service';
import { CreateActivityEventsTaskDto, UpdateActivityEventsTaskDto, BatchUpdateStatusDto, UpdateTaskStatusDto, SubmitEventsTaskDto } from 'src/util/database/mysql/activity_events_task/dto';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class WebEventsTaskController {
    private readonly webEventsTaskService;
    private readonly httpResponseResultService;
    constructor(webEventsTaskService: WebEventsTaskService, httpResponseResultService: HttpResponseResultService);
    createTask(createDto: CreateActivityEventsTaskDto, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getMyTasks(req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getActivityTasks(activityId: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getMyTaskStatistics(req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getUpcomingTasks(req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getOngoingTasks(req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getMyTasksByStatus(status: number, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    searchMyTasks(keyword: string, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    getTaskDetail(id: number, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    updateTask(id: number, updateDto: UpdateActivityEventsTaskDto, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    updateTaskStatus(id: number, updateStatusDto: UpdateTaskStatusDto, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    adminReviewTask(id: number, reviewDto: {
        status: number;
        remark?: string;
    }, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    submitTask(id: number, submitDto: SubmitEventsTaskDto, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    checkSubmitStatus(id: number, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    batchUpdateTaskStatus(batchUpdateDto: BatchUpdateStatusDto, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    deleteTask(id: number, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
}
