import { Course } from '../management/course.entity';
export declare class TaskTemplate {
    id: number;
    courseId: number;
    taskName: string;
    taskDescription: string;
    durationDays: number;
    attachments: Record<string, any>;
    workIdsStr: string;
    selfAssessmentItems: Record<string, any>;
    status: number;
    attachmentsCount: number;
    assessmentItemsCount: number;
    firstAttachmentType: string;
    createdAt: Date;
    updatedAt: Date;
    course: Course;
}
