import { HttpException } from '@nestjs/common';
export declare class ConcurrencyConflictException extends HttpException {
    constructor(courseId: number, classId: number, lockKey: string);
}
export declare class DuplicateOperationException extends HttpException {
    constructor(courseId: number, classId: number, teacherId: number, existingRecordId: number, lastExecutionTime: string);
}
export declare class CourseNotFoundOrNotPublishedException extends HttpException {
    constructor(courseId: number);
}
export declare class InsufficientStudentPointsException extends HttpException {
    constructor(studentId: number, currentPoints: number, requiredPoints: number, studentName?: string);
}
export declare class InsufficientTeacherPermissionException extends HttpException {
    constructor(teacherId: number, classId: number, courseId?: number, courseStatus?: number, creatorId?: number);
}
export declare class EmptyClassException extends HttpException {
    constructor(classId: number, studentCount?: number);
}
export declare class IncompleteSettingsException extends HttpException {
    constructor(courseId: number, missingSettings: string[]);
}
export declare class PartialFailureException extends HttpException {
    constructor(data: any);
}
