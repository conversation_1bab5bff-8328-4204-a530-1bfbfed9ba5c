"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const operators_1 = require("rxjs/operators");
const encrypt_decorator_1 = require("./encrypt.decorator");
const encryption_service_1 = require("./encryption.service");
const crypto = require("crypto");
let EncryptInterceptor = class EncryptInterceptor {
    reflector;
    encryptionService;
    constructor(reflector, encryptionService) {
        this.reflector = reflector;
        this.encryptionService = encryptionService;
    }
    async intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const encryptOptions = this.reflector.get(encrypt_decorator_1.ENCRYPT_METADATA_KEY, context.getHandler()) || this.reflector.get(encrypt_decorator_1.ENCRYPT_METADATA_KEY, context.getClass());
        if (!encryptOptions || !encryptOptions.enabled) {
            return next.handle();
        }
        let sessionId = request.headers['x-session-id'];
        if (!sessionId && request.body && request.body.encryptedAesKey) {
            try {
                sessionId = crypto.randomUUID();
                const clientInfo = {
                    ip: request.ip,
                    userAgent: request.headers['user-agent'] || '未知'
                };
                const isSecureSession = encryptOptions.securityLevel === 'secure';
                const sessionType = isSecureSession ? encryption_service_1.SessionType.SECURE : encryption_service_1.SessionType.STANDARD;
                await this.encryptionService.createSession(sessionId, request.body.encryptedAesKey, request.body.keyId, sessionType, clientInfo);
                response.setHeader('X-Session-Id', sessionId);
                request.headers['x-session-id'] = sessionId;
                delete request.body.encryptedAesKey;
            }
            catch (error) {
                console.error('[Encrypt] 创建会话失败:', error);
            }
        }
        if (encryptOptions.decryptRequest) {
            try {
                const encryptedData = request.body?.data;
                if (encryptedData) {
                    const sessionId = request.headers['x-session-id'];
                    const decryptedData = await this.encryptionService.decrypt(encryptedData, sessionId);
                    request.body = JSON.parse(decryptedData);
                }
            }
            catch (error) {
                console.error('解密请求数据失败', error);
            }
        }
        return next.handle().pipe((0, operators_1.map)(async (data) => {
            if (!data)
                return data;
            try {
                const sessionId = request.headers['x-session-id'];
                if (!sessionId) {
                    console.error('缺少会话ID（X-Session-ID），跳过加密处理');
                    return data;
                }
                if (encryptOptions.encryptFields && encryptOptions.encryptFields.length > 0) {
                    const result = JSON.parse(JSON.stringify(data));
                    for (const fieldPath of encryptOptions.encryptFields) {
                        const pathParts = fieldPath.split('.');
                        let target = result;
                        let parent = null;
                        let lastKey = null;
                        for (let i = 0; i < pathParts.length; i++) {
                            const part = pathParts[i];
                            if (i === pathParts.length - 1) {
                                lastKey = part;
                                if (target && target[lastKey] !== undefined) {
                                    parent = target;
                                    const valueToEncrypt = JSON.stringify(target[lastKey]);
                                    if (parent) {
                                        parent[lastKey] = await this.encryptionService.encrypt(valueToEncrypt, sessionId);
                                    }
                                }
                                else {
                                }
                            }
                            else {
                                if (target && typeof target[part] === 'object') {
                                    parent = target;
                                    target = target[part];
                                }
                                else {
                                    break;
                                }
                            }
                        }
                    }
                    result.partialEncrypted = true;
                    result.encryptedFields = encryptOptions.encryptFields;
                    return result;
                }
                const encryptedResult = {
                    encrypted: true,
                    data: await this.encryptionService.encrypt(JSON.stringify(data), sessionId),
                };
                return encryptedResult;
            }
            catch (error) {
                console.error('加密响应数据失败', error);
                console.error('[Encrypt] 详细错误信息:', error instanceof Error ? error.stack : '未知错误');
                console.error('[Encrypt] 请求URL:', request.url);
                console.error('[Encrypt] 加密选项:', JSON.stringify(encryptOptions));
                return data;
            }
        }), (0, operators_1.tap)({
            next: async (result) => {
                const sessionId = request.headers['x-session-id'];
                if (sessionId && encryptOptions && encryptOptions.autoDestroySession) {
                    await this.encryptionService.deleteSession(sessionId);
                }
                return result;
            },
            error: async (error) => {
                const sessionId = request.headers['x-session-id'];
                if (sessionId && encryptOptions && encryptOptions.securityLevel === 'secure') {
                    await this.encryptionService.deleteSession(sessionId);
                }
            }
        }));
    }
};
exports.EncryptInterceptor = EncryptInterceptor;
exports.EncryptInterceptor = EncryptInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        encryption_service_1.EncryptionService])
], EncryptInterceptor);
//# sourceMappingURL=encrypt.interceptor.js.map