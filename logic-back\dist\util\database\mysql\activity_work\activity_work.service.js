"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityWorkService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_work_entity_1 = require("./entities/activity_work.entity");
let ActivityWorkService = class ActivityWorkService {
    activityWorkRepository;
    constructor(activityWorkRepository) {
        this.activityWorkRepository = activityWorkRepository;
    }
    async create(createActivityWorkDto) {
        const activityWork = this.activityWorkRepository.create(createActivityWorkDto);
        return this.activityWorkRepository.save(activityWork);
    }
    async findAll() {
        return this.activityWorkRepository.find({
            where: { isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findOne(id) {
        const activityWork = await this.activityWorkRepository.findOne({
            where: { id, isDelete: false }
        });
        if (!activityWork) {
            throw new common_1.NotFoundException(`活动作品关联ID ${id} 未找到`);
        }
        return activityWork;
    }
    async update(id, updateActivityWorkDto) {
        await this.activityWorkRepository.update(id, updateActivityWorkDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.activityWorkRepository.update(id, { isDelete: true });
    }
    async hardRemove(id) {
        await this.activityWorkRepository.delete(id);
    }
    async findByActivityId(activityId) {
        return this.activityWorkRepository.find({
            where: { activityId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByWorkId(workId) {
        return this.activityWorkRepository.find({
            where: { workId, isDelete: false },
        });
    }
    async findByUserId(userId) {
        return this.activityWorkRepository.find({
            where: { userId, isDelete: false },
        });
    }
    async findByIsSelected(isSelected) {
        return this.activityWorkRepository.find({
            where: { isSelected, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findWinners() {
        return this.activityWorkRepository.find({
            where: { isWinner: 1, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async updateIsSelected(id, isSelected) {
        await this.activityWorkRepository.update(id, { isSelected });
        return this.findOne(id);
    }
    async updateIsWinner(id, isWinner) {
        await this.activityWorkRepository.update(id, { isWinner });
        return this.findOne(id);
    }
    async removeByActivityId(activityId) {
        await this.activityWorkRepository.update({ activityId }, { isDelete: true });
    }
    async addActivityWorks(activityId, works) {
        const activityWorks = works.map(work => ({
            activityId,
            workId: work.workId,
            userId: work.userId || 0,
            workTitle: '',
            workCover: '',
            workDesc: '',
            isSelected: 1,
            isWinner: work.isAwarded ? 1 : 0,
            isShow: 1,
            creatorId: work.userId || 0,
            isDelete: false,
        }));
        await this.activityWorkRepository.save(activityWorks);
        return true;
    }
    async addImageToActivity(activityId, works) {
        return await this.addActivityWorks(activityId, works);
    }
    async updateActivityWorks(activityId, works) {
        await this.removeByActivityId(activityId);
        return await this.addActivityWorks(activityId, works);
    }
    async checkUserSubmitted(activityId, userId) {
        if (!userId)
            return false;
        const count = await this.activityWorkRepository.count({
            where: { activityId, userId, isDelete: false },
        });
        return count > 0;
    }
    async getActivityWorks(activityId, filters) {
        const where = { activityId, isDelete: false };
        if (filters?.isAwarded !== undefined) {
            where.isWinner = filters.isAwarded ? 1 : 0;
        }
        if (filters?.userId !== undefined) {
            where.userId = filters.userId;
        }
        return await this.activityWorkRepository.find({
            where,
            relations: ['userWorkInfo'],
            order: { createTime: 'DESC' },
        });
    }
    async deleteActivityWork(id) {
        await this.remove(id);
        return true;
    }
    async deleteActivityWorks(activityId, workIds) {
        for (const workId of workIds) {
            await this.activityWorkRepository.update({ activityId, workId, isDelete: false }, { isDelete: true });
        }
        return true;
    }
    async setWorkAwarded(id, isAwarded) {
        await this.activityWorkRepository.update(id, { isWinner: isAwarded ? 1 : 0 });
        return true;
    }
    async updateWorkCategory(id, category) {
        return true;
    }
    async updateWorkStatus(id, status) {
        await this.activityWorkRepository.update(id, { isSelected: status });
        return true;
    }
    async cancelUserSubmission(id, userId) {
        const activityWork = await this.findOne(id);
        if (activityWork.userId !== userId) {
            throw new Error('无权限取消该报名');
        }
        await this.remove(id);
        return true;
    }
};
exports.ActivityWorkService = ActivityWorkService;
exports.ActivityWorkService = ActivityWorkService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_work_entity_1.ActivityWork)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ActivityWorkService);
//# sourceMappingURL=activity_work.service.js.map