"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLoggerConfig = createLoggerConfig;
const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const path = require("path");
const fs = require("fs");
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}
const logFormat = winston.format.combine(winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
}), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]`;
    if (context) {
        logMessage += ` [${context}]`;
    }
    logMessage += ` ${message}`;
    if (Object.keys(meta).length > 0) {
        logMessage += ` ${JSON.stringify(meta)}`;
    }
    if (trace) {
        logMessage += `\n${trace}`;
    }
    return logMessage;
}));
const consoleFormat = winston.format.combine(winston.format.colorize(), winston.format.timestamp({
    format: 'HH:mm:ss.SSS'
}), winston.format.printf(({ timestamp, level, message, context }) => {
    let logMessage = `${timestamp} ${level}`;
    if (context) {
        logMessage += ` [${context}]`;
    }
    logMessage += ` ${message}`;
    return logMessage;
}));
function createLoggerConfig() {
    const isDevelopment = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development';
    const transports = [];
    if (isDevelopment) {
        transports.push(new winston.transports.Console({
            format: consoleFormat,
            level: 'debug'
        }));
    }
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'application-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'debug'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'error'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'http-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'info'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'database-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'debug'
    }));
    return {
        transports,
        format: logFormat,
        defaultMeta: {
            service: 'logic-back',
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            pid: process.pid,
            hostname: require('os').hostname()
        },
        exitOnError: false,
        exceptionHandlers: [
            new DailyRotateFile({
                filename: path.join(logsDir, 'exceptions-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                zippedArchive: true,
                maxSize: '20m',
                maxFiles: '30d',
                format: logFormat
            })
        ],
        rejectionHandlers: [
            new DailyRotateFile({
                filename: path.join(logsDir, 'rejections-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                zippedArchive: true,
                maxSize: '20m',
                maxFiles: '30d',
                format: logFormat
            })
        ]
    };
}
//# sourceMappingURL=logger.config.js.map