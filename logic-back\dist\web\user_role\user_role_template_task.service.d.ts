import { OnModuleInit } from '@nestjs/common';
import { Repository } from 'typeorm';
import { RolePermissionTemplate } from 'src/util/database/mysql/role_permission_templates/entities/role_permission_template.entity';
import { RoleTemplateExtensionPermission } from 'src/util/database/mysql/role_template_extension_permission/entities/role_template_extension_permission.entity';
import { RoleTemplateBlockPermission } from 'src/util/database/mysql/role_template_block_permission/entities/role_template_block_permission.entity';
import { Block } from 'src/util/database/mysql/block/entities/block.entity';
import { Extension } from 'src/util/database/mysql/extensions/entities/extension.entity';
export declare class UserRoleTemplateTaskService implements OnModuleInit {
    private templateRepository;
    private extensionPermissionRepository;
    private blockPermissionRepository;
    private blockRepository;
    private extensionRepository;
    private readonly logger;
    constructor(templateRepository: Repository<RolePermissionTemplate>, extensionPermissionRepository: Repository<RoleTemplateExtensionPermission>, blockPermissionRepository: Repository<RoleTemplateBlockPermission>, blockRepository: Repository<Block>, extensionRepository: Repository<Extension>);
    onModuleInit(): Promise<void>;
    private executeStartupTask;
    private checkDatabaseTables;
    private ensureSpecialTemplateWithAllPermissions;
    private getAllExtensionsAndBlocks;
}
