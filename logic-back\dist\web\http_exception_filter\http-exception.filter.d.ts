import { ExceptionFilter, ArgumentsHost, HttpException } from '@nestjs/common';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class HttpExceptionFilter implements ExceptionFilter {
    private readonly httpResponseResultService;
    constructor(httpResponseResultService: HttpResponseResultService);
    catch(exception: HttpException, host: ArgumentsHost): void;
}
