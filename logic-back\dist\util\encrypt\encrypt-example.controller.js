"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptExampleController = void 0;
const common_1 = require("@nestjs/common");
const encrypt_decorator_1 = require("./encrypt.decorator");
let EncryptExampleController = class EncryptExampleController {
    getPublicData() {
        return {
            success: true,
            data: {
                message: '这是公开数据，无需加密',
                timestamp: new Date().toISOString()
            }
        };
    }
    getSecureData() {
        return {
            success: true,
            data: {
                message: '这是敏感数据，需要加密',
                secretInfo: '这是机密信息',
                timestamp: new Date().toISOString()
            }
        };
    }
    getPartialSecureData(id) {
        console.log(`[EncryptExample] partial-secure API被调用，ID: ${id}`);
        console.log('[EncryptExample] 使用加密字段: data.data.data.secretInfo, data.data.data.sensitiveData');
        const responseData = {
            code: 200,
            data: {
                success: true,
                data: {
                    id: id,
                    publicInfo: '这是公开信息',
                    secretInfo: {
                        cardNo: '6225123456789012',
                        cvv: '123'
                    },
                    sensitiveData: '这部分内容会被加密',
                    timestamp: new Date().toISOString()
                }
            }
        };
        console.log('[EncryptExample] 返回的响应数据结构:', JSON.stringify(responseData));
        return responseData;
    }
    getSecureWithDecrypt() {
        return {
            success: true,
            data: {
                message: '这是需要解密请求的接口',
                timestamp: new Date().toISOString()
            }
        };
    }
    testRequestBodyEncryption(body) {
        console.log('[加密测试] 收到解密后的请求体:', JSON.stringify(body));
        return {
            success: true,
            data: {
                message: '成功测试请求体加密',
                receivedData: body,
                timestamp: new Date().toISOString()
            }
        };
    }
    getSimplePartialSecureData(id) {
        console.log(`[EncryptExample] simple-partial API被调用，ID: ${id}`);
        console.log('[EncryptExample] 使用加密字段: secretInfo, sensitiveData');
        const responseData = {
            id: id,
            publicInfo: '这是公开信息',
            secretInfo: {
                cardNo: '6225123456789012',
                cvv: '123'
            },
            sensitiveData: '这部分内容会被加密',
            timestamp: new Date().toISOString()
        };
        console.log('[EncryptExample] 返回的响应数据结构:', JSON.stringify(responseData));
        return responseData;
    }
};
exports.EncryptExampleController = EncryptExampleController;
__decorate([
    (0, common_1.Get)('public-data'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], EncryptExampleController.prototype, "getPublicData", null);
__decorate([
    (0, common_1.Get)('secure-data'),
    (0, encrypt_decorator_1.Encrypt)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], EncryptExampleController.prototype, "getSecureData", null);
__decorate([
    (0, common_1.Get)('partial-secure/:id'),
    (0, encrypt_decorator_1.Encrypt)({
        enabled: true,
        encryptFields: ['data.data.data.secretInfo', 'data.data.data.sensitiveData']
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], EncryptExampleController.prototype, "getPartialSecureData", null);
__decorate([
    (0, common_1.Get)('secure-with-decrypt'),
    (0, encrypt_decorator_1.Encrypt)({
        decryptRequest: true
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], EncryptExampleController.prototype, "getSecureWithDecrypt", null);
__decorate([
    (0, common_1.Post)('test-request-body-encryption'),
    (0, encrypt_decorator_1.Encrypt)({
        enabled: true,
        decryptRequest: true
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], EncryptExampleController.prototype, "testRequestBodyEncryption", null);
__decorate([
    (0, common_1.Get)('simple-partial/:id'),
    (0, encrypt_decorator_1.Encrypt)({
        enabled: true,
        encryptFields: ['secretInfo', 'sensitiveData']
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], EncryptExampleController.prototype, "getSimplePartialSecureData", null);
exports.EncryptExampleController = EncryptExampleController = __decorate([
    (0, common_1.Controller)('encrypt-example')
], EncryptExampleController);
//# sourceMappingURL=encrypt-example.controller.js.map