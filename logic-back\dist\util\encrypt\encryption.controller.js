"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EncryptionController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptionController = void 0;
const common_1 = require("@nestjs/common");
const encryption_service_1 = require("./encryption.service");
const redis_session_service_1 = require("./session/redis-session.service");
const uuid_1 = require("uuid");
const swagger_1 = require("@nestjs/swagger");
const key_management_service_1 = require("./key-management/key-management.service");
let EncryptionController = EncryptionController_1 = class EncryptionController {
    encryptionService;
    redisSessionService;
    keyManagementService;
    sessionRenewals = new Map();
    minRenewInterval = 60 * 1000;
    logger = new common_1.Logger(EncryptionController_1.name);
    constructor(encryptionService, redisSessionService, keyManagementService) {
        this.encryptionService = encryptionService;
        this.redisSessionService = redisSessionService;
        this.keyManagementService = keyManagementService;
    }
    async getPublicKey() {
        try {
            const { keyId, publicKey } = this.encryptionService.getPublicKey();
            return {
                success: true,
                keyId,
                publicKey
            };
        }
        catch (error) {
            this.logger.error(`获取公钥失败: ${error.message}`);
            return {
                success: false,
                message: '获取公钥失败，请稍后重试'
            };
        }
    }
    async getKeyStatus() {
        try {
            const activeKey = this.keyManagementService.getActiveKeyInfo();
            const allKeys = this.keyManagementService.getAllKeysInfo();
            const statistics = {
                totalKeys: allKeys.length,
                activeKeys: allKeys.filter(k => k.status === 'active').length,
                deprecatedKeys: allKeys.filter(k => k.status === 'deprecated').length,
                expiredKeys: allKeys.filter(k => k.status === 'expired').length
            };
            return {
                success: true,
                activeKey,
                availableKeys: allKeys,
                statistics
            };
        }
        catch (error) {
            this.logger.error(`获取密钥状态信息失败: ${error.message}`);
            return {
                success: false,
                message: '获取密钥状态信息失败'
            };
        }
    }
    async createSession(body, headers, req) {
        try {
            const sessionId = this.generateSessionId(req);
            this.logger.log(`创建会话密钥，会话ID: ${sessionId}`);
            const clientInfo = {
                ip: req.ip,
                userAgent: headers['user-agent'],
            };
            const result = await this.encryptionService.createSession(sessionId, body.encryptedKey, body.keyId, encryption_service_1.SessionType.STANDARD, clientInfo);
            if (!result.success) {
                return {
                    success: false,
                    needsKeyUpdate: result.needsKeyUpdate,
                    message: result.errorMessage || '创建会话失败'
                };
            }
            return {
                success: true,
                sessionId,
                needsKeyUpdate: result.needsKeyUpdate,
                expiresIn: 30 * 60
            };
        }
        catch (error) {
            this.logger.error(`创建会话失败: ${error.message}`);
            return { success: false, message: '创建会话失败: ' + error.message };
        }
    }
    async createSecureSession(body, headers, req) {
        try {
            const sessionId = this.generateSessionId(req);
            this.logger.log(`创建安全会话密钥，会话ID: ${sessionId}`);
            const clientInfo = {
                ip: req.ip,
                userAgent: headers['user-agent'],
            };
            const result = await this.encryptionService.createSecureSession(sessionId, body.encryptedKey, body.keyId, clientInfo);
            if (!result.success) {
                return {
                    success: false,
                    needsKeyUpdate: result.needsKeyUpdate,
                    message: result.errorMessage || '创建安全会话失败'
                };
            }
            return {
                success: true,
                sessionId,
                needsKeyUpdate: result.needsKeyUpdate,
                expiresIn: 5 * 60
            };
        }
        catch (error) {
            this.logger.error(`创建安全会话失败: ${error.message}`);
            return { success: false, message: '创建安全会话失败: ' + error.message };
        }
    }
    generateSessionId(req) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 15);
        const ip = req.ip?.replace(/[^\w\d]/g, '') || 'unknown';
        return `${timestamp}.${ip}.${random}`;
    }
    async renewSession(sessionId) {
        try {
            const now = Date.now();
            const lastRenewTime = this.sessionRenewals.get(sessionId) || 0;
            const timeSinceLastRenewal = now - lastRenewTime;
            if (lastRenewTime > 0 && timeSinceLastRenewal < this.minRenewInterval) {
                return {
                    success: true,
                    code: common_1.HttpStatus.OK,
                    message: `会话续期过于频繁，请稍后再试 (${Math.round((this.minRenewInterval - timeSinceLastRenewal) / 1000)}秒)`,
                    data: {
                        sessionId,
                        expiresIn: 30 * 60,
                        sessionType: 'standard',
                        throttled: true
                    }
                };
            }
            const sessionType = await this.encryptionService.getSessionType(sessionId);
            if (!sessionType) {
                return {
                    success: false,
                    code: common_1.HttpStatus.NOT_FOUND,
                    message: '会话不存在'
                };
            }
            if (sessionType === encryption_service_1.SessionType.SECURE) {
                return {
                    success: false,
                    code: common_1.HttpStatus.BAD_REQUEST,
                    message: '安全会话不允许续期'
                };
            }
            const sessionData = await this.redisSessionService.getSession(sessionId);
            let originalExpiresAt = 'unknown';
            if (sessionData && sessionData.expiresAt) {
                originalExpiresAt = new Date(sessionData.expiresAt).toISOString();
                this.logger.log(`会话 ${sessionId} 续期前状态: 过期时间 ${originalExpiresAt}, 剩余 ${Math.round((sessionData.expiresAt - Date.now()) / 1000)} 秒`);
            }
            const renewed = await this.redisSessionService.refreshSession(sessionId, encryption_service_1.SessionType.STANDARD);
            if (!renewed) {
                return {
                    success: false,
                    code: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                    message: '会话续期失败'
                };
            }
            const updatedSessionData = await this.redisSessionService.getSession(sessionId);
            let newExpiresAt = 'unknown';
            if (updatedSessionData && updatedSessionData.expiresAt) {
                newExpiresAt = new Date(updatedSessionData.expiresAt).toISOString();
                this.logger.log(`会话 ${sessionId} 续期后状态: 新过期时间 ${newExpiresAt}, 剩余 ${Math.round((updatedSessionData.expiresAt - Date.now()) / 1000)} 秒`);
            }
            this.sessionRenewals.set(sessionId, now);
            return {
                success: true,
                code: common_1.HttpStatus.OK,
                message: '会话续期成功',
                data: {
                    sessionId,
                    expiresIn: 30 * 60,
                    sessionType: 'standard',
                    originalExpiresAt,
                    newExpiresAt
                }
            };
        }
        catch (error) {
            return {
                success: false,
                code: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: `会话续期失败: ${error.message}`
            };
        }
    }
    async deleteSession(sessionId) {
        const deleted = await this.encryptionService.deleteSession(sessionId);
        if (deleted) {
            this.sessionRenewals.delete(sessionId);
        }
        return {
            success: deleted,
            message: deleted ? '会话删除成功' : '会话不存在或删除失败',
        };
    }
    async getSessionStats() {
        await this.encryptionService.logSessionStats();
        return {
            success: true,
            message: '已输出会话统计信息到日志'
        };
    }
    async cleanupSessions() {
        try {
            await this.encryptionService.cleanupExpiredSessions();
            const now = Date.now();
            const expireThreshold = 30 * 60 * 1000;
            Array.from(this.sessionRenewals.entries()).forEach(([sessionId, timestamp]) => {
                if (now - timestamp > expireThreshold) {
                    this.sessionRenewals.delete(sessionId);
                }
            });
            return {
                success: true,
                message: '已触发会话清理'
            };
        }
        catch (error) {
            return {
                success: false,
                message: `会话清理失败: ${error.message}`
            };
        }
    }
    async createSessionDebug(body) {
        try {
            const sessionId = (0, uuid_1.v4)();
            await this.encryptionService.createSessionDebug(sessionId, body.aesKey, body.aesIv);
            return {
                success: true,
                data: {
                    sessionId,
                    expiresIn: 30 * 60,
                }
            };
        }
        catch (error) {
            return {
                success: false,
                message: '创建加密会话失败',
            };
        }
    }
};
exports.EncryptionController = EncryptionController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取RSA公钥' }),
    (0, common_1.Get)('publicKey'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "getPublicKey", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取密钥状态信息' }),
    (0, common_1.Get)('keyStatus'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "getKeyStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建会话密钥' }),
    (0, common_1.Post)('session'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "createSession", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建安全会话密钥(短期有效)' }),
    (0, common_1.Post)('secureSession'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "createSecureSession", null);
__decorate([
    (0, common_1.Post)('renew-session/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "renewSession", null);
__decorate([
    (0, common_1.Delete)('session/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "deleteSession", null);
__decorate([
    (0, common_1.Get)('session-stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "getSessionStats", null);
__decorate([
    (0, common_1.Post)('cleanup-sessions'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "cleanupSessions", null);
__decorate([
    (0, common_1.Post)('create-session-debug'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EncryptionController.prototype, "createSessionDebug", null);
exports.EncryptionController = EncryptionController = EncryptionController_1 = __decorate([
    (0, swagger_1.ApiTags)('加密'),
    (0, common_1.Controller)('api/encryption'),
    __metadata("design:paramtypes", [encryption_service_1.EncryptionService,
        redis_session_service_1.RedisSessionService,
        key_management_service_1.KeyManagementService])
], EncryptionController);
//# sourceMappingURL=encryption.controller.js.map