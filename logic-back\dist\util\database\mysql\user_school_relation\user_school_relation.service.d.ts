import { Repository } from 'typeorm';
import { CreateUserSchoolRelationDto } from './dto/create-user_school_relation.dto';
import { UpdateUserSchoolRelationDto } from './dto/update-user_school_relation.dto';
import { UserSchoolRelation } from './entities/user_school_relation.entity';
import { UserInfo } from '../user_info/entities/user_info.entity';
export declare class UserSchoolRelationService {
    private relationRepository;
    private userInfoRepository;
    constructor(relationRepository: Repository<UserSchoolRelation>, userInfoRepository: Repository<UserInfo>);
    create(createDto: CreateUserSchoolRelationDto): Promise<UserSchoolRelation>;
    findAll(): Promise<UserSchoolRelation[]>;
    findOne(id: number): Promise<UserSchoolRelation>;
    findByUser(userId: number): Promise<UserSchoolRelation[]>;
    findBySchool(schoolId: number): Promise<UserSchoolRelation[]>;
    findByUserAndSchool(userId: number, schoolId: number): Promise<UserSchoolRelation | null>;
    findStudentsOfSchool(schoolId: number): Promise<UserSchoolRelation[]>;
    findTeachersOfSchool(schoolId: number): Promise<UserInfo[]>;
    update(id: number, updateDto: UpdateUserSchoolRelationDto): Promise<UserSchoolRelation>;
    updateRoleType(id: number, roleType: number): Promise<UserSchoolRelation>;
    remove(id: number): Promise<void>;
    removeByUserAndSchool(userId: number, schoolId: number): Promise<void>;
    removeAllByUser(userId: number): Promise<void>;
    removeAllBySchool(schoolId: number): Promise<void>;
}
