"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintGroupController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const voiceprint_group_service_1 = require("./voiceprint_group.service");
const voiceprint_group_dto_1 = require("./dto/voiceprint_group.dto");
let VoiceprintGroupController = class VoiceprintGroupController {
    voiceprintGroupService;
    constructor(voiceprintGroupService) {
        this.voiceprintGroupService = voiceprintGroupService;
    }
    async create(req, createDto) {
        const userId = req.user.userId;
        return this.voiceprintGroupService.create(userId, createDto);
    }
    async findAll(req, queryDto) {
        const userId = req.user.userId;
        return this.voiceprintGroupService.findByUserId(userId, queryDto);
    }
    async findOne(id) {
        return this.voiceprintGroupService.findOne(id);
    }
    async update(id, updateDto) {
        return this.voiceprintGroupService.update(id, updateDto);
    }
    async remove(id) {
        const result = await this.voiceprintGroupService.delete(id);
        return { success: result };
    }
};
exports.VoiceprintGroupController = VoiceprintGroupController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建声纹特征库' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: voiceprint_group_dto_1.VoiceprintGroupDto }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, voiceprint_group_dto_1.CreateVoiceprintGroupDto]),
    __metadata("design:returntype", Promise)
], VoiceprintGroupController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户的声纹特征库列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [voiceprint_group_dto_1.VoiceprintGroupDto] }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, voiceprint_group_dto_1.QueryVoiceprintGroupDto]),
    __metadata("design:returntype", Promise)
], VoiceprintGroupController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取特定声纹特征库详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: voiceprint_group_dto_1.VoiceprintGroupDto }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VoiceprintGroupController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新声纹特征库' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: voiceprint_group_dto_1.VoiceprintGroupDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, voiceprint_group_dto_1.UpdateVoiceprintGroupDto]),
    __metadata("design:returntype", Promise)
], VoiceprintGroupController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除声纹特征库' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VoiceprintGroupController.prototype, "remove", null);
exports.VoiceprintGroupController = VoiceprintGroupController = __decorate([
    (0, swagger_1.ApiTags)('声纹特征库'),
    (0, common_1.Controller)('voiceprint-group'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [voiceprint_group_service_1.VoiceprintGroupService])
], VoiceprintGroupController);
//# sourceMappingURL=voiceprint_group.controller.js.map