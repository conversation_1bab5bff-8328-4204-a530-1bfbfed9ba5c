"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchUpdateStatusDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class BatchUpdateStatusDto {
    taskIds;
    status;
}
exports.BatchUpdateStatusDto = BatchUpdateStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务ID列表', example: [1, 2, 3] }),
    (0, class_validator_1.IsNotEmpty)({ message: '任务ID列表不能为空' }),
    (0, class_validator_1.IsArray)({ message: '任务ID列表必须是数组' }),
    (0, class_validator_1.ArrayMinSize)(1, { message: '至少需要选择一个任务' }),
    (0, class_validator_1.IsNumber)({}, { each: true, message: '任务ID必须是数字' }),
    __metadata("design:type", Array)
], BatchUpdateStatusDto.prototype, "taskIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '新状态：0-待开始 1-进行中 2-已完成 3-已取消', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '状态不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    __metadata("design:type", Number)
], BatchUpdateStatusDto.prototype, "status", void 0);
//# sourceMappingURL=batch-update-status.dto.js.map