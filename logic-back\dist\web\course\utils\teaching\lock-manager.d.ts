export declare class LockManager {
    private readonly logger;
    private readonly locks;
    private readonly cleanupInterval;
    constructor();
    acquireLock(lockKey: string, ttl?: number): Promise<boolean>;
    releaseLock(lockKey: string): Promise<void>;
    withDistributedLock<T>(lockKey: string, operation: () => Promise<T>, ttl?: number, maxRetries?: number, retryDelay?: number): Promise<T>;
    private cleanupExpiredLocks;
    private sleep;
    getLockStatus(): {
        [key: string]: {
            age: number;
            ttl: number;
        };
    };
    onModuleDestroy(): void;
}
