{"version": 3, "file": "teaching.exceptions.js", "sourceRoot": "", "sources": ["../../../../../../src/web/course/domain/exceptions/teaching/teaching.exceptions.ts"], "names": [], "mappings": ";;;AAAA,2CAA2D;AAK3D,MAAa,4BAA6B,SAAQ,sBAAa;IAC7D,YAAY,QAAgB,EAAE,OAAe,EAAE,OAAe;QAC5D,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE;gBACJ,MAAM,EAAE,2BAA2B;gBACnC,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,UAAU,EAAE,EAAE;aACf;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;CACF;AAfD,oEAeC;AAKD,MAAa,2BAA4B,SAAQ,sBAAa;IAC5D,YACE,QAAgB,EAChB,OAAe,EACf,SAAiB,EACjB,gBAAwB,EACxB,iBAAyB;QAEzB,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,mBAAmB;YAC5B,IAAI,EAAE;gBACJ,MAAM,EAAE,cAAc;gBACtB,QAAQ;gBACR,OAAO;gBACP,SAAS;gBACT,gBAAgB;gBAChB,iBAAiB;aAClB;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,kBAAkB,CAAC,CAAC;IACjD,CAAC;CACF;AAtBD,kEAsBC;AAKD,MAAa,qCAAsC,SAAQ,sBAAa;IACtE,YAAY,QAAgB;QAC1B,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,WAAW;YACpB,IAAI,EAAE;gBACJ,QAAQ;gBACR,MAAM,EAAE,kBAAkB;aAC3B;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;IACpD,CAAC;CACF;AAZD,sFAYC;AAKD,MAAa,kCAAmC,SAAQ,sBAAa;IACnE,YACE,SAAiB,EACjB,aAAqB,EACrB,cAAsB,EACtB,WAAoB;QAEpB,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,iBAAiB;YAC1B,IAAI,EAAE;gBACJ,MAAM,EAAE,qBAAqB;gBAC7B,SAAS;gBACT,WAAW,EAAE,WAAW,IAAI,KAAK,SAAS,EAAE;gBAC5C,aAAa;gBACb,cAAc;gBACd,SAAS,EAAE,cAAc,GAAG,aAAa;gBACzC,UAAU,EAAE,uBAAuB;aACpC;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC;CACF;AAtBD,gFAsBC;AAKD,MAAa,sCAAuC,SAAQ,sBAAa;IACvE,YAAY,SAAiB,EAAE,OAAe,EAAE,QAAiB,EAAE,YAAqB,EAAE,SAAkB;QAC1G,IAAI,MAAM,GAAG,YAAY,CAAC;QAG1B,IAAI,QAAQ,IAAI,YAAY,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YACtE,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBAEvB,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,MAAM,GAAG,uBAAuB,CAAC;gBACnC,CAAC;YACH,CAAC;iBAAM,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBAE9B,MAAM,GAAG,eAAe,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,IAAI,EAAE;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,SAAS;aACV;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;CACF;AA/BD,wFA+BC;AAKD,MAAa,mBAAoB,SAAQ,sBAAa;IACpD,YAAY,OAAe,EAAE,eAAuB,CAAC;QACnD,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,QAAQ;YACjB,IAAI,EAAE;gBACJ,MAAM,EAAE,gBAAgB;gBACxB,OAAO;gBACP,YAAY;aACb;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,oBAAoB,CAAC,CAAC;IACnD,CAAC;CACF;AAbD,kDAaC;AAKD,MAAa,2BAA4B,SAAQ,sBAAa;IAC5D,YAAY,QAAgB,EAAE,eAAyB;QACrD,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,QAAQ;YACjB,IAAI,EAAE;gBACJ,MAAM,EAAE,kBAAkB;gBAC1B,QAAQ;gBACR,eAAe;aAChB;SACF,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,oBAAoB,CAAC,CAAC;IACnD,CAAC;CACF;AAbD,kEAaC;AAKD,MAAa,uBAAwB,SAAQ,sBAAa;IACxD,YAAY,IAAS;QACnB,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,gBAAgB;YACzB,IAAI;SACL,CAAC;QACF,KAAK,CAAC,QAAQ,EAAE,mBAAU,CAAC,eAAe,CAAC,CAAC;IAC9C,CAAC;CACF;AATD,0DASC"}