import { WebActivityWorkService } from './web_activity_work.service';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class WebActivityWorkController {
    private readonly webActivityWorkService;
    private readonly httpResponseResultService;
    constructor(webActivityWorkService: WebActivityWorkService, httpResponseResultService: HttpResponseResultService);
    addActivityWorks(data: {
        activityId: number;
        works: {
            workId: number;
            userId?: number;
            isAwarded?: boolean;
            category?: string;
            sort?: number;
            status?: number;
            contentType?: number;
            isImage?: boolean;
        }[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    addImageToActivity(data: {
        activityId: number;
        works: {
            workId: number;
            userId?: number;
            isAwarded?: boolean;
            category?: string;
            sort?: number;
            status?: number;
            contentType?: number;
        }[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    updateActivityWorks(activityId: number, data: {
        works: {
            workId: number;
            userId?: number;
            isAwarded?: boolean;
            category?: string;
            sort?: number;
            status?: number;
            contentType?: number;
        }[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    checkUserSubmitted(activityId: number, userId?: number, req?: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        submitted: boolean;
    }>>;
    getActivityWorks(activityId: number, isAwarded?: boolean, category?: string, userId?: number, status?: number, contentType?: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[]>>;
    deleteActivityWork(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    deleteActivityWorks(data: {
        activityId: number;
        workIds: number[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    setWorkAwarded(id: number, isAwarded: boolean): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    updateWorkCategory(id: number, category: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    updateWorkStatus(id: number, status: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    cancelSubmission(id: number, req: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
}
