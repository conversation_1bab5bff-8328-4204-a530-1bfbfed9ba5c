"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintFeatureModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const voiceprint_feature_service_1 = require("./voiceprint_feature.service");
const voiceprint_feature_controller_1 = require("./voiceprint_feature.controller");
const voiceprint_feature_entity_1 = require("./entities/voiceprint-feature.entity");
let VoiceprintFeatureModule = class VoiceprintFeatureModule {
};
exports.VoiceprintFeatureModule = VoiceprintFeatureModule;
exports.VoiceprintFeatureModule = VoiceprintFeatureModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([voiceprint_feature_entity_1.VoiceprintFeature])],
        controllers: [voiceprint_feature_controller_1.VoiceprintFeatureController],
        providers: [voiceprint_feature_service_1.VoiceprintFeatureService],
        exports: [voiceprint_feature_service_1.VoiceprintFeatureService],
    })
], VoiceprintFeatureModule);
//# sourceMappingURL=voiceprint_feature.module.js.map