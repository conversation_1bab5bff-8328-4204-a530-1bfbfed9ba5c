"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StudentSelfAssessmentSubmissionController = void 0;
const common_1 = require("@nestjs/common");
const student_self_assessment_submission_service_1 = require("./student_self_assessment_submission.service");
const create_student_self_assessment_submission_dto_1 = require("./dto/create-student_self_assessment_submission.dto");
const update_student_self_assessment_submission_dto_1 = require("./dto/update-student_self_assessment_submission.dto");
const swagger_1 = require("@nestjs/swagger");
const create_bulk_student_self_assessment_submission_dto_1 = require("./dto/create-bulk-student_self_assessment_submission.dto");
let StudentSelfAssessmentSubmissionController = class StudentSelfAssessmentSubmissionController {
    studentSelfAssessmentSubmissionService;
    constructor(studentSelfAssessmentSubmissionService) {
        this.studentSelfAssessmentSubmissionService = studentSelfAssessmentSubmissionService;
    }
    createBulk(createBulkDto) {
        return this.studentSelfAssessmentSubmissionService.createBulk(createBulkDto);
    }
    findByAssignmentAndStudent(assignmentId, studentId) {
        return this.studentSelfAssessmentSubmissionService.findByAssignmentAndStudent(+assignmentId, +studentId);
    }
    findSubmissionsByItemId(itemId) {
        return this.studentSelfAssessmentSubmissionService.findByAssessmentItemIdWithStudentInfo(+itemId);
    }
    create(createStudentSelfAssessmentSubmissionDto) {
        return this.studentSelfAssessmentSubmissionService.create(createStudentSelfAssessmentSubmissionDto);
    }
    findAll() {
        return this.studentSelfAssessmentSubmissionService.findAll();
    }
    findOne(id) {
        return this.studentSelfAssessmentSubmissionService.findOne(+id);
    }
    update(id, updateStudentSelfAssessmentSubmissionDto) {
        return this.studentSelfAssessmentSubmissionService.update(+id, updateStudentSelfAssessmentSubmissionDto);
    }
    remove(id) {
        return this.studentSelfAssessmentSubmissionService.remove(+id);
    }
};
exports.StudentSelfAssessmentSubmissionController = StudentSelfAssessmentSubmissionController;
__decorate([
    (0, common_1.Post)('bulk'),
    (0, swagger_1.ApiOperation)({ summary: '批量创建学生自评提交' }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_bulk_student_self_assessment_submission_dto_1.CreateBulkStudentSelfAssessmentSubmissionDto]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "createBulk", null);
__decorate([
    (0, common_1.Get)('assignment/:assignmentId/student/:studentId'),
    (0, swagger_1.ApiOperation)({ summary: '获取学生对某任务的自评' }),
    __param(0, (0, common_1.Param)('assignmentId')),
    __param(1, (0, common_1.Param)('studentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "findByAssignmentAndStudent", null);
__decorate([
    (0, common_1.Get)('item/:itemId/submissions'),
    (0, swagger_1.ApiOperation)({ summary: '获取单个自评项的所有学生评分详情' }),
    __param(0, (0, common_1.Param)('itemId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "findSubmissionsByItemId", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建一个新的学生自评提交' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_student_self_assessment_submission_dto_1.CreateStudentSelfAssessmentSubmissionDto]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有学生自评提交' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取单个学生自评提交' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID更新学生自评提交' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_student_self_assessment_submission_dto_1.UpdateStudentSelfAssessmentSubmissionDto]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID删除学生自评提交' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StudentSelfAssessmentSubmissionController.prototype, "remove", null);
exports.StudentSelfAssessmentSubmissionController = StudentSelfAssessmentSubmissionController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/学生自评提交（student_self_assessment_submission）'),
    (0, common_1.Controller)('student-self-assessment-submission'),
    __metadata("design:paramtypes", [student_self_assessment_submission_service_1.StudentSelfAssessmentSubmissionService])
], StudentSelfAssessmentSubmissionController);
//# sourceMappingURL=student_self_assessment_submission.controller.js.map