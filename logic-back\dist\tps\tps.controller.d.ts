import { TPSService } from './tps.service';
export declare class TPSController {
    private readonly tpsService;
    constructor(tpsService: TPSService);
    createTestStudents(classId: number, count?: number, schoolId?: number): Promise<{
        success: boolean;
        message: string;
        usersCreated: number;
        studentsCreated: number;
        userIds: number[];
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        usersCreated: number;
        studentsCreated: number;
        errors: any[];
        userIds?: undefined;
    }>;
    deleteByPrefix(prefix: string): Promise<{
        success: boolean;
        message: string;
        errors?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        errors?: undefined;
    }>;
    deleteCompleteTestData(timestamp: string): Promise<{
        success: boolean;
        message: string;
        data?: undefined;
        errors?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        data: {
            deletedUsers: number;
            deletedClasses: number;
            totalFound: number;
        };
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
        errors?: undefined;
    }>;
    createCompleteTestData(schoolId: number, teacherCount?: number, studentCount?: number): Promise<{
        success: boolean;
        message: string;
        data: {
            teachersCreated: number;
            classesCreated: number;
            studentsCreated: number;
            userIds: number[];
            details: {
                teachers: any[];
                classes: any[];
                students: any[];
                errors: any[];
            };
        };
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data: {
            teachers: any[];
            classes: any[];
            students: any[];
            errors: any[];
        };
        errors: any[];
    }>;
    createTestTeachers(schoolId: number, count?: number): Promise<{
        success: boolean;
        message: string;
        usersCreated: number;
        userIds: number[];
        errors: any[] | undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        usersCreated: number;
        errors: any[];
        userIds?: undefined;
    }>;
    exportTestDataToCsv(timestamp: string): Promise<{
        success: boolean;
        message: string;
        data?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        data: {
            csvContent: string;
            filename: string;
            count: number;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    assignSpecialPackageToAllStudents(body: {
        packageId: number;
        schoolId: number;
        operatorId: number;
        remark?: string;
        showInMessageCenter?: boolean;
    }): Promise<{
        success: boolean;
        message: string;
        data: {
            packageInfo: {
                id: number;
                name: string;
                points: number;
                validityDays: number;
            };
            assignedCount: number;
            totalStudents: number;
            skippedCount: number;
            errors: any[] | null;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
}
