export declare class OneClickStartDto {
    courseId: number;
    classId: number;
}
export declare class CreatedTaskInfo {
    taskId: number;
    taskName: string;
    startDate: string;
    endDate: string;
    assignedStudents: number;
}
export declare class ExecutionDetailsDto {
    courseName: string;
    seriesName: string;
    className: string;
    studentCount: number;
    pointsPerStudent: number;
    templateName: string;
    createdTasks: CreatedTaskInfo[];
    failedOperations: string[];
    warningMessages: string[];
}
export declare class OneClickStartDataDto {
    success: boolean;
    teachingRecordId: number;
    pointsAllocated: number;
    tasksCreated: number;
    templateApplied: boolean;
    executionTime: string;
    lockAcquireTime: number;
    totalExecutionTime: number;
    details: ExecutionDetailsDto;
}
export declare class OneClickStartResponseDto {
    code: number;
    message: string;
    data: OneClickStartDataDto;
}
