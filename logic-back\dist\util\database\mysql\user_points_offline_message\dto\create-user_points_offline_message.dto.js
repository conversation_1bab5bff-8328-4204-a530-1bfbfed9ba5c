"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserPointsOfflineMessageDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateUserPointsOfflineMessageDto {
    userId;
    pointsChange;
    totalPoints;
    status = 0;
}
exports.CreateUserPointsOfflineMessageDto = CreateUserPointsOfflineMessageDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsNumber)(),
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    __metadata("design:type", Number)
], CreateUserPointsOfflineMessageDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '积分变动值不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '积分变动值必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '积分变动值', example: 100 }),
    __metadata("design:type", Number)
], CreateUserPointsOfflineMessageDto.prototype, "pointsChange", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '总积分不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '总积分必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '总积分', example: 500 }),
    __metadata("design:type", Number)
], CreateUserPointsOfflineMessageDto.prototype, "totalPoints", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, swagger_1.ApiProperty)({ description: '消息状态 0:未发送 1:已发送', default: 0, example: 0 }),
    __metadata("design:type", Number)
], CreateUserPointsOfflineMessageDto.prototype, "status", void 0);
//# sourceMappingURL=create-user_points_offline_message.dto.js.map