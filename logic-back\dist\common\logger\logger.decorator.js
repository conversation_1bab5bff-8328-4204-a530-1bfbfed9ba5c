"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
exports.LogMethod = LogMethod;
const common_1 = require("@nestjs/common");
function LogMethod(context) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = async function (...args) {
            const loggerService = this.loggerService || this.logger;
            const className = target.constructor.name;
            const methodName = propertyName;
            const logContext = context || `${className}.${methodName}`;
            if (loggerService) {
                loggerService.debug(`Method ${methodName} called`, logContext);
                const startTime = Date.now();
                try {
                    const result = await method.apply(this, args);
                    const duration = Date.now() - startTime;
                    loggerService.logPerformance(`${className}.${methodName}`, duration);
                    loggerService.debug(`Method ${methodName} completed successfully`, logContext);
                    return result;
                }
                catch (error) {
                    const duration = Date.now() - startTime;
                    loggerService.error(`Method ${methodName} failed after ${duration}ms: ${error.message}`, error.stack, logContext);
                    throw error;
                }
            }
            else {
                return await method.apply(this, args);
            }
        };
        return descriptor;
    };
}
exports.Logger = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.loggerService;
});
//# sourceMappingURL=logger.decorator.js.map