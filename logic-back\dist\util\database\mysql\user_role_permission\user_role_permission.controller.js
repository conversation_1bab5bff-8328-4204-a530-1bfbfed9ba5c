"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRolePermissionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_role_permission_service_1 = require("./user_role_permission.service");
const create_user_role_permission_dto_1 = require("./dto/create-user_role_permission.dto");
const update_user_role_permission_dto_1 = require("./dto/update-user_role_permission.dto");
const user_role_permission_entity_1 = require("./entities/user_role_permission.entity");
let UserRolePermissionController = class UserRolePermissionController {
    userRolePermissionService;
    constructor(userRolePermissionService) {
        this.userRolePermissionService = userRolePermissionService;
    }
    async create(createUserRolePermissionDto) {
        return await this.userRolePermissionService.create(createUserRolePermissionDto);
    }
    async batchAssignPermissions(roleId, permissionIds) {
        return await this.userRolePermissionService.batchAssignPermissions(roleId, permissionIds);
    }
    async findAll() {
        return await this.userRolePermissionService.findAll();
    }
    async findByRole(roleId) {
        return await this.userRolePermissionService.findByRole(+roleId);
    }
    async findByPermission(permissionId) {
        return await this.userRolePermissionService.findByPermission(+permissionId);
    }
    async findOne(id) {
        return await this.userRolePermissionService.findOne(+id);
    }
    async update(id, updateUserRolePermissionDto) {
        return await this.userRolePermissionService.update(+id, updateUserRolePermissionDto);
    }
    async remove(id) {
        await this.userRolePermissionService.remove(+id);
    }
    async removeByRoleAndPermission(roleId, permissionId) {
        await this.userRolePermissionService.removeByRoleAndPermission(+roleId, +permissionId);
    }
    async removeAllByRole(roleId) {
        await this.userRolePermissionService.removeAllByRole(+roleId);
    }
};
exports.UserRolePermissionController = UserRolePermissionController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建角色权限关联' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: user_role_permission_entity_1.UserRolePermission }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '该角色已拥有此权限' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_role_permission_dto_1.CreateUserRolePermissionDto]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('batch-assign'),
    (0, swagger_1.ApiOperation)({ summary: '批量分配权限给角色' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                roleId: { type: 'number', example: 1 },
                permissionIds: { type: 'array', items: { type: 'number' }, example: [1, 2, 3] }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '分配成功', type: [user_role_permission_entity_1.UserRolePermission] }),
    __param(0, (0, common_1.Body)('roleId')),
    __param(1, (0, common_1.Body)('permissionIds')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Array]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "batchAssignPermissions", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有角色权限关联' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_role_permission_entity_1.UserRolePermission] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('role/:roleId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定角色的所有权限关联' }),
    (0, swagger_1.ApiParam)({ name: 'roleId', description: '角色ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_role_permission_entity_1.UserRolePermission] }),
    __param(0, (0, common_1.Param)('roleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "findByRole", null);
__decorate([
    (0, common_1.Get)('permission/:permissionId'),
    (0, swagger_1.ApiOperation)({ summary: '获取拥有指定权限的所有角色关联' }),
    (0, swagger_1.ApiParam)({ name: 'permissionId', description: '权限ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_role_permission_entity_1.UserRolePermission] }),
    __param(0, (0, common_1.Param)('permissionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "findByPermission", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取角色权限关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_role_permission_entity_1.UserRolePermission }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新角色权限关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_role_permission_entity_1.UserRolePermission }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '该角色已拥有此权限' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_role_permission_dto_1.UpdateUserRolePermissionDto]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除角色权限关联' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关联ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)('role/:roleId/permission/:permissionId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除指定角色和权限的关联' }),
    (0, swagger_1.ApiParam)({ name: 'roleId', description: '角色ID' }),
    (0, swagger_1.ApiParam)({ name: 'permissionId', description: '权限ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关联不存在' }),
    __param(0, (0, common_1.Param)('roleId')),
    __param(1, (0, common_1.Param)('permissionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "removeByRoleAndPermission", null);
__decorate([
    (0, common_1.Delete)('role/:roleId/all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除指定角色的所有权限关联' }),
    (0, swagger_1.ApiParam)({ name: 'roleId', description: '角色ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    __param(0, (0, common_1.Param)('roleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRolePermissionController.prototype, "removeAllByRole", null);
exports.UserRolePermissionController = UserRolePermissionController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/角色权限关联(user_role_permission)'),
    (0, common_1.Controller)('user-role-permission'),
    __metadata("design:paramtypes", [user_role_permission_service_1.UserRolePermissionService])
], UserRolePermissionController);
//# sourceMappingURL=user_role_permission.controller.js.map