"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var KeyManagementService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeyManagementService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const crypto = require("crypto");
const redis_service_1 = require("../../database/redis/redis.service");
const schedule_1 = require("@nestjs/schedule");
let KeyManagementService = KeyManagementService_1 = class KeyManagementService {
    configService;
    redisService;
    logger = new common_1.Logger(KeyManagementService_1.name);
    keys = new Map();
    currentKeyId;
    rsaKeyPairs = new Map();
    activeRsaKeyId;
    rsaKeyPair;
    rotationInterval = 30 * 24 * 60 * 60 * 1000;
    algorithm = 'aes-256-cbc';
    useRedis = true;
    redisKeyPrefix = 'encryption:key:';
    keyRetentionPeriod = 7 * 24 * 60 * 60 * 1000;
    constructor(configService, redisService) {
        this.configService = configService;
        this.redisService = redisService;
    }
    async onModuleInit() {
        this.loadOptions();
        await this.initializeKeys();
        await this.initializeRsaKeyPairs();
        this.logger.log('密钥管理服务初始化完成');
    }
    loadOptions() {
        const config = this.configService.get('keyManagement') || {};
        if (config.rotationInterval) {
            this.rotationInterval = config.rotationInterval;
        }
        if (config.algorithm) {
            this.algorithm = config.algorithm;
        }
        if (config.useRedis !== undefined) {
            this.useRedis = config.useRedis;
        }
        if (config.redisKeyPrefix) {
            this.redisKeyPrefix = config.redisKeyPrefix;
        }
        if (config.keyRetentionPeriod) {
            this.keyRetentionPeriod = config.keyRetentionPeriod;
        }
        this.logger.log(`密钥管理选项已加载: algorithm=${this.algorithm}, useRedis=${this.useRedis}`);
    }
    async initializeRsaKeyPairs() {
        try {
            let loadedFromSource = false;
            if (this.useRedis) {
                this.logger.log('尝试从Redis加载RSA密钥对...');
                try {
                    await this.loadRsaKeyPairsFromRedis();
                    if (this.rsaKeyPairs.size > 0) {
                        loadedFromSource = true;
                        this.logger.log(`已从Redis加载${this.rsaKeyPairs.size}个RSA密钥对`);
                        if (!this.hasActiveRsaKey()) {
                            this.setNewestKeyAsActive();
                        }
                    }
                    else {
                        this.logger.warn('Redis中未找到有效的RSA密钥对');
                    }
                }
                catch (redisErr) {
                    this.logger.error(`从Redis加载RSA密钥对失败: ${redisErr.message}`);
                }
            }
            if (!loadedFromSource) {
                this.logger.log('尝试从配置加载RSA密钥对...');
                try {
                    await this.loadRsaKeyPairsFromConfig();
                    if (this.rsaKeyPairs.size > 0) {
                        loadedFromSource = true;
                        this.logger.log('已从配置加载RSA密钥对');
                    }
                    else {
                        this.logger.warn('配置中未找到有效的RSA密钥对');
                    }
                }
                catch (configErr) {
                    this.logger.error(`从配置加载RSA密钥对失败: ${configErr.message}`);
                }
            }
            if (!this.hasActiveRsaKey()) {
                this.logger.log('未找到有效的活跃RSA密钥对，生成新密钥对...');
                await this.generateNewRsaKeyPair();
            }
            this.updateLegacyRsaKeyPair();
            await this.validateActiveRsaKeyPairWithoutRegeneration();
            this.logger.log(`RSA密钥对已初始化，共${this.rsaKeyPairs.size}个版本，活跃版本: ${this.activeRsaKeyId}`);
        }
        catch (error) {
            this.logger.error(`初始化RSA密钥对失败: ${error.message}`, error.stack);
            await this.generateNewRsaKeyPair();
            this.logger.log('已生成紧急备用RSA密钥对');
        }
    }
    setNewestKeyAsActive() {
        if (this.rsaKeyPairs.size === 0) {
            return false;
        }
        let newestKey = null;
        let newestKeyId = '';
        for (const [id, keyPair] of this.rsaKeyPairs.entries()) {
            if (!newestKey || keyPair.createdAt > newestKey.createdAt) {
                newestKey = keyPair;
                newestKeyId = id;
            }
        }
        if (newestKey) {
            newestKey.status = 'active';
            this.activeRsaKeyId = newestKeyId;
            if (this.useRedis) {
                this.saveRsaKeyPairToRedis(newestKeyId, newestKey).catch(err => {
                    this.logger.error(`保存活跃密钥状态到Redis失败: ${err.message}`);
                });
            }
            this.logger.log(`已将最新的密钥 ${newestKeyId} 设置为活跃密钥`);
            return true;
        }
        return false;
    }
    async validateActiveRsaKeyPairWithoutRegeneration() {
        try {
            if (!this.activeRsaKeyId || !this.rsaKeyPairs.has(this.activeRsaKeyId)) {
                throw new Error('没有活跃的RSA密钥对');
            }
            const activeKey = this.rsaKeyPairs.get(this.activeRsaKeyId);
            if (!activeKey) {
                throw new Error('活跃RSA密钥对无效');
            }
            const testStr = 'test-rsa-key-pair-validation';
            const encrypted = crypto.publicEncrypt({
                key: activeKey.publicKey,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            }, Buffer.from(testStr));
            const decrypted = crypto.privateDecrypt({
                key: activeKey.privateKey,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            }, encrypted);
            if (decrypted.toString() !== testStr) {
                throw new Error('RSA密钥对验证失败：解密结果与原始数据不匹配');
            }
            this.logger.log('活跃RSA密钥对验证通过');
            return true;
        }
        catch (error) {
            this.logger.error(`RSA密钥对验证失败: ${error.message}，但不会自动重新生成密钥`);
            return false;
        }
    }
    hasActiveRsaKey() {
        for (const [id, keyPair] of this.rsaKeyPairs.entries()) {
            if (keyPair.status === 'active') {
                this.activeRsaKeyId = id;
                return true;
            }
        }
        return false;
    }
    updateLegacyRsaKeyPair() {
        if (this.activeRsaKeyId && this.rsaKeyPairs.has(this.activeRsaKeyId)) {
            const activeKey = this.rsaKeyPairs.get(this.activeRsaKeyId);
            if (activeKey) {
                this.rsaKeyPair = {
                    privateKey: activeKey.privateKey,
                    publicKey: activeKey.publicKey,
                    createdAt: activeKey.createdAt
                };
            }
        }
    }
    async loadRsaKeyPairsFromRedis() {
        try {
            let foundActiveKey = false;
            let activeKeyId = null;
            const legacyRedisKey = `${this.redisKeyPrefix}rsa`;
            const legacyKeyData = await this.redisService.get(legacyRedisKey);
            if (legacyKeyData) {
                try {
                    const keyPair = JSON.parse(legacyKeyData);
                    const keyHash = crypto.createHash('sha256')
                        .update(keyPair.publicKey)
                        .digest('hex')
                        .substring(0, 8);
                    const keyId = `key-${keyHash}`;
                    const versionedKeyPair = {
                        id: keyId,
                        privateKey: keyPair.privateKey,
                        publicKey: keyPair.publicKey,
                        createdAt: keyPair.createdAt || Date.now(),
                        expiresAt: (keyPair.createdAt || Date.now()) + this.rotationInterval,
                        status: 'active'
                    };
                    this.rsaKeyPairs.set(keyId, versionedKeyPair);
                    activeKeyId = keyId;
                    foundActiveKey = true;
                    await this.saveRsaKeyPairToRedis(keyId, versionedKeyPair);
                    this.logger.log(`从旧格式加载并转换RSA密钥对，使用固定ID: ${keyId}`);
                }
                catch (parseError) {
                    this.logger.error(`解析旧格式RSA密钥失败: ${parseError.message}`);
                }
            }
            const redisKeys = await this.redisService.getClient().keys(`${this.redisKeyPrefix}rsa:*`);
            for (const redisKey of redisKeys) {
                const keyData = await this.redisService.get(redisKey);
                if (keyData) {
                    try {
                        const keyPair = JSON.parse(keyData);
                        const keyId = redisKey.replace(`${this.redisKeyPrefix}rsa:`, '');
                        if (keyPair && keyPair.publicKey && keyPair.privateKey) {
                            const keyHash = crypto.createHash('sha256')
                                .update(keyPair.publicKey)
                                .digest('hex')
                                .substring(0, 8);
                            const consistentKeyId = `key-${keyHash}`;
                            if (keyId !== consistentKeyId) {
                                this.logger.warn(`密钥ID不一致，Redis中为 ${keyId}，计算出的一致性ID为 ${consistentKeyId}，将使用一致性ID`);
                                keyPair.id = consistentKeyId;
                                this.rsaKeyPairs.set(consistentKeyId, keyPair);
                                if (keyPair.status === 'active') {
                                    if (!foundActiveKey || (keyPair.createdAt > (this.rsaKeyPairs.get(activeKeyId)?.createdAt || 0))) {
                                        activeKeyId = consistentKeyId;
                                        foundActiveKey = true;
                                    }
                                }
                                await this.saveRsaKeyPairToRedis(consistentKeyId, keyPair);
                                if (keyId !== consistentKeyId) {
                                    await this.redisService.del(redisKey);
                                }
                            }
                            else {
                                this.rsaKeyPairs.set(keyId, keyPair);
                                if (keyPair.status === 'active') {
                                    if (!foundActiveKey || (keyPair.createdAt > (this.rsaKeyPairs.get(activeKeyId)?.createdAt || 0))) {
                                        activeKeyId = keyId;
                                        foundActiveKey = true;
                                    }
                                }
                            }
                        }
                        else {
                            this.logger.warn(`Redis中的RSA密钥数据不完整: ${redisKey}`);
                        }
                    }
                    catch (parseError) {
                        this.logger.error(`解析Redis RSA密钥数据失败: ${parseError.message}`);
                    }
                }
            }
            if (foundActiveKey && activeKeyId) {
                for (const [id, keyPair] of this.rsaKeyPairs.entries()) {
                    if (id !== activeKeyId && keyPair.status === 'active') {
                        keyPair.status = 'deprecated';
                        await this.saveRsaKeyPairToRedis(id, keyPair);
                        this.logger.log(`将密钥 ${id} 状态从 active 更改为 deprecated`);
                    }
                }
                this.activeRsaKeyId = activeKeyId;
                this.logger.log(`设置活跃密钥ID: ${activeKeyId}`);
            }
            this.logger.log(`从Redis加载了${this.rsaKeyPairs.size}个RSA密钥对`);
        }
        catch (error) {
            this.logger.error(`从Redis加载RSA密钥对失败: ${error.message}`, error.stack);
        }
    }
    async loadRsaKeyPairsFromConfig() {
        try {
            const configRsaKey = this.configService.get('encryption.rsaKeyPair');
            if (configRsaKey && configRsaKey.privateKey && configRsaKey.publicKey) {
                const keyHash = crypto.createHash('sha256')
                    .update(configRsaKey.publicKey)
                    .digest('hex')
                    .substring(0, 8);
                const keyId = `key-${keyHash}`;
                const versionedKeyPair = {
                    id: keyId,
                    privateKey: configRsaKey.privateKey,
                    publicKey: configRsaKey.publicKey,
                    createdAt: configRsaKey.createdAt || Date.now(),
                    expiresAt: (configRsaKey.createdAt || Date.now()) + this.rotationInterval,
                    status: 'active'
                };
                this.rsaKeyPairs.set(keyId, versionedKeyPair);
                this.activeRsaKeyId = keyId;
                if (this.useRedis) {
                    await this.saveRsaKeyPairToRedis(keyId, versionedKeyPair);
                }
                this.logger.log(`已从配置加载并转换为版本化RSA密钥对，ID: ${keyId}`);
            }
        }
        catch (error) {
            this.logger.error(`从配置加载RSA密钥对失败: ${error.message}`, error.stack);
        }
    }
    async generateNewRsaKeyPair() {
        const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: 2048,
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem',
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem',
            },
        });
        const keyHash = crypto.createHash('sha256')
            .update(publicKey)
            .digest('hex')
            .substring(0, 8);
        const keyId = `key-${keyHash}`;
        const now = Date.now();
        const keyPair = {
            id: keyId,
            privateKey,
            publicKey,
            createdAt: now,
            expiresAt: now + this.rotationInterval,
            status: 'active'
        };
        if (this.activeRsaKeyId) {
            const oldActiveKey = this.rsaKeyPairs.get(this.activeRsaKeyId);
            if (oldActiveKey) {
                oldActiveKey.status = 'deprecated';
                if (this.useRedis) {
                    await this.saveRsaKeyPairToRedis(this.activeRsaKeyId, oldActiveKey);
                }
            }
        }
        this.rsaKeyPairs.set(keyId, keyPair);
        this.activeRsaKeyId = keyId;
        this.updateLegacyRsaKeyPair();
        if (this.useRedis) {
            await this.saveRsaKeyPairToRedis(keyId, keyPair);
        }
        this.logger.log(`生成了新的RSA密钥对，ID: ${keyId}，指纹: ${this.getKeyFingerprint(publicKey)}`);
        return keyId;
    }
    async saveRsaKeyPairToRedis(keyId, keyPair) {
        if (!this.useRedis) {
            return;
        }
        try {
            const redisKey = `${this.redisKeyPrefix}rsa:${keyId}`;
            const ttl = 30 * 24 * 60 * 60;
            await this.redisService.set(redisKey, JSON.stringify(keyPair), ttl);
            this.logger.log(`RSA密钥对已保存到Redis，ID: ${keyId}, 有效期30天，指纹：${this.getKeyFingerprint(keyPair.publicKey)}`);
        }
        catch (error) {
            this.logger.error(`保存RSA密钥对到Redis失败: ${error.message}`, error.stack);
        }
    }
    getKeyFingerprint(key) {
        try {
            const hash = crypto.createHash('sha256').update(key).digest('hex');
            return hash.substring(0, 8);
        }
        catch (error) {
            return 'unknown';
        }
    }
    getPublicKeyWithVersion() {
        if (!this.activeRsaKeyId || !this.rsaKeyPairs.has(this.activeRsaKeyId)) {
            this.logger.warn('没有活跃的RSA密钥对，尝试生成新密钥对');
            throw new Error('RSA密钥未初始化，请稍后重试');
        }
        const keyPair = this.rsaKeyPairs.get(this.activeRsaKeyId);
        if (!keyPair) {
            this.logger.error('活跃密钥ID对应的密钥对不存在');
            throw new Error('RSA密钥状态异常，请稍后重试');
        }
        return {
            keyId: this.activeRsaKeyId,
            publicKey: keyPair.publicKey
        };
    }
    getRsaKeyPair() {
        if (!this.rsaKeyPair) {
            this.generateNewRsaKeyPair().catch(err => {
                this.logger.error(`生成新RSA密钥对失败: ${err.message}`);
            });
            throw new Error('RSA密钥对未初始化，请稍后重试');
        }
        return this.rsaKeyPair;
    }
    getRsaKeyPairById(keyId) {
        let keyPair = this.rsaKeyPairs.get(keyId);
        if (!keyPair) {
            this.logger.warn(`请求的RSA密钥ID ${keyId} 不存在，尝试通过前缀或指纹匹配`);
            const requestedFingerprint = keyId.split('-').pop();
            if (requestedFingerprint && requestedFingerprint.length >= 4) {
                for (const [id, pair] of this.rsaKeyPairs.entries()) {
                    const currentFingerprint = this.getKeyFingerprint(pair.publicKey);
                    if (currentFingerprint.includes(requestedFingerprint) ||
                        requestedFingerprint.includes(currentFingerprint)) {
                        this.logger.log(`找到指纹匹配的密钥: 请求的指纹 ${requestedFingerprint}, 匹配到密钥ID ${id}, 指纹 ${currentFingerprint}`);
                        keyPair = pair;
                        break;
                    }
                }
            }
        }
        if (!keyPair) {
            this.logger.warn(`请求的RSA密钥ID ${keyId} 不存在，可能已过期`);
            if (this.activeRsaKeyId && this.rsaKeyPairs.has(this.activeRsaKeyId)) {
                const activeKeyPair = this.rsaKeyPairs.get(this.activeRsaKeyId);
                if (!activeKeyPair) {
                    throw new Error('活跃密钥数据异常');
                }
                return {
                    keyPair: activeKeyPair,
                    needsUpdate: true
                };
            }
            throw new Error('请求的RSA密钥不存在，且没有可用的活跃密钥');
        }
        const needsUpdate = keyPair.status !== 'active';
        return { keyPair, needsUpdate };
    }
    decryptWithRsaPrivateKeyById(encryptedData, keyId) {
        try {
            const { keyPair, needsUpdate } = this.getRsaKeyPairById(keyId);
            try {
                const decrypted = crypto.privateDecrypt({
                    key: keyPair.privateKey,
                    padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                }, Buffer.from(encryptedData, 'base64'));
                return { data: decrypted, needsUpdate };
            }
            catch (decryptError) {
                this.logger.error(`RSA私钥(ID:${keyId})解密操作失败: ${decryptError.message}`, decryptError.stack);
                if (keyId !== this.activeRsaKeyId && this.activeRsaKeyId) {
                    this.logger.warn(`使用指定密钥ID ${keyId} 解密失败，尝试使用当前活跃密钥 ${this.activeRsaKeyId} 重试...`);
                    try {
                        const activeKeyPair = this.rsaKeyPairs.get(this.activeRsaKeyId);
                        if (activeKeyPair) {
                            const decrypted = crypto.privateDecrypt({
                                key: activeKeyPair.privateKey,
                                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                            }, Buffer.from(encryptedData, 'base64'));
                            this.logger.log(`使用活跃密钥 ${this.activeRsaKeyId} 解密成功，客户端需要更新密钥`);
                            return { data: decrypted, needsUpdate: true };
                        }
                    }
                    catch (retryError) {
                        this.logger.error(`使用活跃密钥重试解密仍然失败: ${retryError.message}`);
                    }
                }
                if (decryptError.message.includes('routines::oaep decoding error')) {
                    throw new Error(`RSA解密失败，可能是密钥不匹配。请求密钥ID: ${keyId}, 活跃密钥ID: ${this.activeRsaKeyId}`);
                }
                throw decryptError;
            }
        }
        catch (error) {
            this.logger.error(`使用RSA私钥解密失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    decryptWithRsaPrivateKey(encryptedData) {
        try {
            if (!this.activeRsaKeyId || !this.rsaKeyPairs.has(this.activeRsaKeyId)) {
                this.logger.warn('RSA密钥对未初始化，尝试重新生成密钥对');
                this.generateNewRsaKeyPair().catch(err => {
                    this.logger.error(`生成新的RSA密钥对失败: ${err.message}`);
                });
                throw new Error('RSA密钥对已重新生成，需要客户端重新获取公钥');
            }
            const keyPair = this.rsaKeyPairs.get(this.activeRsaKeyId);
            if (!keyPair) {
                throw new Error('活跃密钥数据无效');
            }
            const createdTime = new Date(keyPair.createdAt);
            this.logger.log(`使用创建于 ${createdTime.toISOString()} 的RSA私钥进行解密`);
            try {
                return crypto.privateDecrypt({
                    key: keyPair.privateKey,
                    padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                }, Buffer.from(encryptedData, 'base64'));
            }
            catch (decryptError) {
                this.logger.error(`RSA私钥解密操作失败: ${decryptError.message}`, decryptError.stack);
                if (decryptError.message.includes('routines::oaep decoding error')) {
                    this.logger.warn('检测到可能的密钥不匹配，尝试重新生成并储存RSA密钥对');
                    this.generateNewRsaKeyPair().catch(err => {
                        this.logger.error(`生成新的RSA密钥对失败: ${err.message}`);
                    });
                    throw new Error('RSA密钥已更新，请客户端重新获取公钥后再试');
                }
                throw decryptError;
            }
        }
        catch (error) {
            this.logger.error(`RSA私钥解密失败: ${error.message}`, error.stack);
            throw new Error('RSA解密失败，请重新获取公钥');
        }
    }
    encryptWithRsaPublicKey(data) {
        try {
            if (!this.rsaKeyPair) {
                throw new Error('RSA密钥对未初始化');
            }
            const buffer = typeof data === 'string' ? Buffer.from(data) : data;
            const encrypted = crypto.publicEncrypt({
                key: this.rsaKeyPair.publicKey,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            }, buffer);
            return encrypted.toString('base64');
        }
        catch (error) {
            this.logger.error(`RSA公钥加密失败: ${error.message}`, error.stack);
            throw new Error('RSA加密失败');
        }
    }
    protectSessionKey(sessionKey) {
        try {
            const serializedKey = {
                key: sessionKey.aesKey.toString('base64'),
                iv: sessionKey.iv.toString('base64'),
                expiresAt: sessionKey.expiresAt,
                sessionType: sessionKey.sessionType,
                clientInfo: sessionKey.clientInfo
            };
            const encryptedKey = this.encryptWithSystemKey(JSON.stringify(serializedKey));
            return {
                protected: true,
                data: encryptedKey,
                expiresAt: sessionKey.expiresAt,
                sessionType: sessionKey.sessionType
            };
        }
        catch (error) {
            this.logger.error(`保护会话密钥失败: ${error.message}`, error.stack);
            return sessionKey;
        }
    }
    restoreSessionKey(protectedKey) {
        if (!protectedKey.protected) {
            return protectedKey;
        }
        try {
            const decryptedData = this.decryptWithSystemKey(protectedKey.data);
            const sessionKey = JSON.parse(decryptedData);
            return {
                aesKey: Buffer.from(sessionKey.key, 'base64'),
                iv: Buffer.from(sessionKey.iv, 'base64'),
                expiresAt: sessionKey.expiresAt,
                sessionType: sessionKey.sessionType || protectedKey.sessionType,
                clientInfo: sessionKey.clientInfo
            };
        }
        catch (error) {
            this.logger.error(`恢复会话密钥失败: ${error.message}`, error.stack);
            throw new Error('会话密钥恢复失败');
        }
    }
    async initializeKeys() {
        if (this.useRedis) {
            await this.loadKeysFromRedis();
        }
        else {
            await this.loadKeysFromConfig();
        }
        if (this.keys.size === 0 || this.shouldRotateKey()) {
            await this.rotateKey();
        }
        else {
            this.updateCurrentKeyId();
        }
    }
    async loadKeysFromRedis() {
        try {
            const redisKeys = await this.redisService.getClient().keys(`${this.redisKeyPrefix}*`);
            for (const redisKey of redisKeys) {
                if (redisKey === `${this.redisKeyPrefix}rsa` || redisKey.startsWith(`${this.redisKeyPrefix}rsa:`)) {
                    continue;
                }
                const keyData = await this.redisService.get(redisKey);
                if (keyData) {
                    try {
                        const keyInfo = JSON.parse(keyData);
                        if (keyInfo && keyInfo.key) {
                            keyInfo.key = Buffer.from(keyInfo.key, 'base64');
                            if (keyInfo.iv) {
                                keyInfo.iv = Buffer.from(keyInfo.iv, 'base64');
                            }
                            this.keys.set(keyInfo.id, keyInfo);
                            this.logger.log(`从Redis加载了密钥: ${keyInfo.id}`);
                        }
                        else {
                            this.logger.warn(`Redis中的密钥数据格式不正确: ${redisKey}`);
                        }
                    }
                    catch (parseError) {
                        this.logger.error(`解析Redis密钥数据失败: ${parseError.message}`, parseError.stack);
                    }
                }
            }
            this.logger.log(`从Redis加载了${this.keys.size}个密钥`);
        }
        catch (error) {
            this.logger.error(`从Redis加载密钥失败: ${error.message}`, error.stack);
            await this.loadKeysFromConfig();
        }
    }
    async loadKeysFromConfig() {
        try {
            const configKeys = this.configService.get('encryption.keys') || [];
            for (const configKey of configKeys) {
                const keyInfo = {
                    id: configKey.id,
                    key: Buffer.from(configKey.key, 'base64'),
                    iv: configKey.iv ? Buffer.from(configKey.iv, 'base64') : undefined,
                    algorithm: configKey.algorithm || this.algorithm,
                    createdAt: configKey.createdAt || Date.now(),
                    expiresAt: configKey.expiresAt,
                    isActive: configKey.isActive !== undefined ? configKey.isActive : true,
                };
                this.keys.set(keyInfo.id, keyInfo);
            }
            this.logger.log(`从配置加载了${this.keys.size}个密钥`);
        }
        catch (error) {
            this.logger.error(`从配置加载密钥失败: ${error.message}`, error.stack);
        }
    }
    async saveKeyToRedis(keyInfo) {
        if (!this.useRedis) {
            return;
        }
        try {
            const keyData = {
                ...keyInfo,
                key: keyInfo.key.toString('base64'),
                iv: keyInfo.iv ? keyInfo.iv.toString('base64') : undefined,
            };
            const redisKey = `${this.redisKeyPrefix}${keyInfo.id}`;
            await this.redisService.set(redisKey, JSON.stringify(keyData));
            this.logger.log(`成功保存密钥到Redis: ${redisKey}`);
        }
        catch (error) {
            this.logger.error(`保存密钥到Redis失败: ${error.message}`, error.stack);
        }
    }
    async rotateKey() {
        const newKeyId = crypto.randomUUID();
        const keyLength = this.getKeyLength();
        const key = crypto.randomBytes(keyLength);
        const iv = crypto.randomBytes(16);
        const keyInfo = {
            id: newKeyId,
            key,
            iv,
            algorithm: this.algorithm,
            createdAt: Date.now(),
            expiresAt: Date.now() + this.rotationInterval,
            isActive: true,
        };
        for (const [keyId, info] of this.keys.entries()) {
            info.isActive = false;
            if (this.useRedis) {
                await this.saveKeyToRedis(info);
            }
        }
        this.keys.set(newKeyId, keyInfo);
        this.currentKeyId = newKeyId;
        if (this.useRedis) {
            await this.saveKeyToRedis(keyInfo);
        }
        this.logger.log(`密钥已自动轮换，新密钥ID: ${newKeyId}`);
    }
    getKeyLength() {
        if (this.algorithm.startsWith('aes-128')) {
            return 16;
        }
        else if (this.algorithm.startsWith('aes-192')) {
            return 24;
        }
        else if (this.algorithm.startsWith('aes-256')) {
            return 32;
        }
        else {
            return 32;
        }
    }
    updateCurrentKeyId() {
        for (const [keyId, keyInfo] of this.keys.entries()) {
            if (keyInfo.isActive) {
                this.currentKeyId = keyId;
                return;
            }
        }
        if (this.keys.size > 0) {
            let newestKey = null;
            for (const keyInfo of this.keys.values()) {
                if (!newestKey || keyInfo.createdAt > newestKey.createdAt) {
                    newestKey = keyInfo;
                }
            }
            if (newestKey) {
                this.currentKeyId = newestKey.id;
                newestKey.isActive = true;
            }
        }
    }
    shouldRotateKey() {
        for (const [keyId, keyInfo] of this.keys.entries()) {
            if (keyInfo.isActive && keyInfo.expiresAt && keyInfo.expiresAt < Date.now()) {
                this.logger.log(`密钥${keyId}已过期，需要轮换`);
                return true;
            }
        }
        return false;
    }
    getCurrentKey() {
        if (!this.currentKeyId || !this.keys.has(this.currentKeyId)) {
            this.rotateKey().catch(err => {
                this.logger.error(`创建新密钥失败: ${err.message}`);
            });
            throw new Error('当前没有活跃的密钥，正在创建新密钥');
        }
        const key = this.keys.get(this.currentKeyId);
        if (!key) {
            throw new Error('当前密钥无效');
        }
        return key;
    }
    getKeyById(keyId) {
        const key = this.keys.get(keyId);
        if (!key) {
            throw new Error(`密钥${keyId}不存在`);
        }
        return key;
    }
    async handleKeyRotation() {
        this.logger.log('开始定期密钥检查...');
        try {
            await this.cleanupExpiredKeys();
            const keys = Array.from(this.keys.values());
            const activeKeys = keys.filter(key => key.isActive);
            if (activeKeys.length === 0) {
                this.logger.warn('没有活跃的密钥，执行密钥轮换');
                await this.rotateKey();
            }
            else {
                const activeKey = activeKeys[0];
                if (activeKey.expiresAt && activeKey.expiresAt < Date.now() + 7 * 24 * 60 * 60 * 1000) {
                    this.logger.log('检测到密钥即将过期，执行密钥轮换');
                    await this.rotateKey();
                }
                else {
                    this.logger.log('当前密钥状态正常，无需轮换');
                }
            }
        }
        catch (error) {
            this.logger.error(`密钥轮换任务失败: ${error.message}`, error.stack);
        }
    }
    async cleanupExpiredKeys() {
        const now = Date.now();
        const expiredKeyIds = [];
        for (const [keyId, keyInfo] of this.keys.entries()) {
            if (keyInfo.expiresAt && keyInfo.expiresAt < now && !keyInfo.isActive) {
                expiredKeyIds.push(keyId);
            }
        }
        for (const keyId of expiredKeyIds) {
            this.keys.delete(keyId);
            if (this.useRedis) {
                const redisKey = `${this.redisKeyPrefix}${keyId}`;
                await this.redisService.del(redisKey);
            }
        }
        if (expiredKeyIds.length > 0) {
            this.logger.log(`已删除${expiredKeyIds.length}个过期密钥`);
        }
    }
    encryptWithSystemKey(data) {
        try {
            const keyInfo = this.getCurrentKey();
            const iv = keyInfo.iv || Buffer.from('0000000000000000');
            const cipher = crypto.createCipheriv(keyInfo.algorithm, keyInfo.key, iv);
            let encrypted = cipher.update(data, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            return `${keyInfo.id}.${encrypted}`;
        }
        catch (error) {
            this.logger.error(`使用系统密钥加密数据失败: ${error.message}`, error.stack);
            throw new Error('系统加密失败');
        }
    }
    decryptWithSystemKey(encryptedData) {
        try {
            const [keyId, data] = encryptedData.split('.');
            if (!keyId || !data) {
                throw new Error('加密数据格式无效');
            }
            const keyInfo = this.getKeyById(keyId);
            const iv = keyInfo.iv || Buffer.from('0000000000000000');
            const decipher = crypto.createDecipheriv(keyInfo.algorithm, keyInfo.key, iv);
            let decrypted = decipher.update(data, 'base64', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        }
        catch (error) {
            this.logger.error(`使用系统密钥解密数据失败: ${error.message}`, error.stack);
            throw new Error('系统解密失败');
        }
    }
    getActiveKeyInfo() {
        if (!this.activeRsaKeyId || !this.rsaKeyPairs.has(this.activeRsaKeyId)) {
            return null;
        }
        const activeKey = this.rsaKeyPairs.get(this.activeRsaKeyId);
        if (!activeKey) {
            return null;
        }
        const now = Date.now();
        const remainingMs = activeKey.expiresAt - now;
        const remainingDays = Math.floor(remainingMs / (24 * 60 * 60 * 1000));
        return {
            keyId: this.activeRsaKeyId,
            createdAt: new Date(activeKey.createdAt),
            expiresAt: new Date(activeKey.expiresAt),
            fingerprint: this.getKeyFingerprint(activeKey.publicKey),
            algorithm: 'RSA-2048',
            remainingDays: remainingDays
        };
    }
    getAllKeysInfo() {
        const result = [];
        for (const [keyId, keyPair] of this.rsaKeyPairs.entries()) {
            result.push({
                keyId: keyId,
                status: keyPair.status,
                createdAt: new Date(keyPair.createdAt),
                expiresAt: new Date(keyPair.expiresAt),
                fingerprint: this.getKeyFingerprint(keyPair.publicKey),
                isActive: keyId === this.activeRsaKeyId
            });
        }
        return result.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
};
exports.KeyManagementService = KeyManagementService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KeyManagementService.prototype, "handleKeyRotation", null);
exports.KeyManagementService = KeyManagementService = KeyManagementService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        redis_service_1.RedisService])
], KeyManagementService);
//# sourceMappingURL=key-management.service.js.map