"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintFeatureController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const voiceprint_feature_service_1 = require("./voiceprint_feature.service");
const create_voiceprint_feature_dto_1 = require("./dto/create-voiceprint-feature.dto");
const update_voiceprint_feature_dto_1 = require("./dto/update-voiceprint-feature.dto");
let VoiceprintFeatureController = class VoiceprintFeatureController {
    voiceprintFeatureService;
    constructor(voiceprintFeatureService) {
        this.voiceprintFeatureService = voiceprintFeatureService;
    }
    create(createVoiceprintFeatureDto) {
        return this.voiceprintFeatureService.create(createVoiceprintFeatureDto);
    }
    findAll() {
        return this.voiceprintFeatureService.findAll();
    }
    findByUserId(userId) {
        return this.voiceprintFeatureService.findByUserId(userId);
    }
    findByGroupId(groupId) {
        return this.voiceprintFeatureService.findByGroupId(groupId);
    }
    findOne(id) {
        return this.voiceprintFeatureService.findOne(+id);
    }
    update(id, updateVoiceprintFeatureDto) {
        return this.voiceprintFeatureService.update(+id, updateVoiceprintFeatureDto);
    }
    remove(id) {
        return this.voiceprintFeatureService.remove(+id);
    }
};
exports.VoiceprintFeatureController = VoiceprintFeatureController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建声纹特征' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '成功创建声纹特征' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_voiceprint_feature_dto_1.CreateVoiceprintFeatureDto]),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有声纹特征' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取声纹特征' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('group/:groupId'),
    (0, swagger_1.ApiOperation)({ summary: '根据特征库ID获取声纹特征' }),
    __param(0, (0, common_1.Param)('groupId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "findByGroupId", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取声纹特征' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新声纹特征' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_voiceprint_feature_dto_1.UpdateVoiceprintFeatureDto]),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除声纹特征' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], VoiceprintFeatureController.prototype, "remove", null);
exports.VoiceprintFeatureController = VoiceprintFeatureController = __decorate([
    (0, swagger_1.ApiTags)('声纹特征'),
    (0, common_1.Controller)('voiceprint-feature'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [voiceprint_feature_service_1.VoiceprintFeatureService])
], VoiceprintFeatureController);
//# sourceMappingURL=voiceprint_feature.controller.js.map