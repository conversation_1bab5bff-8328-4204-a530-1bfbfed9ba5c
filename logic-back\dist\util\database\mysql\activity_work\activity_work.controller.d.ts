import { ActivityWorkService } from './activity_work.service';
import { CreateActivityWorkDto } from './dto/create-activity_work.dto';
import { UpdateActivityWorkDto } from './dto/update-activity_work.dto';
import { ActivityWork } from './entities/activity_work.entity';
export declare class ActivityWorkController {
    private readonly activityWorkService;
    constructor(activityWorkService: ActivityWorkService);
    create(createActivityWorkDto: CreateActivityWorkDto): Promise<ActivityWork>;
    findAll(): Promise<ActivityWork[]>;
    findByActivityId(activityId: string): Promise<ActivityWork[]>;
    findByWorkId(workId: string): Promise<ActivityWork[]>;
    findByUserId(userId: string): Promise<ActivityWork[]>;
    findByIsSelected(isSelected: string): Promise<ActivityWork[]>;
    findWinners(): Promise<ActivityWork[]>;
    updateIsSelected(id: string, isSelected: string): Promise<ActivityWork>;
    updateIsWinner(id: string, isWinner: string): Promise<ActivityWork>;
    removeByActivityId(activityId: string): Promise<void>;
    findOne(id: string): Promise<ActivityWork>;
    update(id: string, updateActivityWorkDto: UpdateActivityWorkDto): Promise<ActivityWork>;
    remove(id: string): Promise<void>;
    hardRemove(id: string): Promise<void>;
}
