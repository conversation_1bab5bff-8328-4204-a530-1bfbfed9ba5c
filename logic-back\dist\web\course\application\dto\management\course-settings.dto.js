"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseSettingsResponseDto = exports.CourseSettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CourseSettingsDto {
    templateId;
    requiredPoints;
    autoCreateTasks;
}
exports.CourseSettingsDto = CourseSettingsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '模板ID',
        example: 1,
        nullable: true
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Object)
], CourseSettingsDto.prototype, "templateId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '所需积分',
        example: 100,
        default: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CourseSettingsDto.prototype, "requiredPoints", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '是否自动创建任务：0=否，1=是',
        example: 1,
        default: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CourseSettingsDto.prototype, "autoCreateTasks", void 0);
class CourseSettingsResponseDto {
    id;
    courseId;
    templateId;
    requiredPoints;
    autoCreateTasks;
    createdAt;
    updatedAt;
}
exports.CourseSettingsResponseDto = CourseSettingsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '设置ID',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseSettingsResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '对应课程ID',
        example: 25
    }),
    __metadata("design:type", Number)
], CourseSettingsResponseDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '积木模板ID',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Object)
], CourseSettingsResponseDto.prototype, "templateId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所需积分',
        example: 100
    }),
    __metadata("design:type", Number)
], CourseSettingsResponseDto.prototype, "requiredPoints", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否自动创建任务：0=否，1=是',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseSettingsResponseDto.prototype, "autoCreateTasks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建时间',
        example: '2023-07-15T10:30:00Z'
    }),
    __metadata("design:type", Date)
], CourseSettingsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '更新时间',
        example: '2023-07-15T10:30:00Z'
    }),
    __metadata("design:type", Date)
], CourseSettingsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=course-settings.dto.js.map