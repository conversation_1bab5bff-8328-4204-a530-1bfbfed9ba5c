"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityAuditModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const activity_audit_service_1 = require("./activity_audit.service");
const activity_audit_controller_1 = require("./activity_audit.controller");
const activity_audit_entity_1 = require("./entities/activity_audit.entity");
let ActivityAuditModule = class ActivityAuditModule {
};
exports.ActivityAuditModule = ActivityAuditModule;
exports.ActivityAuditModule = ActivityAuditModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([activity_audit_entity_1.ActivityAudit])],
        controllers: [activity_audit_controller_1.ActivityAuditController],
        providers: [activity_audit_service_1.ActivityAuditService],
        exports: [activity_audit_service_1.ActivityAuditService],
    })
], ActivityAuditModule);
//# sourceMappingURL=activity_audit.module.js.map