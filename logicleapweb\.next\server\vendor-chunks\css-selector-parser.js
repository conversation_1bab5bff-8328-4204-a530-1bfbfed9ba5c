"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-selector-parser";
exports.ids = ["vendor-chunks/css-selector-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/ast.js":
/*!**********************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/ast.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ast: () => (/* binding */ ast)\n/* harmony export */ });\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nfunction astMethods(type) {\n    return function (generatorName, checkerName) {\n        var _a;\n        return (_a = {},\n            _a[generatorName] = function (props) { return (__assign({ type: type }, props)); },\n            _a[checkerName] = function (entity) {\n                return typeof entity === 'object' && entity !== null && entity.type === type;\n            },\n            _a);\n    };\n}\n/**\n * AST structure generators and matchers.\n * For instance, `ast.selector({rules: [...]})` creates AstSelector and `ast.isSelector(...)` checks if\n * AstSelector was specified.\n *\n * @example\n *\n * // Represents CSS selector: ns|div#user-34.user.user-active[role=\"button\"]:lang(en)::before > *\n * const selector = ast.selector({\n *     rules: [\n *         ast.rule({\n *             items: [\n *                 ast.tagName({name: 'div', namespace: ast.namespaceName({name: 'ns'})}),\n *                 ast.id({name: 'user-34'}),\n *                 ast.className({name: 'user'}),\n *                 ast.className({name: 'user-active'}),\n *                 ast.attribute({\n *                     name: 'role',\n *                     operator: '=',\n *                     value: ast.string({value: 'button'})\n *                 }),\n *                 ast.pseudoClass({\n *                     name: 'lang',\n *                     argument: ast.string({value: 'en'})\n *                 }),\n *                 ast.pseudoElement({name: 'before'})\n *             ],\n *             nestedRule: ast.rule({combinator: '>', items: [ast.wildcardTag()]})\n *         })\n *     ]\n * });\n * console.log(ast.isSelector(selector)); // prints true\n * console.log(ast.isRule(selector)); // prints false\n */\nvar ast = __assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign({}, astMethods('Selector')('selector', 'isSelector')), astMethods('Rule')('rule', 'isRule')), astMethods('TagName')('tagName', 'isTagName')), astMethods('Id')('id', 'isId')), astMethods('ClassName')('className', 'isClassName')), astMethods('WildcardTag')('wildcardTag', 'isWildcardTag')), astMethods('NamespaceName')('namespaceName', 'isNamespaceName')), astMethods('WildcardNamespace')('wildcardNamespace', 'isWildcardNamespace')), astMethods('NoNamespace')('noNamespace', 'isNoNamespace')), astMethods('Attribute')('attribute', 'isAttribute')), astMethods('PseudoClass')('pseudoClass', 'isPseudoClass')), astMethods('PseudoElement')('pseudoElement', 'isPseudoElement')), astMethods('String')('string', 'isString')), astMethods('Formula')('formula', 'isFormula')), astMethods('FormulaOfSelector')('formulaOfSelector', 'isFormulaOfSelector')), astMethods('Substitution')('substitution', 'isSubstitution'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/ast.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/index.js":
/*!************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ast: () => (/* reexport safe */ _ast_js__WEBPACK_IMPORTED_MODULE_2__.ast),\n/* harmony export */   createParser: () => (/* reexport safe */ _parser_js__WEBPACK_IMPORTED_MODULE_0__.createParser),\n/* harmony export */   render: () => (/* reexport safe */ _render_js__WEBPACK_IMPORTED_MODULE_1__.render)\n/* harmony export */ });\n/* harmony import */ var _parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parser.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/parser.js\");\n/* harmony import */ var _render_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./render.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/render.js\");\n/* harmony import */ var _ast_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ast.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/ast.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L21qcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDTjtBQUNOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL2Nzcy1zZWxlY3Rvci1wYXJzZXIvZGlzdC9tanMvaW5kZXguanM/NTIwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBjcmVhdGVQYXJzZXIgfSBmcm9tICcuL3BhcnNlci5qcyc7XG5leHBvcnQgeyByZW5kZXIgfSBmcm9tICcuL3JlbmRlci5qcyc7XG5leHBvcnQgeyBhc3QgfSBmcm9tICcuL2FzdC5qcyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/indexes.js":
/*!**************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/indexes.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMulticharIndex: () => (/* binding */ createMulticharIndex),\n/* harmony export */   createRegularIndex: () => (/* binding */ createRegularIndex),\n/* harmony export */   emptyMulticharIndex: () => (/* binding */ emptyMulticharIndex),\n/* harmony export */   emptyRegularIndex: () => (/* binding */ emptyRegularIndex)\n/* harmony export */ });\nvar emptyMulticharIndex = {};\nvar emptyRegularIndex = {};\nfunction extendIndex(item, index) {\n    var currentIndex = index;\n    for (var pos = 0; pos < item.length; pos++) {\n        var isLast = pos === item.length - 1;\n        var char = item.charAt(pos);\n        var charIndex = currentIndex[char] || (currentIndex[char] = { chars: {} });\n        if (isLast) {\n            charIndex.self = item;\n        }\n        currentIndex = charIndex.chars;\n    }\n}\nfunction createMulticharIndex(items) {\n    if (items.length === 0) {\n        return emptyMulticharIndex;\n    }\n    var index = {};\n    for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n        var item = items_1[_i];\n        extendIndex(item, index);\n    }\n    return index;\n}\nfunction createRegularIndex(items) {\n    if (items.length === 0) {\n        return emptyRegularIndex;\n    }\n    var result = {};\n    for (var _i = 0, items_2 = items; _i < items_2.length; _i++) {\n        var item = items_2[_i];\n        result[item] = true;\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L21qcy9pbmRleGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTztBQUNBO0FBQ1A7QUFDQTtBQUNBLHNCQUFzQixtQkFBbUI7QUFDekM7QUFDQTtBQUNBLHNFQUFzRSxXQUFXO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MscUJBQXFCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLHFCQUFxQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL2Nzcy1zZWxlY3Rvci1wYXJzZXIvZGlzdC9tanMvaW5kZXhlcy5qcz8wZjQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgZW1wdHlNdWx0aWNoYXJJbmRleCA9IHt9O1xuZXhwb3J0IHZhciBlbXB0eVJlZ3VsYXJJbmRleCA9IHt9O1xuZnVuY3Rpb24gZXh0ZW5kSW5kZXgoaXRlbSwgaW5kZXgpIHtcbiAgICB2YXIgY3VycmVudEluZGV4ID0gaW5kZXg7XG4gICAgZm9yICh2YXIgcG9zID0gMDsgcG9zIDwgaXRlbS5sZW5ndGg7IHBvcysrKSB7XG4gICAgICAgIHZhciBpc0xhc3QgPSBwb3MgPT09IGl0ZW0ubGVuZ3RoIC0gMTtcbiAgICAgICAgdmFyIGNoYXIgPSBpdGVtLmNoYXJBdChwb3MpO1xuICAgICAgICB2YXIgY2hhckluZGV4ID0gY3VycmVudEluZGV4W2NoYXJdIHx8IChjdXJyZW50SW5kZXhbY2hhcl0gPSB7IGNoYXJzOiB7fSB9KTtcbiAgICAgICAgaWYgKGlzTGFzdCkge1xuICAgICAgICAgICAgY2hhckluZGV4LnNlbGYgPSBpdGVtO1xuICAgICAgICB9XG4gICAgICAgIGN1cnJlbnRJbmRleCA9IGNoYXJJbmRleC5jaGFycztcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlTXVsdGljaGFySW5kZXgoaXRlbXMpIHtcbiAgICBpZiAoaXRlbXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiBlbXB0eU11bHRpY2hhckluZGV4O1xuICAgIH1cbiAgICB2YXIgaW5kZXggPSB7fTtcbiAgICBmb3IgKHZhciBfaSA9IDAsIGl0ZW1zXzEgPSBpdGVtczsgX2kgPCBpdGVtc18xLmxlbmd0aDsgX2krKykge1xuICAgICAgICB2YXIgaXRlbSA9IGl0ZW1zXzFbX2ldO1xuICAgICAgICBleHRlbmRJbmRleChpdGVtLCBpbmRleCk7XG4gICAgfVxuICAgIHJldHVybiBpbmRleDtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVSZWd1bGFySW5kZXgoaXRlbXMpIHtcbiAgICBpZiAoaXRlbXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiBlbXB0eVJlZ3VsYXJJbmRleDtcbiAgICB9XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIGZvciAodmFyIF9pID0gMCwgaXRlbXNfMiA9IGl0ZW1zOyBfaSA8IGl0ZW1zXzIubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIHZhciBpdGVtID0gaXRlbXNfMltfaV07XG4gICAgICAgIHJlc3VsdFtpdGVtXSA9IHRydWU7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/indexes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/parser.js":
/*!*************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/parser.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createParser: () => (/* binding */ createParser)\n/* harmony export */ });\n/* harmony import */ var _indexes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./indexes.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/indexes.js\");\n/* harmony import */ var _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pseudo-signatures.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js\");\n/* harmony import */ var _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./syntax-definitions.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js\");\n\n\n\n\nvar errorPrefix = \"css-selector-parser parse error: \";\n/**\n * Creates a parse function to be used later to parse CSS selectors.\n */\nfunction createParser(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.syntax, syntax = _a === void 0 ? 'latest' : _a, substitutes = options.substitutes, _b = options.strict, strict = _b === void 0 ? true : _b;\n    var syntaxDefinition = typeof syntax === 'object' ? syntax : _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.cssSyntaxDefinitions[syntax];\n    if (syntaxDefinition.baseSyntax) {\n        syntaxDefinition = (0,_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.extendSyntaxDefinition)(_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.cssSyntaxDefinitions[syntaxDefinition.baseSyntax], syntaxDefinition);\n    }\n    var _c = syntaxDefinition.tag\n        ? [true, Boolean((0,_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.getXmlOptions)(syntaxDefinition.tag).wildcard)]\n        : [false, false], tagNameEnabled = _c[0], tagNameWildcardEnabled = _c[1];\n    var idEnabled = Boolean(syntaxDefinition.ids);\n    var classNamesEnabled = Boolean(syntaxDefinition.classNames);\n    var namespaceEnabled = Boolean(syntaxDefinition.namespace);\n    var namespaceWildcardEnabled = syntaxDefinition.namespace &&\n        (syntaxDefinition.namespace === true || syntaxDefinition.namespace.wildcard === true);\n    if (namespaceEnabled && !tagNameEnabled) {\n        throw new Error(\"\".concat(errorPrefix, \"Namespaces cannot be enabled while tags are disabled.\"));\n    }\n    var substitutesEnabled = Boolean(substitutes);\n    var combinatorsIndex = syntaxDefinition.combinators\n        ? (0,_indexes_js__WEBPACK_IMPORTED_MODULE_0__.createMulticharIndex)(syntaxDefinition.combinators)\n        : _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyMulticharIndex;\n    var _d = syntaxDefinition.attributes\n        ? [\n            true,\n            syntaxDefinition.attributes.operators\n                ? (0,_indexes_js__WEBPACK_IMPORTED_MODULE_0__.createMulticharIndex)(syntaxDefinition.attributes.operators)\n                : _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyMulticharIndex,\n            syntaxDefinition.attributes.caseSensitivityModifiers\n                ? (0,_indexes_js__WEBPACK_IMPORTED_MODULE_0__.createRegularIndex)(syntaxDefinition.attributes.caseSensitivityModifiers)\n                : _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyRegularIndex,\n            syntaxDefinition.attributes.unknownCaseSensitivityModifiers === 'accept'\n        ]\n        : [false, _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyMulticharIndex, _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyRegularIndex, false], attributesEnabled = _d[0], attributesOperatorsIndex = _d[1], attributesCaseSensitivityModifiers = _d[2], attributesAcceptUnknownCaseSensitivityModifiers = _d[3];\n    var attributesCaseSensitivityModifiersEnabled = attributesAcceptUnknownCaseSensitivityModifiers || Object.keys(attributesCaseSensitivityModifiers).length > 0;\n    var _e = syntaxDefinition.pseudoClasses\n        ? [\n            true,\n            syntaxDefinition.pseudoClasses.definitions\n                ? (0,_pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.calculatePseudoSignatures)(syntaxDefinition.pseudoClasses.definitions)\n                : _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures,\n            syntaxDefinition.pseudoClasses.unknown === 'accept'\n        ]\n        : [false, _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures, false], pseudoClassesEnabled = _e[0], pseudoClassesDefinitions = _e[1], pseudoClassesAcceptUnknown = _e[2];\n    var _f = syntaxDefinition.pseudoElements\n        ? [\n            true,\n            syntaxDefinition.pseudoElements.notation === 'singleColon' ||\n                syntaxDefinition.pseudoElements.notation === 'both',\n            !syntaxDefinition.pseudoElements.notation ||\n                syntaxDefinition.pseudoElements.notation === 'doubleColon' ||\n                syntaxDefinition.pseudoElements.notation === 'both',\n            syntaxDefinition.pseudoElements.definitions\n                ? (0,_pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.calculatePseudoSignatures)(Array.isArray(syntaxDefinition.pseudoElements.definitions)\n                    ? { NoArgument: syntaxDefinition.pseudoElements.definitions }\n                    : syntaxDefinition.pseudoElements.definitions)\n                : _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures,\n            syntaxDefinition.pseudoElements.unknown === 'accept'\n        ]\n        : [false, false, false, _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures, false], pseudoElementsEnabled = _f[0], pseudoElementsSingleColonNotationEnabled = _f[1], pseudoElementsDoubleColonNotationEnabled = _f[2], pseudoElementsDefinitions = _f[3], pseudoElementsAcceptUnknown = _f[4];\n    var str = '';\n    var l = str.length;\n    var pos = 0;\n    var chr = '';\n    var is = function (comparison) { return chr === comparison; };\n    var isTagStart = function () { return is('*') || (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr); };\n    var rewind = function (newPos) {\n        pos = newPos;\n        chr = str.charAt(pos);\n    };\n    var next = function () {\n        pos++;\n        chr = str.charAt(pos);\n    };\n    var readAndNext = function () {\n        var current = chr;\n        pos++;\n        chr = str.charAt(pos);\n        return current;\n    };\n    /** @throws ParserError */\n    function fail(errorMessage) {\n        var position = Math.min(l - 1, pos);\n        var error = new Error(\"\".concat(errorPrefix).concat(errorMessage, \" Pos: \").concat(position, \".\"));\n        error.position = position;\n        error.name = 'ParserError';\n        throw error;\n    }\n    function assert(condition, errorMessage) {\n        if (!condition) {\n            return fail(errorMessage);\n        }\n    }\n    var assertNonEof = function () {\n        assert(pos < l, 'Unexpected end of input.');\n    };\n    var isEof = function () { return pos >= l; };\n    var pass = function (character) {\n        assert(pos < l, \"Expected \\\"\".concat(character, \"\\\" but end of input reached.\"));\n        assert(chr === character, \"Expected \\\"\".concat(character, \"\\\" but \\\"\").concat(chr, \"\\\" found.\"));\n        pos++;\n        chr = str.charAt(pos);\n    };\n    function matchMulticharIndex(index) {\n        var match = matchMulticharIndexPos(index, pos);\n        if (match) {\n            pos += match.length;\n            chr = str.charAt(pos);\n            return match;\n        }\n    }\n    function matchMulticharIndexPos(index, subPos) {\n        var char = str.charAt(subPos);\n        var charIndex = index[char];\n        if (charIndex) {\n            var subMatch = matchMulticharIndexPos(charIndex.chars, subPos + 1);\n            if (subMatch) {\n                return subMatch;\n            }\n            if (charIndex.self) {\n                return charIndex.self;\n            }\n        }\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#hex-digit-diagram\n     */\n    function parseHex() {\n        var hex = readAndNext();\n        var count = 1;\n        while ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr) && count < _utils_js__WEBPACK_IMPORTED_MODULE_3__.maxHexLength) {\n            hex += readAndNext();\n            count++;\n        }\n        skipSingleWhitespace();\n        return String.fromCharCode(parseInt(hex, 16));\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#string-token-diagram\n     */\n    function parseString(quote) {\n        var result = '';\n        pass(quote);\n        while (pos < l) {\n            if (is(quote)) {\n                next();\n                return result;\n            }\n            else if (is('\\\\')) {\n                next();\n                if (is(quote)) {\n                    result += quote;\n                    next();\n                }\n                else if (chr === '\\n' || chr === '\\f') {\n                    next();\n                }\n                else if (chr === '\\r') {\n                    next();\n                    if (is('\\n')) {\n                        next();\n                    }\n                }\n                else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += chr;\n                    next();\n                }\n            }\n            else {\n                result += chr;\n                next();\n            }\n        }\n        return result;\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#ident-token-diagram\n     */\n    function parseIdentifier() {\n        if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n            return null;\n        }\n        var result = '';\n        while (is('-')) {\n            result += chr;\n            next();\n        }\n        if (result === '-' && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdent)(chr) && !is('\\\\')) {\n            fail('Identifiers cannot consist of a single hyphen.');\n        }\n        if (strict && result.length >= 2) {\n            // Checking this only for strict mode since browsers work fine with these identifiers.\n            fail('Identifiers cannot start with two hyphens with strict mode on.');\n        }\n        if (_utils_js__WEBPACK_IMPORTED_MODULE_3__.digitsChars[chr]) {\n            fail('Identifiers cannot start with hyphens followed by digits.');\n        }\n        while (pos < l) {\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdent)(chr)) {\n                result += readAndNext();\n            }\n            else if (is('\\\\')) {\n                next();\n                assertNonEof();\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += readAndNext();\n                }\n            }\n            else {\n                break;\n            }\n        }\n        return result;\n    }\n    function parsePseudoClassString() {\n        var result = '';\n        while (pos < l) {\n            if (is(')')) {\n                break;\n            }\n            else if (is('\\\\')) {\n                next();\n                if (isEof() && !strict) {\n                    return (result + '\\\\').trim();\n                }\n                assertNonEof();\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += readAndNext();\n                }\n            }\n            else {\n                result += readAndNext();\n            }\n        }\n        return result.trim();\n    }\n    function skipSingleWhitespace() {\n        if (chr === ' ' || chr === '\\t' || chr === '\\f' || chr === '\\n') {\n            next();\n            return;\n        }\n        if (chr === '\\r') {\n            next();\n        }\n        if (chr === '\\n') {\n            next();\n        }\n    }\n    function skipWhitespace() {\n        while (_utils_js__WEBPACK_IMPORTED_MODULE_3__.whitespaceChars[chr]) {\n            next();\n        }\n    }\n    function parseSelector(relative) {\n        if (relative === void 0) { relative = false; }\n        skipWhitespace();\n        var rules = [parseRule(relative)];\n        while (is(',')) {\n            next();\n            skipWhitespace();\n            rules.push(parseRule(relative));\n        }\n        return {\n            type: 'Selector',\n            rules: rules\n        };\n    }\n    function parseAttribute() {\n        pass('[');\n        skipWhitespace();\n        var attr;\n        if (is('|')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            next();\n            var name_1 = parseIdentifier();\n            assert(name_1, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: name_1,\n                namespace: { type: 'NoNamespace' }\n            };\n        }\n        else if (is('*')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            assert(namespaceWildcardEnabled, 'Wildcard namespace is not enabled.');\n            next();\n            pass('|');\n            var name_2 = parseIdentifier();\n            assert(name_2, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: name_2,\n                namespace: { type: 'WildcardNamespace' }\n            };\n        }\n        else {\n            var identifier = parseIdentifier();\n            assert(identifier, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: identifier\n            };\n            if (is('|')) {\n                var savedPos = pos;\n                next();\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n                    assert(namespaceEnabled, 'Namespaces are not enabled.');\n                    var name_3 = parseIdentifier();\n                    assert(name_3, 'Expected attribute name.');\n                    attr = {\n                        type: 'Attribute',\n                        name: name_3,\n                        namespace: { type: 'NamespaceName', name: identifier }\n                    };\n                }\n                else {\n                    rewind(savedPos);\n                }\n            }\n        }\n        assert(attr.name, 'Expected attribute name.');\n        skipWhitespace();\n        if (isEof() && !strict) {\n            return attr;\n        }\n        if (is(']')) {\n            next();\n        }\n        else {\n            attr.operator = matchMulticharIndex(attributesOperatorsIndex);\n            assert(attr.operator, 'Expected a valid attribute selector operator.');\n            skipWhitespace();\n            assertNonEof();\n            if (_utils_js__WEBPACK_IMPORTED_MODULE_3__.quoteChars[chr]) {\n                attr.value = {\n                    type: 'String',\n                    value: parseString(chr)\n                };\n            }\n            else if (substitutesEnabled && is('$')) {\n                next();\n                var name_4 = parseIdentifier();\n                assert(name_4, 'Expected substitute name.');\n                attr.value = {\n                    type: 'Substitution',\n                    name: name_4\n                };\n            }\n            else {\n                var value = parseIdentifier();\n                assert(value, 'Expected attribute value.');\n                attr.value = {\n                    type: 'String',\n                    value: value\n                };\n            }\n            skipWhitespace();\n            if (isEof() && !strict) {\n                return attr;\n            }\n            if (!is(']')) {\n                var caseSensitivityModifier = parseIdentifier();\n                assert(caseSensitivityModifier, 'Expected end of attribute selector.');\n                attr.caseSensitivityModifier = caseSensitivityModifier;\n                assert(attributesCaseSensitivityModifiersEnabled, 'Attribute case sensitivity modifiers are not enabled.');\n                assert(attributesAcceptUnknownCaseSensitivityModifiers ||\n                    attributesCaseSensitivityModifiers[attr.caseSensitivityModifier], 'Unknown attribute case sensitivity modifier.');\n                skipWhitespace();\n                if (isEof() && !strict) {\n                    return attr;\n                }\n            }\n            pass(']');\n        }\n        return attr;\n    }\n    function parseNumber() {\n        var result = '';\n        while (_utils_js__WEBPACK_IMPORTED_MODULE_3__.digitsChars[chr]) {\n            result += readAndNext();\n        }\n        assert(result !== '', 'Formula parse error.');\n        return parseInt(result);\n    }\n    var isNumberStart = function () { return is('-') || is('+') || _utils_js__WEBPACK_IMPORTED_MODULE_3__.digitsChars[chr]; };\n    function parseFormula() {\n        if (is('e') || is('o')) {\n            var ident = parseIdentifier();\n            if (ident === 'even') {\n                skipWhitespace();\n                return [2, 0];\n            }\n            if (ident === 'odd') {\n                skipWhitespace();\n                return [2, 1];\n            }\n        }\n        var firstNumber = null;\n        var firstNumberMultiplier = 1;\n        if (is('-')) {\n            next();\n            firstNumberMultiplier = -1;\n        }\n        if (isNumberStart()) {\n            if (is('+')) {\n                next();\n            }\n            firstNumber = parseNumber();\n            if (!is('\\\\') && !is('n')) {\n                return [0, firstNumber * firstNumberMultiplier];\n            }\n        }\n        if (firstNumber === null) {\n            firstNumber = 1;\n        }\n        firstNumber *= firstNumberMultiplier;\n        var identifier;\n        if (is('\\\\')) {\n            next();\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                identifier = parseHex();\n            }\n            else {\n                identifier = readAndNext();\n            }\n        }\n        else {\n            identifier = readAndNext();\n        }\n        assert(identifier === 'n', 'Formula parse error: expected \"n\".');\n        skipWhitespace();\n        if (is('+') || is('-')) {\n            var sign = is('+') ? 1 : -1;\n            next();\n            skipWhitespace();\n            return [firstNumber, sign * parseNumber()];\n        }\n        else {\n            return [firstNumber, 0];\n        }\n    }\n    function parsePseudoArgument(pseudoName, type, signature) {\n        var argument;\n        if (is('(')) {\n            next();\n            skipWhitespace();\n            if (substitutesEnabled && is('$')) {\n                next();\n                var name_5 = parseIdentifier();\n                assert(name_5, 'Expected substitute name.');\n                argument = {\n                    type: 'Substitution',\n                    name: name_5\n                };\n            }\n            else if (signature.type === 'String') {\n                argument = {\n                    type: 'String',\n                    value: parsePseudoClassString()\n                };\n                assert(argument.value, \"Expected \".concat(type, \" argument value.\"));\n            }\n            else if (signature.type === 'Selector') {\n                argument = parseSelector(true);\n            }\n            else if (signature.type === 'Formula') {\n                var _a = parseFormula(), a = _a[0], b = _a[1];\n                argument = {\n                    type: 'Formula',\n                    a: a,\n                    b: b\n                };\n                if (signature.ofSelector) {\n                    skipWhitespace();\n                    if (is('o') || is('\\\\')) {\n                        var ident = parseIdentifier();\n                        assert(ident === 'of', 'Formula of selector parse error.');\n                        skipWhitespace();\n                        argument = {\n                            type: 'FormulaOfSelector',\n                            a: a,\n                            b: b,\n                            selector: parseRule()\n                        };\n                    }\n                }\n            }\n            else {\n                return fail(\"Invalid \".concat(type, \" signature.\"));\n            }\n            skipWhitespace();\n            if (isEof() && !strict) {\n                return argument;\n            }\n            pass(')');\n        }\n        else {\n            assert(signature.optional, \"Argument is required for \".concat(type, \" \\\"\").concat(pseudoName, \"\\\".\"));\n        }\n        return argument;\n    }\n    function parseTagName() {\n        if (is('*')) {\n            assert(tagNameWildcardEnabled, 'Wildcard tag name is not enabled.');\n            next();\n            return { type: 'WildcardTag' };\n        }\n        else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n            assert(tagNameEnabled, 'Tag names are not enabled.');\n            var name_6 = parseIdentifier();\n            assert(name_6, 'Expected tag name.');\n            return {\n                type: 'TagName',\n                name: name_6\n            };\n        }\n        else {\n            return fail('Expected tag name.');\n        }\n    }\n    function parseTagNameWithNamespace() {\n        if (is('*')) {\n            var savedPos = pos;\n            next();\n            if (!is('|')) {\n                rewind(savedPos);\n                return parseTagName();\n            }\n            next();\n            if (!isTagStart()) {\n                rewind(savedPos);\n                return parseTagName();\n            }\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            assert(namespaceWildcardEnabled, 'Wildcard namespace is not enabled.');\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'WildcardNamespace' };\n            return tagName;\n        }\n        else if (is('|')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            next();\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'NoNamespace' };\n            return tagName;\n        }\n        else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n            var identifier = parseIdentifier();\n            assert(identifier, 'Expected tag name.');\n            if (!is('|')) {\n                assert(tagNameEnabled, 'Tag names are not enabled.');\n                return {\n                    type: 'TagName',\n                    name: identifier\n                };\n            }\n            var savedPos = pos;\n            next();\n            if (!isTagStart()) {\n                rewind(savedPos);\n                return {\n                    type: 'TagName',\n                    name: identifier\n                };\n            }\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'NamespaceName', name: identifier };\n            return tagName;\n        }\n        else {\n            return fail('Expected tag name.');\n        }\n    }\n    function parseRule(relative) {\n        var _a, _b;\n        if (relative === void 0) { relative = false; }\n        var rule = { type: 'Rule', items: [] };\n        if (relative) {\n            var combinator = matchMulticharIndex(combinatorsIndex);\n            if (combinator) {\n                rule.combinator = combinator;\n                skipWhitespace();\n            }\n        }\n        while (pos < l) {\n            if (isTagStart()) {\n                assert(rule.items.length === 0, 'Unexpected tag/namespace start.');\n                rule.items.push(parseTagNameWithNamespace());\n            }\n            else if (is('|')) {\n                var savedPos = pos;\n                next();\n                if (isTagStart()) {\n                    assert(rule.items.length === 0, 'Unexpected tag/namespace start.');\n                    rewind(savedPos);\n                    rule.items.push(parseTagNameWithNamespace());\n                }\n                else {\n                    rewind(savedPos);\n                    break;\n                }\n            }\n            else if (is('.')) {\n                assert(classNamesEnabled, 'Class names are not enabled.');\n                next();\n                var className = parseIdentifier();\n                assert(className, 'Expected class name.');\n                rule.items.push({ type: 'ClassName', name: className });\n            }\n            else if (is('#')) {\n                assert(idEnabled, 'IDs are not enabled.');\n                next();\n                var idName = parseIdentifier();\n                assert(idName, 'Expected ID name.');\n                rule.items.push({ type: 'Id', name: idName });\n            }\n            else if (is('[')) {\n                assert(attributesEnabled, 'Attributes are not enabled.');\n                rule.items.push(parseAttribute());\n            }\n            else if (is(':')) {\n                var isDoubleColon = false;\n                var isPseudoElement = false;\n                next();\n                if (is(':')) {\n                    assert(pseudoElementsEnabled, 'Pseudo elements are not enabled.');\n                    assert(pseudoElementsDoubleColonNotationEnabled, 'Pseudo elements double colon notation is not enabled.');\n                    isDoubleColon = true;\n                    next();\n                }\n                var pseudoName = parseIdentifier();\n                assert(isDoubleColon || pseudoName, 'Expected pseudo-class name.');\n                assert(!isDoubleColon || pseudoName, 'Expected pseudo-element name.');\n                assert(pseudoName, 'Expected pseudo-class name.');\n                assert(!isDoubleColon ||\n                    pseudoElementsAcceptUnknown ||\n                    Object.prototype.hasOwnProperty.call(pseudoElementsDefinitions, pseudoName), \"Unknown pseudo-element \\\"\".concat(pseudoName, \"\\\".\"));\n                isPseudoElement =\n                    pseudoElementsEnabled &&\n                        (isDoubleColon ||\n                            (!isDoubleColon &&\n                                pseudoElementsSingleColonNotationEnabled &&\n                                Object.prototype.hasOwnProperty.call(pseudoElementsDefinitions, pseudoName)));\n                if (isPseudoElement) {\n                    var signature = (_a = pseudoElementsDefinitions[pseudoName]) !== null && _a !== void 0 ? _a : (pseudoElementsAcceptUnknown && _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.defaultPseudoSignature);\n                    var pseudoElement = {\n                        type: 'PseudoElement',\n                        name: pseudoName\n                    };\n                    var argument = parsePseudoArgument(pseudoName, 'pseudo-element', signature);\n                    if (argument) {\n                        assert(argument.type !== 'Formula' && argument.type !== 'FormulaOfSelector', 'Pseudo-elements cannot have formula argument.');\n                        pseudoElement.argument = argument;\n                    }\n                    rule.items.push(pseudoElement);\n                }\n                else {\n                    assert(pseudoClassesEnabled, 'Pseudo-classes are not enabled.');\n                    var signature = (_b = pseudoClassesDefinitions[pseudoName]) !== null && _b !== void 0 ? _b : (pseudoClassesAcceptUnknown && _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.defaultPseudoSignature);\n                    assert(signature, \"Unknown pseudo-class: \\\"\".concat(pseudoName, \"\\\".\"));\n                    var argument = parsePseudoArgument(pseudoName, 'pseudo-class', signature);\n                    var pseudoClass = {\n                        type: 'PseudoClass',\n                        name: pseudoName\n                    };\n                    if (argument) {\n                        pseudoClass.argument = argument;\n                    }\n                    rule.items.push(pseudoClass);\n                }\n            }\n            else {\n                break;\n            }\n        }\n        if (rule.items.length === 0) {\n            if (isEof()) {\n                return fail('Expected rule but end of input reached.');\n            }\n            else {\n                return fail(\"Expected rule but \\\"\".concat(chr, \"\\\" found.\"));\n            }\n        }\n        skipWhitespace();\n        if (!isEof() && !is(',') && !is(')')) {\n            var combinator = matchMulticharIndex(combinatorsIndex);\n            skipWhitespace();\n            rule.nestedRule = parseRule();\n            rule.nestedRule.combinator = combinator;\n        }\n        return rule;\n    }\n    return function (input) {\n        // noinspection SuspiciousTypeOfGuard\n        if (typeof input !== 'string') {\n            throw new Error(\"\".concat(errorPrefix, \"Expected string input.\"));\n        }\n        str = input;\n        l = str.length;\n        pos = 0;\n        chr = str.charAt(0);\n        return parseSelector();\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js":
/*!************************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePseudoSignatures: () => (/* binding */ calculatePseudoSignatures),\n/* harmony export */   defaultPseudoSignature: () => (/* binding */ defaultPseudoSignature),\n/* harmony export */   emptyPseudoSignatures: () => (/* binding */ emptyPseudoSignatures),\n/* harmony export */   inverseCategories: () => (/* binding */ inverseCategories)\n/* harmony export */ });\nvar emptyPseudoSignatures = {};\nvar defaultPseudoSignature = {\n    type: 'String',\n    optional: true\n};\nfunction calculatePseudoSignature(types) {\n    var result = {\n        type: 'NoArgument',\n        optional: false\n    };\n    function setResultType(type) {\n        if (result.type && result.type !== type && result.type !== 'NoArgument') {\n            throw new Error(\"Conflicting pseudo-class argument type: \\\"\".concat(result.type, \"\\\" vs \\\"\").concat(type, \"\\\".\"));\n        }\n        result.type = type;\n    }\n    for (var _i = 0, types_1 = types; _i < types_1.length; _i++) {\n        var type = types_1[_i];\n        if (type === 'NoArgument') {\n            result.optional = true;\n        }\n        if (type === 'Formula') {\n            setResultType('Formula');\n        }\n        if (type === 'FormulaOfSelector') {\n            setResultType('Formula');\n            result.ofSelector = true;\n        }\n        if (type === 'String') {\n            setResultType('String');\n        }\n        if (type === 'Selector') {\n            setResultType('Selector');\n        }\n    }\n    return result;\n}\nfunction inverseCategories(obj) {\n    var result = {};\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\n        var category = _a[_i];\n        var items = obj[category];\n        if (items) {\n            for (var _b = 0, _c = items; _b < _c.length; _b++) {\n                var item = _c[_b];\n                (result[item] || (result[item] = [])).push(category);\n            }\n        }\n    }\n    return result;\n}\nfunction calculatePseudoSignatures(definitions) {\n    var pseudoClassesToArgumentTypes = inverseCategories(definitions);\n    var result = {};\n    for (var _i = 0, _a = Object.keys(pseudoClassesToArgumentTypes); _i < _a.length; _i++) {\n        var pseudoClass = _a[_i];\n        var argumentTypes = pseudoClassesToArgumentTypes[pseudoClass];\n        if (argumentTypes) {\n            result[pseudoClass] = calculatePseudoSignature(argumentTypes);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/render.js":
/*!*************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/render.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   render: () => (/* binding */ render)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js\");\n\nvar errorPrefix = \"css-selector-parser render error: \";\nfunction renderNamespace(namespace) {\n    if (namespace.type === 'WildcardNamespace') {\n        return '*|';\n    }\n    else if (namespace.type === 'NamespaceName') {\n        return \"\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(namespace.name), \"|\");\n    }\n    else if (namespace.type === 'NoNamespace') {\n        return '|';\n    }\n    throw new Error(\"\".concat(errorPrefix, \"Unknown namespace type: \").concat(namespace.type, \".\"));\n}\nfunction renderSubstitution(sub) {\n    return \"$\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(sub.name));\n}\nfunction renderFormula(a, b) {\n    if (a) {\n        var result = \"\".concat(a === 1 ? '' : a === -1 ? '-' : a, \"n\");\n        if (b) {\n            result += \"\".concat(b > 0 ? '+' : '').concat(b);\n        }\n        return result;\n    }\n    else {\n        return String(b);\n    }\n}\n/**\n * Renders CSS Selector AST back to a string.\n *\n * @example\n *\n * import {ast, render} from 'css-selector-parser';\n *\n * const selector = ast.selector({\n *     rules: [\n *         ast.rule({\n *             items: [\n *                 ast.tagName({name: 'a'}),\n *                 ast.id({name: 'user-23'}),\n *                 ast.className({name: 'user'}),\n *                 ast.pseudoClass({name: 'visited'}),\n *                 ast.pseudoElement({name: 'before'})\n *             ]\n *         })\n *     ]\n * });\n *\n * console.log(render(selector)); // a#user-23.user:visited::before\n */\nfunction render(entity) {\n    if (entity.type === 'Selector') {\n        return entity.rules.map(render).join(', ');\n    }\n    if (entity.type === 'Rule') {\n        var result = '';\n        var items = entity.items, combinator = entity.combinator, nestedRule = entity.nestedRule;\n        if (combinator) {\n            result += \"\".concat(combinator, \" \");\n        }\n        for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n            var item = items_1[_i];\n            result += render(item);\n        }\n        if (nestedRule) {\n            result += \" \".concat(render(nestedRule));\n        }\n        return result;\n    }\n    else if (entity.type === 'TagName' || entity.type === 'WildcardTag') {\n        var result = '';\n        var namespace = entity.namespace;\n        if (namespace) {\n            result += renderNamespace(namespace);\n        }\n        if (entity.type === 'TagName') {\n            result += (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name);\n        }\n        else if (entity.type === 'WildcardTag') {\n            result += '*';\n        }\n        return result;\n    }\n    else if (entity.type === 'Id') {\n        return \"#\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name));\n    }\n    else if (entity.type === 'ClassName') {\n        return \".\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name));\n    }\n    else if (entity.type === 'Attribute') {\n        var name_1 = entity.name, namespace = entity.namespace, operator = entity.operator, value = entity.value, caseSensitivityModifier = entity.caseSensitivityModifier;\n        var result = '[';\n        if (namespace) {\n            result += renderNamespace(namespace);\n        }\n        result += (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(name_1);\n        if (operator && value) {\n            result += operator;\n            if (value.type === 'String') {\n                result += (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeString)(value.value);\n            }\n            else if (value.type === 'Substitution') {\n                result += renderSubstitution(value);\n            }\n            else {\n                throw new Error(\"Unknown attribute value type: \".concat(value.type, \".\"));\n            }\n            if (caseSensitivityModifier) {\n                result += \" \".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(caseSensitivityModifier));\n            }\n        }\n        result += ']';\n        return result;\n    }\n    else if (entity.type === 'PseudoClass') {\n        var name_2 = entity.name, argument = entity.argument;\n        var result = \":\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(name_2));\n        if (argument) {\n            result += \"(\".concat(argument.type === 'String' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(argument.value) : render(argument), \")\");\n        }\n        return result;\n    }\n    else if (entity.type === 'PseudoElement') {\n        var name_3 = entity.name, argument = entity.argument;\n        var result = \"::\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(name_3));\n        if (argument) {\n            result += \"(\".concat(argument.type === 'String' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(argument.value) : render(argument), \")\");\n        }\n        return result;\n    }\n    else if (entity.type === 'String') {\n        throw new Error(\"\".concat(errorPrefix, \"String cannot be rendered outside of context.\"));\n    }\n    else if (entity.type === 'Formula') {\n        return renderFormula(entity.a, entity.b);\n    }\n    else if (entity.type === 'FormulaOfSelector') {\n        return renderFormula(entity.a, entity.b) + ' of ' + render(entity.selector);\n    }\n    else if (entity.type === 'Substitution') {\n        return \"$\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name));\n    }\n    throw new Error(\"Unknown type specified to render method: \".concat(entity.type, \".\"));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/render.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js":
/*!*************************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cssSyntaxDefinitions: () => (/* binding */ cssSyntaxDefinitions),\n/* harmony export */   extendSyntaxDefinition: () => (/* binding */ extendSyntaxDefinition),\n/* harmony export */   getXmlOptions: () => (/* binding */ getXmlOptions)\n/* harmony export */ });\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar emptyXmlOptions = {};\nvar defaultXmlOptions = { wildcard: true };\nfunction getXmlOptions(param) {\n    if (param) {\n        if (typeof param === 'boolean') {\n            return defaultXmlOptions;\n        }\n        else {\n            return param;\n        }\n    }\n    else {\n        return emptyXmlOptions;\n    }\n}\nfunction withMigration(migration, merge) {\n    return function (base, extension) { return merge(migration(base), migration(extension)); };\n}\nfunction withNoNegative(merge) {\n    return function (base, extension) {\n        var result = merge(base, extension);\n        if (!result) {\n            throw new Error(\"Syntax definition cannot be null or undefined.\");\n        }\n        return result;\n    };\n}\nfunction withPositive(positive, merge) {\n    return function (base, extension) {\n        if (extension === true) {\n            return positive;\n        }\n        return merge(base === true ? positive : base, extension);\n    };\n}\nfunction mergeSection(values) {\n    return function (base, extension) {\n        if (!extension || !base) {\n            return extension;\n        }\n        if (typeof extension !== 'object' || extension === null) {\n            throw new Error(\"Unexpected syntax definition extension type: \".concat(extension, \".\"));\n        }\n        var result = __assign({}, base);\n        for (var _i = 0, _a = Object.entries(extension); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            var mergeSchema = values[key];\n            result[key] = mergeSchema(base[key], value);\n        }\n        return result;\n    };\n}\nfunction replaceValueIfSpecified(base, extension) {\n    if (extension !== undefined) {\n        return extension;\n    }\n    return base;\n}\nfunction concatArray(base, extension) {\n    if (!extension) {\n        return base;\n    }\n    if (!base) {\n        return extension;\n    }\n    return base.concat(extension);\n}\nfunction mergeDefinitions(base, extension) {\n    if (!extension) {\n        return base;\n    }\n    if (!base) {\n        return extension;\n    }\n    var result = __assign({}, base);\n    for (var _i = 0, _a = Object.entries(extension); _i < _a.length; _i++) {\n        var _b = _a[_i], key = _b[0], value = _b[1];\n        if (!value) {\n            delete result[key];\n            continue;\n        }\n        var baseValue = base[key];\n        if (!baseValue) {\n            result[key] = value;\n            continue;\n        }\n        result[key] = baseValue.concat(value);\n    }\n    return result;\n}\nvar extendSyntaxDefinition = withNoNegative(mergeSection({\n    baseSyntax: replaceValueIfSpecified,\n    tag: withPositive(defaultXmlOptions, mergeSection({\n        wildcard: replaceValueIfSpecified\n    })),\n    ids: replaceValueIfSpecified,\n    classNames: replaceValueIfSpecified,\n    namespace: withPositive(defaultXmlOptions, mergeSection({\n        wildcard: replaceValueIfSpecified\n    })),\n    combinators: concatArray,\n    attributes: mergeSection({\n        operators: concatArray,\n        caseSensitivityModifiers: concatArray,\n        unknownCaseSensitivityModifiers: replaceValueIfSpecified\n    }),\n    pseudoClasses: mergeSection({\n        unknown: replaceValueIfSpecified,\n        definitions: mergeDefinitions\n    }),\n    pseudoElements: mergeSection({\n        unknown: replaceValueIfSpecified,\n        notation: replaceValueIfSpecified,\n        definitions: withMigration(function (definitions) { return (Array.isArray(definitions) ? { NoArgument: definitions } : definitions); }, mergeDefinitions)\n    })\n}));\nvar css1SyntaxDefinition = {\n    tag: {},\n    ids: true,\n    classNames: true,\n    combinators: [],\n    pseudoElements: {\n        unknown: 'reject',\n        notation: 'singleColon',\n        definitions: ['first-letter', 'first-line']\n    },\n    pseudoClasses: {\n        unknown: 'reject',\n        definitions: {\n            NoArgument: ['link', 'visited', 'active']\n        }\n    }\n};\nvar css2SyntaxDefinition = extendSyntaxDefinition(css1SyntaxDefinition, {\n    tag: { wildcard: true },\n    combinators: ['>', '+'],\n    attributes: {\n        unknownCaseSensitivityModifiers: 'reject',\n        operators: ['=', '~=', '|=']\n    },\n    pseudoElements: {\n        definitions: ['before', 'after']\n    },\n    pseudoClasses: {\n        unknown: 'reject',\n        definitions: {\n            NoArgument: ['hover', 'focus', 'first-child'],\n            String: ['lang']\n        }\n    }\n});\nvar selectors3SyntaxDefinition = extendSyntaxDefinition(css2SyntaxDefinition, {\n    namespace: {\n        wildcard: true\n    },\n    combinators: ['~'],\n    attributes: {\n        operators: ['^=', '$=', '*=']\n    },\n    pseudoElements: {\n        notation: 'both'\n    },\n    pseudoClasses: {\n        definitions: {\n            NoArgument: [\n                'root',\n                'last-child',\n                'first-of-type',\n                'last-of-type',\n                'only-child',\n                'only-of-type',\n                'empty',\n                'target',\n                'enabled',\n                'disabled',\n                'checked',\n                'indeterminate'\n            ],\n            Formula: ['nth-child', 'nth-last-child', 'nth-of-type', 'nth-last-of-type'],\n            Selector: ['not']\n        }\n    }\n});\nvar selectors4SyntaxDefinition = extendSyntaxDefinition(selectors3SyntaxDefinition, {\n    combinators: ['||'],\n    attributes: {\n        caseSensitivityModifiers: ['i', 'I', 's', 'S']\n    },\n    pseudoClasses: {\n        definitions: {\n            NoArgument: [\n                'any-link',\n                'local-link',\n                'target-within',\n                'scope',\n                'current',\n                'past',\n                'future',\n                'focus-within',\n                'focus-visible',\n                'read-write',\n                'read-only',\n                'placeholder-shown',\n                'default',\n                'valid',\n                'invalid',\n                'in-range',\n                'out-of-range',\n                'required',\n                'optional',\n                'blank',\n                'user-invalid'\n            ],\n            Formula: ['nth-col', 'nth-last-col'],\n            String: ['dir'],\n            FormulaOfSelector: ['nth-child', 'nth-last-child'],\n            Selector: ['current', 'is', 'where', 'has']\n        }\n    }\n});\nvar progressiveSyntaxDefinition = extendSyntaxDefinition(selectors4SyntaxDefinition, {\n    pseudoElements: {\n        unknown: 'accept'\n    },\n    pseudoClasses: {\n        unknown: 'accept'\n    },\n    attributes: {\n        unknownCaseSensitivityModifiers: 'accept'\n    }\n});\nvar cssSyntaxDefinitions = {\n    css1: css1SyntaxDefinition,\n    css2: css2SyntaxDefinition,\n    css3: selectors3SyntaxDefinition,\n    'selectors-3': selectors3SyntaxDefinition,\n    'selectors-4': selectors4SyntaxDefinition,\n    latest: selectors4SyntaxDefinition,\n    progressive: progressiveSyntaxDefinition\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js":
/*!************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   digitsChars: () => (/* binding */ digitsChars),\n/* harmony export */   escapeIdentifier: () => (/* binding */ escapeIdentifier),\n/* harmony export */   escapeString: () => (/* binding */ escapeString),\n/* harmony export */   identEscapeChars: () => (/* binding */ identEscapeChars),\n/* harmony export */   isHex: () => (/* binding */ isHex),\n/* harmony export */   isIdent: () => (/* binding */ isIdent),\n/* harmony export */   isIdentStart: () => (/* binding */ isIdentStart),\n/* harmony export */   maxHexLength: () => (/* binding */ maxHexLength),\n/* harmony export */   quoteChars: () => (/* binding */ quoteChars),\n/* harmony export */   stringRenderEscapeChars: () => (/* binding */ stringRenderEscapeChars),\n/* harmony export */   whitespaceChars: () => (/* binding */ whitespaceChars)\n/* harmony export */ });\nfunction isIdentStart(c) {\n    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c === '-' || c === '_' || c === '\\\\' || c >= '\\u00a0';\n}\nfunction isIdent(c) {\n    return ((c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c >= '0' && c <= '9') ||\n        c === '-' ||\n        c === '_' ||\n        c >= '\\u00a0');\n}\nfunction isHex(c) {\n    return (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F') || (c >= '0' && c <= '9');\n}\nvar identEscapeChars = {\n    '!': true,\n    '\"': true,\n    '#': true,\n    $: true,\n    '%': true,\n    '&': true,\n    \"'\": true,\n    '(': true,\n    ')': true,\n    '*': true,\n    '+': true,\n    ',': true,\n    '.': true,\n    '/': true,\n    ';': true,\n    '<': true,\n    '=': true,\n    '>': true,\n    '?': true,\n    '@': true,\n    '[': true,\n    '\\\\': true,\n    ']': true,\n    '^': true,\n    '`': true,\n    '{': true,\n    '|': true,\n    '}': true,\n    '~': true\n};\nvar stringRenderEscapeChars = {\n    '\\n': true,\n    '\\r': true,\n    '\\t': true,\n    '\\f': true,\n    '\\v': true\n};\nvar whitespaceChars = {\n    ' ': true,\n    '\\t': true,\n    '\\n': true,\n    '\\r': true,\n    '\\f': true\n};\nvar quoteChars = {\n    '\"': true,\n    \"'\": true\n};\nvar digitsChars = {\n    0: true,\n    1: true,\n    2: true,\n    3: true,\n    4: true,\n    5: true,\n    6: true,\n    7: true,\n    8: true,\n    9: true\n};\nvar maxHexLength = 6;\nfunction escapeIdentifier(s) {\n    var len = s.length;\n    var result = '';\n    var i = 0;\n    while (i < len) {\n        var chr = s.charAt(i);\n        if (identEscapeChars[chr] || (chr === '-' && i === 1 && s.charAt(0) === '-')) {\n            result += '\\\\' + chr;\n        }\n        else {\n            if (chr === '-' ||\n                chr === '_' ||\n                (chr >= 'A' && chr <= 'Z') ||\n                (chr >= 'a' && chr <= 'z') ||\n                (chr >= '0' && chr <= '9' && i !== 0 && !(i === 1 && s.charAt(0) === '-'))) {\n                result += chr;\n            }\n            else {\n                var charCode = chr.charCodeAt(0);\n                if ((charCode & 0xf800) === 0xd800) {\n                    var extraCharCode = s.charCodeAt(i++);\n                    if ((charCode & 0xfc00) !== 0xd800 || (extraCharCode & 0xfc00) !== 0xdc00) {\n                        throw Error('UCS-2(decode): illegal sequence');\n                    }\n                    charCode = ((charCode & 0x3ff) << 10) + (extraCharCode & 0x3ff) + 0x10000;\n                }\n                result += '\\\\' + charCode.toString(16) + ' ';\n            }\n        }\n        i++;\n    }\n    return result.trim();\n}\nfunction escapeString(s) {\n    var len = s.length;\n    var result = '';\n    var i = 0;\n    while (i < len) {\n        var chr = s.charAt(i);\n        if (chr === '\"') {\n            chr = '\\\\\"';\n        }\n        else if (chr === '\\\\') {\n            chr = '\\\\\\\\';\n        }\n        else if (stringRenderEscapeChars[chr]) {\n            chr = '\\\\' + chr.charCodeAt(0).toString(16) + (i === len - 1 ? '' : ' ');\n        }\n        result += chr;\n        i++;\n    }\n    return \"\\\"\".concat(result, \"\\\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js\n");

/***/ })

};
;