{"version": 3, "file": "connection-monitor.service.js", "sourceRoot": "", "sources": ["../../../src/util/database/connection-monitor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAkE;AAClE,6CAAmD;AACnD,qCAAqC;AACrC,+CAAwD;AAGjD,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAKhB;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAEpE,YAEmB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAEJ,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEhC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAMK,AAAN,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAK,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,EAAE,CAAC;gBACtF,MAAM,IAAI,GAAI,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,CAAC;gBAGlD,MAAM,UAAU,GAAG;oBACjB,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxE,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACzE,oBAAoB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxF,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC9D,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iBACrD,CAAC;gBAGF,MAAM,SAAS,GAAG,UAAU,CAAC,gBAAgB,GAAG,UAAU,CAAC,eAAe,CAAC;gBAC3E,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;gBAChF,CAAC;gBAGD,IAAI,IAAI,IAAI,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;oBACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAK,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,EAAE,CAAC;gBACtF,MAAM,IAAI,GAAI,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,CAAC;gBAGlD,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC9D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;oBAGtE,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC3D,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;wBAC5C,IAAI,CAAC;4BACH,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;gCACjC,UAAU,CAAC,GAAG,EAAE,CAAC;4BACnB,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;wBACxC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB;QAC3B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAK,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,EAAE,CAAC;gBACtF,MAAM,IAAI,GAAI,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,CAAC;gBAElD,OAAO;oBACL,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxE,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACzE,oBAAoB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxF,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC9D,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACpD,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa;iBAC3C,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC;gBAClB,oBAAoB,EAAE,CAAC;gBACvB,eAAe,EAAE,CAAC;gBAClB,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,KAAK;aACnB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YACvC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,2BAA2B;QAC/B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAK,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,EAAE,CAAC;gBACtF,MAAM,IAAI,GAAI,IAAI,CAAC,UAAU,CAAC,MAAc,CAAC,IAAI,CAAC;gBAElD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;oBAC/C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,SAAS,QAAQ,CAAC,CAAC;oBAG3C,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC3D,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;wBAC5C,IAAI,CAAC;4BACH,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;gCACjC,UAAU,CAAC,GAAG,EAAE,CAAC;4BACnB,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAED,OAAO,EAAE,mBAAmB,EAAE,SAAS,EAAE,CAAC;gBAC5C,CAAC;YACH,CAAC;YAED,OAAO,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACxC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAjJY,4DAAwB;AAkB7B;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,YAAY,CAAC;;;;qEA6BjC;AAMK;IADL,IAAA,eAAI,EAAC,gBAAgB,CAAC;;;;sEA0BtB;mCA7EU,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,GAAE,CAAA;qCACU,oBAAU;GAL9B,wBAAwB,CAiJpC"}