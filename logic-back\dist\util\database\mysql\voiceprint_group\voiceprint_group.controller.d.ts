import { VoiceprintGroupService } from './voiceprint_group.service';
import { CreateVoiceprintGroupDto, UpdateVoiceprintGroupDto, QueryVoiceprintGroupDto } from './dto/voiceprint_group.dto';
export declare class VoiceprintGroupController {
    private readonly voiceprintGroupService;
    constructor(voiceprintGroupService: VoiceprintGroupService);
    create(req: any, createDto: CreateVoiceprintGroupDto): Promise<import("./entities/voiceprint_group.entity").VoiceprintGroupEntity>;
    findAll(req: any, queryDto: QueryVoiceprintGroupDto): Promise<import("./entities/voiceprint_group.entity").VoiceprintGroupEntity[]>;
    findOne(id: number): Promise<import("./entities/voiceprint_group.entity").VoiceprintGroupEntity | null>;
    update(id: number, updateDto: UpdateVoiceprintGroupDto): Promise<import("./entities/voiceprint_group.entity").VoiceprintGroupEntity>;
    remove(id: number): Promise<{
        success: boolean;
    }>;
}
