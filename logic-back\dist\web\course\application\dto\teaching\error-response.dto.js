"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartialFailureDataDto = exports.PartialFailureDetailsDto = exports.FailedOperationDto = exports.IncompleteSettingsDataDto = exports.EmptyClassDataDto = exports.InsufficientPermissionDataDto = exports.CourseNotFoundDataDto = exports.DuplicateOperationDataDto = exports.ConcurrencyConflictDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ConcurrencyConflictDataDto {
    reason;
    courseId;
    classId;
    lockKey;
    retryAfter;
}
exports.ConcurrencyConflictDataDto = ConcurrencyConflictDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误原因', example: '无法获取操作锁，可能有其他用户正在为此班级开启课程' }),
    __metadata("design:type", String)
], ConcurrencyConflictDataDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 25 }),
    __metadata("design:type", Number)
], ConcurrencyConflictDataDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级ID', example: 10 }),
    __metadata("design:type", Number)
], ConcurrencyConflictDataDto.prototype, "classId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '锁键', example: 'course-teaching:25:10' }),
    __metadata("design:type", String)
], ConcurrencyConflictDataDto.prototype, "lockKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '建议重试时间(秒)', example: 30 }),
    __metadata("design:type", Number)
], ConcurrencyConflictDataDto.prototype, "retryAfter", void 0);
class DuplicateOperationDataDto {
    reason;
    courseId;
    classId;
    teacherId;
    existingRecordId;
    lastExecutionTime;
}
exports.DuplicateOperationDataDto = DuplicateOperationDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误原因', example: '检测到重复的一键上课操作' }),
    __metadata("design:type", String)
], DuplicateOperationDataDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 25 }),
    __metadata("design:type", Number)
], DuplicateOperationDataDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级ID', example: 10 }),
    __metadata("design:type", Number)
], DuplicateOperationDataDto.prototype, "classId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教师ID', example: 100 }),
    __metadata("design:type", Number)
], DuplicateOperationDataDto.prototype, "teacherId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '已存在的记录ID', example: 12 }),
    __metadata("design:type", Number)
], DuplicateOperationDataDto.prototype, "existingRecordId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '上次执行时间', example: '2024-01-26T15:25:00Z' }),
    __metadata("design:type", String)
], DuplicateOperationDataDto.prototype, "lastExecutionTime", void 0);
class CourseNotFoundDataDto {
    courseId;
    reason;
}
exports.CourseNotFoundDataDto = CourseNotFoundDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 25 }),
    __metadata("design:type", Number)
], CourseNotFoundDataDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误原因', example: '指定的课程不存在或状态不是已发布' }),
    __metadata("design:type", String)
], CourseNotFoundDataDto.prototype, "reason", void 0);
class InsufficientPermissionDataDto {
    reason;
    teacherId;
    classId;
}
exports.InsufficientPermissionDataDto = InsufficientPermissionDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误原因', example: '您没有权限操作此班级' }),
    __metadata("design:type", String)
], InsufficientPermissionDataDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教师ID', example: 100 }),
    __metadata("design:type", Number)
], InsufficientPermissionDataDto.prototype, "teacherId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级ID', example: 10 }),
    __metadata("design:type", Number)
], InsufficientPermissionDataDto.prototype, "classId", void 0);
class EmptyClassDataDto {
    reason;
    classId;
    studentCount;
}
exports.EmptyClassDataDto = EmptyClassDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误原因', example: '班级中没有学生，无法开启课程' }),
    __metadata("design:type", String)
], EmptyClassDataDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级ID', example: 10 }),
    __metadata("design:type", Number)
], EmptyClassDataDto.prototype, "classId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生数量', example: 0 }),
    __metadata("design:type", Number)
], EmptyClassDataDto.prototype, "studentCount", void 0);
class IncompleteSettingsDataDto {
    reason;
    courseId;
    missingSettings;
}
exports.IncompleteSettingsDataDto = IncompleteSettingsDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误原因', example: '课程设置不完整，请先完成课程配置' }),
    __metadata("design:type", String)
], IncompleteSettingsDataDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 25 }),
    __metadata("design:type", Number)
], IncompleteSettingsDataDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '缺失的设置项', example: ['templateId', 'taskTemplates'] }),
    __metadata("design:type", Array)
], IncompleteSettingsDataDto.prototype, "missingSettings", void 0);
class FailedOperationDto {
    operation;
    error;
    affectedStudents;
}
exports.FailedOperationDto = FailedOperationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作类型', example: 'applyTemplate' }),
    __metadata("design:type", String)
], FailedOperationDto.prototype, "operation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误信息', example: '模板服务暂时不可用' }),
    __metadata("design:type", String)
], FailedOperationDto.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '受影响的学生数量', example: 3 }),
    __metadata("design:type", Number)
], FailedOperationDto.prototype, "affectedStudents", void 0);
class PartialFailureDetailsDto {
    courseName;
    seriesName;
    className;
    studentCount;
    pointsPerStudent;
    templateName;
    createdTasks;
    failedOperations;
    warningMessages;
}
exports.PartialFailureDetailsDto = PartialFailureDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程名称', example: '第一课：Node.js基础入门' }),
    __metadata("design:type", String)
], PartialFailureDetailsDto.prototype, "courseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列名称', example: 'Node.js后端开发系列' }),
    __metadata("design:type", String)
], PartialFailureDetailsDto.prototype, "seriesName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '班级名称', example: '软件工程2024-1班' }),
    __metadata("design:type", String)
], PartialFailureDetailsDto.prototype, "className", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生数量', example: 30 }),
    __metadata("design:type", Number)
], PartialFailureDetailsDto.prototype, "studentCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每学生积分', example: 100 }),
    __metadata("design:type", Number)
], PartialFailureDetailsDto.prototype, "pointsPerStudent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '模板名称', example: 'Node.js开发环境模板' }),
    __metadata("design:type", String)
], PartialFailureDetailsDto.prototype, "templateName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建的任务列表', type: 'array' }),
    __metadata("design:type", Array)
], PartialFailureDetailsDto.prototype, "createdTasks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '失败的操作列表', type: [FailedOperationDto] }),
    __metadata("design:type", Array)
], PartialFailureDetailsDto.prototype, "failedOperations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '警告信息列表', type: [String] }),
    __metadata("design:type", Array)
], PartialFailureDetailsDto.prototype, "warningMessages", void 0);
class PartialFailureDataDto {
    success;
    teachingRecordId;
    pointsAllocated;
    tasksCreated;
    templateApplied;
    executionTime;
    lockAcquireTime;
    totalExecutionTime;
    details;
}
exports.PartialFailureDataDto = PartialFailureDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否成功', example: true }),
    __metadata("design:type", Boolean)
], PartialFailureDataDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '教学记录ID', example: 16 }),
    __metadata("design:type", Number)
], PartialFailureDataDto.prototype, "teachingRecordId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分配的总积分', example: 2700 }),
    __metadata("design:type", Number)
], PartialFailureDataDto.prototype, "pointsAllocated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建的任务数量', example: 2 }),
    __metadata("design:type", Number)
], PartialFailureDataDto.prototype, "tasksCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否应用了模板', example: false }),
    __metadata("design:type", Boolean)
], PartialFailureDataDto.prototype, "templateApplied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作执行时间', example: '2024-01-26T15:35:00Z' }),
    __metadata("design:type", String)
], PartialFailureDataDto.prototype, "executionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '获取锁耗时(毫秒)', example: 32 }),
    __metadata("design:type", Number)
], PartialFailureDataDto.prototype, "lockAcquireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总执行耗时(毫秒)', example: 1850 }),
    __metadata("design:type", Number)
], PartialFailureDataDto.prototype, "totalExecutionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '详细信息', type: PartialFailureDetailsDto }),
    __metadata("design:type", PartialFailureDetailsDto)
], PartialFailureDataDto.prototype, "details", void 0);
//# sourceMappingURL=error-response.dto.js.map