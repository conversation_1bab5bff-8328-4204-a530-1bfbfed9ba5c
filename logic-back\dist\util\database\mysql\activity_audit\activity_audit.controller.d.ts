import { ActivityAuditService } from './activity_audit.service';
import { CreateActivityAuditDto } from './dto/create-activity_audit.dto';
import { UpdateActivityAuditDto } from './dto/update-activity_audit.dto';
import { ActivityAudit } from './entities/activity_audit.entity';
export declare class ActivityAuditController {
    private readonly activityAuditService;
    constructor(activityAuditService: ActivityAuditService);
    create(createActivityAuditDto: CreateActivityAuditDto): Promise<ActivityAudit>;
    findAll(): Promise<ActivityAudit[]>;
    findByActivityId(activityId: string): Promise<ActivityAudit[]>;
    findByAuditorId(auditorId: string): Promise<ActivityAudit[]>;
    findByResult(result: string): Promise<ActivityAudit[]>;
    findOne(id: string): Promise<ActivityAudit>;
    update(id: string, updateActivityAuditDto: UpdateActivityAuditDto): Promise<ActivityAudit>;
    remove(id: string): Promise<void>;
    hardRemove(id: string): Promise<void>;
}
