"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBulkStudentSelfAssessmentSubmissionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class SelfAssessmentSubmissionItemDto {
    assessmentItemId;
    score;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '自评项ID' }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], SelfAssessmentSubmissionItemDto.prototype, "assessmentItemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分数 (1-5)' }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], SelfAssessmentSubmissionItemDto.prototype, "score", void 0);
class CreateBulkStudentSelfAssessmentSubmissionDto {
    assignmentId;
    studentId;
    submissions;
}
exports.CreateBulkStudentSelfAssessmentSubmissionDto = CreateBulkStudentSelfAssessmentSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的学生任务完成情况ID' }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateBulkStudentSelfAssessmentSubmissionDto.prototype, "assignmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生ID' }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateBulkStudentSelfAssessmentSubmissionDto.prototype, "studentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [SelfAssessmentSubmissionItemDto], description: '提交的自评项列表' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => SelfAssessmentSubmissionItemDto),
    __metadata("design:type", Array)
], CreateBulkStudentSelfAssessmentSubmissionDto.prototype, "submissions", void 0);
//# sourceMappingURL=create-bulk-student_self_assessment_submission.dto.js.map