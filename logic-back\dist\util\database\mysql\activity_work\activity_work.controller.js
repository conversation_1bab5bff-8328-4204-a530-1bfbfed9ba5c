"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityWorkController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const activity_work_service_1 = require("./activity_work.service");
const create_activity_work_dto_1 = require("./dto/create-activity_work.dto");
const update_activity_work_dto_1 = require("./dto/update-activity_work.dto");
const activity_work_entity_1 = require("./entities/activity_work.entity");
let ActivityWorkController = class ActivityWorkController {
    activityWorkService;
    constructor(activityWorkService) {
        this.activityWorkService = activityWorkService;
    }
    create(createActivityWorkDto) {
        return this.activityWorkService.create(createActivityWorkDto);
    }
    findAll() {
        return this.activityWorkService.findAll();
    }
    findByActivityId(activityId) {
        return this.activityWorkService.findByActivityId(+activityId);
    }
    findByWorkId(workId) {
        return this.activityWorkService.findByWorkId(+workId);
    }
    findByUserId(userId) {
        return this.activityWorkService.findByUserId(+userId);
    }
    findByIsSelected(isSelected) {
        return this.activityWorkService.findByIsSelected(+isSelected);
    }
    findWinners() {
        return this.activityWorkService.findWinners();
    }
    updateIsSelected(id, isSelected) {
        return this.activityWorkService.updateIsSelected(+id, +isSelected);
    }
    updateIsWinner(id, isWinner) {
        return this.activityWorkService.updateIsWinner(+id, +isWinner);
    }
    removeByActivityId(activityId) {
        return this.activityWorkService.removeByActivityId(+activityId);
    }
    findOne(id) {
        return this.activityWorkService.findOne(+id);
    }
    update(id, updateActivityWorkDto) {
        return this.activityWorkService.update(+id, updateActivityWorkDto);
    }
    remove(id) {
        return this.activityWorkService.remove(+id);
    }
    hardRemove(id) {
        return this.activityWorkService.hardRemove(+id);
    }
};
exports.ActivityWorkController = ActivityWorkController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建活动作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '创建成功', type: activity_work_entity_1.ActivityWork }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_work_dto_1.CreateActivityWorkDto]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有活动作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_work_entity_1.ActivityWork] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定活动的所有作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_work_entity_1.ActivityWork] }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findByActivityId", null);
__decorate([
    (0, common_1.Get)('work/:workId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定作品的所有活动关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_work_entity_1.ActivityWork] }),
    __param(0, (0, common_1.Param)('workId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findByWorkId", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定用户参与的活动作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_work_entity_1.ActivityWork] }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('selected/:isSelected'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定入选状态的活动作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_work_entity_1.ActivityWork] }),
    __param(0, (0, common_1.Param)('isSelected')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findByIsSelected", null);
__decorate([
    (0, common_1.Get)('winners'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有获奖作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [activity_work_entity_1.ActivityWork] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findWinners", null);
__decorate([
    (0, common_1.Patch)(':id/selected/:isSelected'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动作品入选状态' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: activity_work_entity_1.ActivityWork }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('isSelected')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "updateIsSelected", null);
__decorate([
    (0, common_1.Patch)(':id/winner/:isWinner'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动作品获奖状态' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: activity_work_entity_1.ActivityWork }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('isWinner')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "updateIsWinner", null);
__decorate([
    (0, common_1.Delete)('activity/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '删除指定活动的所有作品关联（软删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "removeByActivityId", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取活动作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: activity_work_entity_1.ActivityWork }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '活动作品关联不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动作品关联' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: activity_work_entity_1.ActivityWork }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '活动作品关联不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_activity_work_dto_1.UpdateActivityWorkDto]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除活动作品关联（软删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)(':id/hard'),
    (0, swagger_1.ApiOperation)({ summary: '彻底删除活动作品关联（硬删除）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ActivityWorkController.prototype, "hardRemove", null);
exports.ActivityWorkController = ActivityWorkController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/活动作品关联(activity_work)'),
    (0, common_1.Controller)('activity-work'),
    __metadata("design:paramtypes", [activity_work_service_1.ActivityWorkService])
], ActivityWorkController);
//# sourceMappingURL=activity_work.controller.js.map