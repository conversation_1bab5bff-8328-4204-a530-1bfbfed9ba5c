"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserWorkLikeModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_work_like_service_1 = require("./user_work_like.service");
const user_work_like_controller_1 = require("./user_work_like.controller");
const user_work_like_entity_1 = require("./entities/user_work_like.entity");
let UserWorkLikeModule = class UserWorkLikeModule {
};
exports.UserWorkLikeModule = UserWorkLikeModule;
exports.UserWorkLikeModule = UserWorkLikeModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([user_work_like_entity_1.UserWorkLike])],
        controllers: [user_work_like_controller_1.UserWorkLikeController],
        providers: [user_work_like_service_1.UserWorkLikeService],
        exports: [user_work_like_service_1.UserWorkLikeService],
    })
], UserWorkLikeModule);
//# sourceMappingURL=user_work_like.module.js.map