mysql:
  type: mysql
  host: ************
  port: 13306
  username: root
  password: '123456'
  database: logicleaptest
  charset: utf8mb4
  entities:
    - 'dist/**/*.entity{.ts,.js}'
    # - "src/**/*.entity{.ts,.js}"
  synchronize: false # 生产环境中设置为false
  # 优化连接池配置
  extra:
    connectionLimit: 20        # 减少连接数，避免耗尽
    waitForConnections: true
    queueLimit: 50            # 减少队列限制
    acquireTimeout: 30000     # 减少获取连接超时时间
    timeout: 30000            # 减少查询超时时间
    reconnect: true           # 自动重连
    maxReconnects: 3          # 最大重连次数
    # 添加连接释放配置
    idleTimeout: 300000       # 空闲连接5分钟后释放
    evictInterval: 60000      # 每分钟检查一次空闲连接
    maxLifetime: 1800000      # 连接最大生存时间30分钟
  # 添加重试机制
  retryAttempts: 3
  retryDelay: 3000
  keepConnectionAlive: true
  connectTimeout: 30000

redis:
  host: 127.0.0.1
  port: 6379
  password: ''
  db: 0
