"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n            const mockCourses = [\n                {\n                    id: 1,\n                    title: \"Node.js基础入门\",\n                    name: \"Node.js基础入门\",\n                    seriesTitle: \"Node.js开发系列\",\n                    description: \"Node.js基础知识学习\",\n                    category: \"programming\",\n                    status: \"inactive\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: 2,\n                    title: \"Express框架应用\",\n                    name: \"Express框架应用\",\n                    seriesTitle: \"Node.js开发系列\",\n                    description: \"Express框架的使用\",\n                    category: \"programming\",\n                    status: \"active\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }\n            ];\n            setCourseList(mockCourses);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n            setLoading(false);\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除子课程成功\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n            } else {\n                notification.error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程失败:\", error);\n            notification.error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(values.courseId);\n            if (res.code === 200) {\n                notification.success(\"发布课程成功\");\n                setIsPublishCourseModalVisible(false);\n                publishCourseForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，课程信息:\", publishData);\n                // 刷新课程列表\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布课程失败:\", error);\n            notification.error(\"发布课程失败，请重试\");\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0\",\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-6 text-gray-600\",\n                        children: [\n                            \"└─ \",\n                            text\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 761,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 772,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 771,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: \"确定要删除这个子课程吗？\",\n                                onConfirm: ()=>handleDeleteSubCourse(record.id, record.seriesId),\n                                okText: \"确定\",\n                                cancelText: \"取消\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 967,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 977,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishCourseModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 973,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 965,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                placeholder: \"搜索课程名称、描述或分类\",\n                                allowClear: true,\n                                style: {\n                                    width: 300\n                                },\n                                onSearch: setSearchKeyword,\n                                onChange: (e)=>setSearchKeyword(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setIsAddCourseModalVisible(true),\n                                        children: \"添加课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        type: \"default\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setIsAddSeriesModalVisible(true),\n                                        children: \"添加系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: filteredCourses,\n                        rowKey: \"id\",\n                        loading: loading,\n                        pagination: {\n                            pageSize: 10,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1026,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 993,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1078,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1065,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1099,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1129,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1136,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1132,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1119,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1159,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1194,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1169,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1218,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1219,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1217,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1216,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1226,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1229,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1207,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1203,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1253,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1258,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1252,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1265,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1267,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1263,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1239,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1281,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1299,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1299,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1301,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1288,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1060,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1040,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1331,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1326,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1339,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1334,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1348,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1349,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1350,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1342,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1361,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1362,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1360,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1355,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1321,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1309,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1387,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1400,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1395,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1422,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1421,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1427,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1426,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1429,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1430,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1425,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1412,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1408,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1447,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1448,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1446,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1440,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1457,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1452,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1476,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1475,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1469,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1464,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1382,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1369,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1510,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1502,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1522,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1521,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1520,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1528,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1527,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1526,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1534,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1532,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1540,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1539,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1538,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1546,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1545,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1552,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1551,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1550,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1558,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1557,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1556,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1564,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1563,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1519,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1513,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1578,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1579,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1580,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1581,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1577,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1571,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1590,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1585,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1605,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1606,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1604,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1598,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1497,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1485,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1643,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1635,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1630,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1655,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1650,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1664,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1666,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1667,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1668,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1669,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1665,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1663,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1625,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1613,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: ()=>{\n                    setIsPublishCourseModalVisible(false);\n                    publishCourseForm.resetFields();\n                },\n                onOk: ()=>publishCourseForm.submit(),\n                okText: \"发布课程\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择要发布的课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: [\n                                            course.title || course.name,\n                                            \" (\",\n                                            course.seriesTitle || \"未分类\",\n                                            \")\"\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1706,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1698,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1693,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1718,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1713,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1727,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1729,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有完整的课程内容才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1730,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看课程的访问统计\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1731,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1732,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1728,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1726,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1688,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1676,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"4srZmRgZW5ilSLYbIxzoZGiE/iY=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});