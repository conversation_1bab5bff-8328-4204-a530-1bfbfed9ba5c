export declare class GetSeriesListQueryDto {
    page?: number;
    pageSize?: number;
    category?: number;
    status?: number;
    tagIds?: string;
    keyword?: string;
    hasVideo?: number;
    hasDocument?: number;
    hasAudio?: number;
    sortBy?: string;
}
export declare class TagInfoDto {
    id: number;
    name: string;
    color: string;
    category: number;
    categoryLabel: string;
}
export declare class ContentSummaryDto {
    hasVideo: boolean;
    hasDocument: boolean;
    hasAudio: boolean;
    videoCourseCount: number;
    documentCourseCount: number;
    averageVideoDuration: number;
    totalResourcesCount: number;
}
export declare class SeriesInfoDto {
    id: number;
    title: string;
    description: string;
    coverImage: string;
    category: number;
    categoryLabel: string;
    status: number;
    statusLabel: string;
    projectMembers: string;
    totalCourses: number;
    totalStudents: number;
    contentSummary: ContentSummaryDto;
    createdAt: string;
    tags: TagInfoDto[];
}
export declare class PaginationDto {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export declare class FilterStatsDto {
    totalSeries: number;
    officialCount: number;
    communityCount: number;
    videoSeriesCount: number;
    documentSeriesCount: number;
}
export declare class SeriesListDataDto {
    list: SeriesInfoDto[];
    pagination: PaginationDto;
    filterStats: FilterStatsDto;
}
export declare class SeriesListResponseDto {
    code: number;
    message: string;
    data: SeriesListDataDto;
}
