{"version": 3, "file": "series-detail.dto.js", "sourceRoot": "", "sources": ["../../../../../../src/web/course/application/dto/marketplace/series-detail.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,uDAA+C;AAG/C,MAAa,aAAa;IAExB,EAAE,CAAS;IAGX,KAAK,CAAS;IAGd,WAAW,CAAS;IAGpB,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,MAAM,CAAS;IAGf,WAAW,CAAS;IAGpB,QAAQ,CAAS;IAGjB,WAAW,CAAS;IAGpB,QAAQ,CAAS;IAGjB,aAAa,CAAS;IAGtB,kBAAkB,CAAS;IAG3B,SAAS,CAAS;IAGlB,kBAAkB,CAAS;IAG3B,cAAc,CAAS;CACxB;AA7CD,sCA6CC;AA3CC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;yCACtC;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;4CACjD;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;;kDAC/C;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;;iDACpE;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;iDAC9B;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CACnD;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACnC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CAC1C;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;kDACvC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CAC1C;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;oDACjC;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;yDAC7B;AAG3B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;gDAChD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;yDAChC;AAG3B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDAC1B;AAIzB,MAAa,gBAAiB,SAAQ,aAAa;IAkBjD,aAAa,CAAsB;IAcnC,YAAY,CAAQ;IAYpB,mBAAmB,CAAQ;CAC5B;AA7CD,4CA6CC;AA3BC;IAjBC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE;YACP,QAAQ,EAAE,CAAC;YACX,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC;YACX,KAAK,EAAE;gBACL,GAAG,EAAE,6CAA6C;gBAClD,IAAI,EAAE,iBAAiB;aACxB;YACD,QAAQ,EAAE;gBACR,GAAG,EAAE,uDAAuD;gBAC5D,IAAI,EAAE,iBAAiB;aACxB;YACD,KAAK,EAAE,IAAI;SACZ;KACF,CAAC;;uDACiC;AAcnC;IAZC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE;YACP;gBACE,KAAK,EAAE,MAAM;gBACb,OAAO,EAAE;oBACP,mBAAmB;oBACnB,gBAAgB;iBACjB;aACF;SACF;KACF,CAAC;;sDACkB;AAYpB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE;YACP;gBACE,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,oBAAoB;gBACzB,WAAW,EAAE,aAAa;aAC3B;SACF;KACF,CAAC;;6DACyB;AAI7B,MAAa,mBAAmB;IAE9B,EAAE,CAAS;IAGX,KAAK,CAAS;IAGd,WAAW,CAAS;IAGpB,UAAU,CAAS;IAGnB,QAAQ,CAAS;IAGjB,aAAa,CAAS;IAGtB,MAAM,CAAS;IAGf,WAAW,CAAS;IAGpB,cAAc,CAAS;IAGvB,YAAY,CAAS;IAGrB,aAAa,CAAS;IAGtB,SAAS,CAAS;IAGlB,SAAS,CAAS;IAGlB,SAAS,CAAS;IAGlB,IAAI,CAAe;IAGnB,OAAO,CAAkB;IAGzB,aAAa,CAAmB;IAGhC,eAAe,CAAS;CACzB;AAtDD,kDAsDC;AApDC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;+CACxC;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;kDAC/C;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;;wDACjE;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;uDACtE;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDACxC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0DAC9B;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;mDACjD;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;wDACjC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;2DACtC;AAGvB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;yDAC5B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;0DAC7B;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;sDAClC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;sDACpD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;sDACpD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,4BAAU,CAAC,EAAE,CAAC;;iDACtC;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;;oDACnC;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;8BACnD,gBAAgB;0DAAC;AAGhC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;4DAC3B;AAI1B,MAAa,uBAAuB;IAElC,IAAI,CAAS;IAGb,OAAO,CAAS;IAGhB,IAAI,CAAsB;CAC3B;AATD,0DASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;qDACvC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;wDACzC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,mBAAmB,EAAE,CAAC;8BAClE,mBAAmB;qDAAC"}