"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebEventsTaskController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const web_events_task_service_1 = require("./web_events_task.service");
const dto_1 = require("../../util/database/mysql/activity_events_task/dto");
const activity_events_task_entity_1 = require("../../util/database/mysql/activity_events_task/entities/activity_events_task.entity");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
let WebEventsTaskController = class WebEventsTaskController {
    webEventsTaskService;
    httpResponseResultService;
    constructor(webEventsTaskService, httpResponseResultService) {
        this.webEventsTaskService = webEventsTaskService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async createTask(createDto, req) {
        try {
            if (req?.user?.id) {
                createDto.creatorId = req.user.id;
                createDto.userId = createDto.userId || req.user.id;
            }
            const result = await this.webEventsTaskService.createTask(createDto);
            return this.httpResponseResultService.success(result, '创建赛事任务成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getMyTasks(req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('用户未登录');
            }
            const result = await this.webEventsTaskService.getUserTasks(userId);
            return this.httpResponseResultService.success(result, '获取任务列表成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getActivityTasks(activityId) {
        try {
            const result = await this.webEventsTaskService.getActivityTasks(activityId);
            return this.httpResponseResultService.success(result, '获取活动任务列表成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getMyTaskStatistics(req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('用户未登录');
            }
            const result = await this.webEventsTaskService.getTaskStatistics(userId);
            return this.httpResponseResultService.success(result, '获取统计信息成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getUpcomingTasks(req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('用户未登录');
            }
            const result = await this.webEventsTaskService.getUpcomingTasks(userId);
            return this.httpResponseResultService.success(result, '获取即将开始的任务成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getOngoingTasks(req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('用户未登录');
            }
            const result = await this.webEventsTaskService.getOngoingTasks(userId);
            return this.httpResponseResultService.success(result, '获取进行中的任务成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getMyTasksByStatus(status, req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('用户未登录');
            }
            const result = await this.webEventsTaskService.getUserTasksByStatus(userId, status);
            return this.httpResponseResultService.success(result, '获取任务列表成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async searchMyTasks(keyword, req) {
        try {
            const userId = req?.user?.id;
            if (!userId) {
                return this.httpResponseResultService.error('用户未登录');
            }
            const result = await this.webEventsTaskService.searchUserTasks(userId, keyword);
            return this.httpResponseResultService.success(result, '搜索任务成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async getTaskDetail(id, req) {
        try {
            const userId = req?.user?.id;
            const result = await this.webEventsTaskService.getTaskDetail(id, userId);
            return this.httpResponseResultService.success(result, '获取任务详情成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async updateTask(id, updateDto, req) {
        try {
            const userId = req?.user?.id;
            const result = await this.webEventsTaskService.updateTask(id, updateDto, userId);
            return this.httpResponseResultService.success(result, '更新任务成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async updateTaskStatus(id, updateStatusDto, req) {
        try {
            const userId = req?.user?.id;
            const result = await this.webEventsTaskService.updateTaskStatus(id, updateStatusDto.status, userId);
            return this.httpResponseResultService.success(result, '更新任务状态成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async adminReviewTask(id, reviewDto, req) {
        try {
            const userId = req?.user?.id;
            const userRoleId = req?.user?.roleId;
            if (userRoleId !== 4) {
                return this.httpResponseResultService.error('没有权限执行此操作，需要管理员权限');
            }
            const result = await this.webEventsTaskService.adminReviewTask(id, reviewDto.status, reviewDto.remark);
            return this.httpResponseResultService.success(result, '审核任务状态成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async submitTask(id, submitDto, req) {
        try {
            const userId = req?.user?.id;
            const result = await this.webEventsTaskService.submitTask(id, submitDto, userId);
            return this.httpResponseResultService.success(result, submitDto.isResubmit ? '重新提交成功' : '提交成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async checkSubmitStatus(id, req) {
        try {
            const userId = req?.user?.id;
            const result = await this.webEventsTaskService.canSubmitTask(id, userId);
            return this.httpResponseResultService.success(result, '检查提交状态成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async batchUpdateTaskStatus(batchUpdateDto, req) {
        try {
            const userId = req?.user?.id;
            await this.webEventsTaskService.batchUpdateTaskStatus(batchUpdateDto.taskIds, batchUpdateDto.status, userId);
            return this.httpResponseResultService.success(null, '批量更新任务状态成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
    async deleteTask(id, req) {
        try {
            const userId = req?.user?.id;
            await this.webEventsTaskService.deleteTask(id, userId);
            return this.httpResponseResultService.success(null, '删除任务成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message);
        }
    }
};
exports.WebEventsTaskController = WebEventsTaskController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建赛事任务' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateActivityEventsTaskDto, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "createTask", null);
__decorate([
    (0, common_1.Get)('my-tasks'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的赛事任务列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getMyTasks", null);
__decorate([
    (0, common_1.Get)('activity/:activityId/tasks'),
    (0, swagger_1.ApiOperation)({ summary: '获取活动的赛事任务列表' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [activity_events_task_entity_1.ActivityEventsTask] }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getActivityTasks", null);
__decorate([
    (0, common_1.Get)('my-tasks/statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的任务统计信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getMyTaskStatistics", null);
__decorate([
    (0, common_1.Get)('my-tasks/upcoming'),
    (0, swagger_1.ApiOperation)({ summary: '获取即将开始的任务' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getUpcomingTasks", null);
__decorate([
    (0, common_1.Get)('my-tasks/ongoing'),
    (0, swagger_1.ApiOperation)({ summary: '获取进行中的任务' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getOngoingTasks", null);
__decorate([
    (0, common_1.Get)('my-tasks/status/:status'),
    (0, swagger_1.ApiOperation)({ summary: '根据状态获取我的任务' }),
    (0, swagger_1.ApiParam)({ name: 'status', description: '任务状态：0-待开始 1-进行中 2-已提交 3-已审核 4-审核不通过' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('status', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getMyTasksByStatus", null);
__decorate([
    (0, common_1.Get)('my-tasks/search'),
    (0, swagger_1.ApiOperation)({ summary: '搜索我的任务' }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', description: '搜索关键词', required: false }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '搜索成功' }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "searchMyTasks", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取任务详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "getTaskDetail", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新任务信息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateActivityEventsTaskDto, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "updateTask", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新任务状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateTaskStatusDto, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "updateTaskStatus", null);
__decorate([
    (0, common_1.Patch)(':id/admin-review'),
    (0, swagger_1.ApiOperation)({ summary: '管理员审核任务状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '审核成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "adminReviewTask", null);
__decorate([
    (0, common_1.Post)(':id/submit'),
    (0, swagger_1.ApiOperation)({ summary: '提交赛事任务（支持重新提交）' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '提交成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.SubmitEventsTaskDto, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "submitTask", null);
__decorate([
    (0, common_1.Get)(':id/submit-status'),
    (0, swagger_1.ApiOperation)({ summary: '检查任务是否可以提交' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检查成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "checkSubmitStatus", null);
__decorate([
    (0, common_1.Post)('batch/status'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新任务状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BatchUpdateStatusDto, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "batchUpdateTaskStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除任务（软删除）' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebEventsTaskController.prototype, "deleteTask", null);
exports.WebEventsTaskController = WebEventsTaskController = __decorate([
    (0, swagger_1.ApiTags)('赛事任务管理（前端API）'),
    (0, common_1.Controller)('api/v1/web/events-task'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [web_events_task_service_1.WebEventsTaskService,
        http_response_result_service_1.HttpResponseResultService])
], WebEventsTaskController);
//# sourceMappingURL=web_events_task.controller.js.map