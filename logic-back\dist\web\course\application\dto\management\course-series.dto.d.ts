export declare class CreateCourseSeriesDto {
    title: string;
    description?: string;
    coverImage?: string;
    category?: number;
    projectMembers?: string;
    tagIds?: number[];
}
export declare class UpdateCourseSeriesDto {
    title?: string;
    description?: string;
    coverImage?: string;
    category?: number;
    projectMembers?: string;
    tagIds?: number[];
}
export declare class CourseSeriesResponseDto {
    id: number;
    title: string;
    description: string;
    coverImage: string;
    category: number;
    status: number;
    projectMembers: string;
    totalCourses: number;
    totalStudents: number;
    creatorId: number;
    createdAt: Date;
    updatedAt: Date;
}
