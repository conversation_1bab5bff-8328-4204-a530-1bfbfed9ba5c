import { Repository } from 'typeorm';
import { CreateActivityAuditDto } from './dto/create-activity_audit.dto';
import { UpdateActivityAuditDto } from './dto/update-activity_audit.dto';
import { ActivityAudit } from './entities/activity_audit.entity';
export declare class ActivityAuditService {
    private readonly activityAuditRepository;
    constructor(activityAuditRepository: Repository<ActivityAudit>);
    create(createActivityAuditDto: CreateActivityAuditDto): Promise<ActivityAudit>;
    findAll(): Promise<ActivityAudit[]>;
    findOne(id: number): Promise<ActivityAudit>;
    update(id: number, updateActivityAuditDto: UpdateActivityAuditDto): Promise<ActivityAudit>;
    remove(id: number): Promise<void>;
    hardRemove(id: number): Promise<void>;
    findByActivityId(activityId: number): Promise<ActivityAudit[]>;
    findByAuditorId(auditorId: number): Promise<ActivityAudit[]>;
    findByResult(result: number): Promise<ActivityAudit[]>;
}
