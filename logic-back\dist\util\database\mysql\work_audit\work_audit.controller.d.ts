import { WorkAuditService } from './work_audit.service';
import { CreateWorkAuditDto } from './dto/create-work_audit.dto';
import { UpdateWorkAuditDto } from './dto/update-work_audit.dto';
import { WorkAudit } from './entities/work_audit.entity';
export declare class WorkAuditController {
    private readonly workAuditService;
    constructor(workAuditService: WorkAuditService);
    create(createWorkAuditDto: CreateWorkAuditDto): Promise<WorkAudit>;
    findAll(): Promise<WorkAudit[]>;
    findOne(id: string): Promise<WorkAudit>;
    update(id: string, updateWorkAuditDto: UpdateWorkAuditDto): Promise<WorkAudit>;
    remove(id: string): Promise<void>;
}
