export declare class CourseContentInfoDto {
    hasVideo: number;
    hasDocument: number;
    hasAudio: number;
    videoDuration: number;
    videoDurationLabel: string;
    videoName: string;
    resourcesCount: number;
}
export declare class CourseSettingsInfoDto {
    templateId: number;
    templateName: string;
    requiredPoints: number;
    autoCreateTasks: number;
    autoCreateTasksLabel: string;
}
export declare class TaskTemplateInfoDto {
    id: number;
    taskName: string;
    taskDescription: string;
    durationDays: number;
    status: number;
    statusLabel: string;
    attachmentsCount: number;
    assessmentItemsCount: number;
    firstAttachmentType: string;
}
export declare class CoursePreviewInfoDto {
    willAllocatePoints: boolean;
    pointsPerStudent: number;
    willApplyTemplate: boolean;
    willCreateTasks: boolean;
    tasksCount: number;
}
export declare class CourseSettingsDataDto {
    courseId: number;
    courseName: string;
    seriesName: string;
    contentInfo: CourseContentInfoDto;
    settings: CourseSettingsInfoDto;
    taskTemplates: TaskTemplateInfoDto[];
    preview: CoursePreviewInfoDto;
}
export declare class CourseSettingsResponseDto {
    code: number;
    message: string;
    data: CourseSettingsDataDto;
}
