"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityService = void 0;
const common_1 = require("@nestjs/common");
const activity_service_1 = require("../../util/database/mysql/activity/activity.service");
const activity_submit_service_1 = require("../../util/database/mysql/activity_submit/activity_submit.service");
const activity_events_task_service_1 = require("../../util/database/mysql/activity_events_task/activity_events_task.service");
const user_student_service_1 = require("../../util/database/mysql/user_student/user_student.service");
const user_school_service_1 = require("../../util/database/mysql/user_school/user_school.service");
const user_class_service_1 = require("../../util/database/mysql/user_class/user_class.service");
const user_info_service_1 = require("../../util/database/mysql/user_info/user_info.service");
const ali_oss_service_1 = require("../../util/ali_service/ali_oss/ali_oss.service");
let WebActivityService = class WebActivityService {
    activityService;
    activitySubmitService;
    activityEventsTaskService;
    userStudentService;
    userSchoolService;
    userClassService;
    userInfoService;
    aliOssService;
    constructor(activityService, activitySubmitService, activityEventsTaskService, userStudentService, userSchoolService, userClassService, userInfoService, aliOssService) {
        this.activityService = activityService;
        this.activitySubmitService = activitySubmitService;
        this.activityEventsTaskService = activityEventsTaskService;
        this.userStudentService = userStudentService;
        this.userSchoolService = userSchoolService;
        this.userClassService = userClassService;
        this.userInfoService = userInfoService;
        this.aliOssService = aliOssService;
    }
    async create(data, creatorId) {
        const createActivityDto = {
            name: data.name,
            startTime: data.startTime,
            endTime: data.endTime,
            coverImage: data.coverImage,
            organizer: data.organizer,
            creatorId: creatorId,
            activityType: data.activityType || 1,
            attachmentFiles: data.attachmentFiles,
            promotionImage: data.promotionImage,
            competitionGroups: data.competitionGroups,
            registrationForm: data.registrationForm,
        };
        return await this.activityService.create(createActivityDto);
    }
    async updateActivity(id, data) {
        return await this.activityService.updateActivity(id, data);
    }
    async deleteActivity(id) {
        return await this.activityService.deleteActivity(id);
    }
    async getById(id) {
        return await this.activityService.getById(id);
    }
    async list(params) {
        return await this.activityService.list(params);
    }
    async getActivityWithWorks(id) {
        return await this.activityService.getActivityWithWorks(id);
    }
    async getActivityContentByType(id) {
        return await this.activityService.getActivityContentByType(id);
    }
    async addWorksToActivity(activityId, works) {
        return await this.activityService.addWorksToActivity(activityId, works);
    }
    async setAwardedWorks(activityId, workIds) {
        return await this.activityService.setAwardedWorks(activityId, workIds);
    }
    async uploadSignature(data) {
        try {
            const base64Data = data.signatureData.replace(/^data:image\/\w+;base64,/, '');
            const buffer = Buffer.from(base64Data, 'base64');
            const timestamp = new Date().getTime();
            const fileName = `signatures/activity_${data.activityId}/user_${data.userId}_${timestamp}.png`;
            const uploadResult = await this.aliOssService.uploadBufferWithContentCheck(buffer, fileName, {
                'Content-Type': 'image/png',
                'Cache-Control': 'max-age=31536000'
            }, false);
            return {
                filePath: uploadResult.url,
                fileName: fileName,
                uploadTime: new Date(),
                userIp: data.userIp
            };
        }
        catch (error) {
            throw new Error(`签名上传失败: ${error.message}`);
        }
    }
    async submitRegistration(data) {
        const submitResult = await this.activitySubmitService.create({
            activityId: data.activityId,
            userId: data.userId,
            userName: data.userName,
            userPhone: data.userPhone,
            agreementAccepted: data.agreementAccepted,
            parentConsentAccepted: data.parentConsentAccepted,
            parentSignaturePath: data.parentSignaturePath,
            signatureTime: data.signatureTime,
            signatureIp: data.signatureIp,
            remark: data.remark
        });
        try {
            const activity = await this.activityService.findOne(data.activityId);
            let instructorName = '';
            let schoolName = '';
            try {
                const studentInfo = await this.userStudentService.findByUser(data.userId);
                console.log('学生信息：', studentInfo);
                if (studentInfo.schoolId) {
                    const schoolInfo = await this.userSchoolService.findOne(studentInfo.schoolId);
                    schoolName = schoolInfo.schoolName;
                }
                if (studentInfo.classId) {
                    const classInfo = await this.userClassService.findOne(studentInfo.classId);
                    console.log('班级信息：', classInfo);
                    if (classInfo && classInfo.teacherId) {
                        const teacherInfo = await this.userInfoService.findOne(classInfo.teacherId);
                        console.log('指导老师：', teacherInfo);
                        instructorName = teacherInfo.nickName || '';
                    }
                }
            }
            catch (userInfoError) {
                console.error('获取用户学校和班级信息失败:', userInfoError);
            }
            const existingTask = await this.activityEventsTaskService.findByUserAndActivity(data.userId, data.activityId);
            if (existingTask) {
                if (existingTask.status === 3) {
                    await this.activityEventsTaskService.updateStatusByUserAndActivity(data.userId, data.activityId, 1);
                    console.log(`用户 ${data.userId} 重新报名活动 "${activity.name}"，恢复赛事任务状态`);
                }
                else {
                    console.log(`用户 ${data.userId} 的赛事任务已存在，状态为 ${existingTask.status}`);
                }
            }
            else {
                await this.activityEventsTaskService.create({
                    userId: data.userId,
                    activityId: data.activityId,
                    eventName: activity.name,
                    startTime: activity.startTime,
                    endTime: activity.endTime,
                    instructorName: instructorName,
                    schoolName: schoolName,
                    contactPerson: '',
                    contactPhone: '',
                    creatorId: data.userId,
                    remark: ''
                });
                console.log(`用户 ${data.userId} 首次报名活动 "${activity.name}"，创建新的赛事任务`);
            }
        }
        catch (error) {
            console.error('创建赛事任务记录失败:', error);
        }
        return submitResult;
    }
    async checkUserRegistration(activityId, userId) {
        const submit = await this.activitySubmitService.findUserSubmitForActivityAllStatus(activityId, userId);
        const isSubmitted = submit && submit.status === 0;
        return {
            submitted: isSubmitted,
            submit: submit || undefined
        };
    }
    async cancelRegistration(activityId, userId) {
        const cancelResult = await this.activitySubmitService.cancelByUser(activityId, userId);
        try {
            const taskUpdateResult = await this.activityEventsTaskService.updateStatusByUserAndActivity(userId, activityId, 3);
            if (taskUpdateResult) {
                console.log(`已取消用户 ${userId} 在活动 ${activityId} 的赛事任务`);
            }
            else {
                console.log(`用户 ${userId} 在活动 ${activityId} 没有找到对应的赛事任务`);
            }
        }
        catch (error) {
            console.error('取消赛事任务失败:', error);
        }
        return cancelResult;
    }
    async getUserRegistrations(userId, query) {
        const page = query.page || 1;
        const size = query.size || 10;
        const submits = await this.activitySubmitService.findByUser(userId);
        const total = submits.length;
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const list = submits.slice(startIndex, endIndex);
        return {
            list,
            total,
            page,
            size,
            totalPages: Math.ceil(total / size)
        };
    }
    async getRegistrationStatistics(activityId) {
        return await this.activitySubmitService.getSubmitStatistics(activityId);
    }
};
exports.WebActivityService = WebActivityService;
exports.WebActivityService = WebActivityService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [activity_service_1.ActivityService,
        activity_submit_service_1.ActivitySubmitService,
        activity_events_task_service_1.ActivityEventsTaskService,
        user_student_service_1.UserStudentService,
        user_school_service_1.UserSchoolService,
        user_class_service_1.UserClassService,
        user_info_service_1.UserInfoService,
        ali_oss_service_1.AliOssService])
], WebActivityService);
//# sourceMappingURL=web_activity.service.js.map