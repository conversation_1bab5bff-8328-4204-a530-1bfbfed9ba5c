"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择要发布的课程\");\n                return;\n            }\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            // 构建发布数据\n            const publishData = {\n                title: values.title,\n                description: values.description,\n                videoDuration: values.videoDuration || 0,\n                status: 1,\n                contentConfig: {\n                    hasVideo: values.hasVideo ? 1 : 0,\n                    hasDocument: values.hasDocument ? 1 : 0,\n                    hasAudio: values.hasAudio ? 1 : 0,\n                    video: {\n                        url: values.videoUrl || \"\",\n                        name: values.videoName || \"\"\n                    }\n                }\n            };\n            console.log(\"\\uD83D\\uDCE4 发布课程数据:\", publishData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(selectedCourseForPublish, publishData);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                fetchCourseList();\n            } else {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布课程失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理系列选择\n    const handleSeriesSelect = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 选择系列ID:\", seriesId);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined); // 重置课程选择\n        // 获取系列详情和子课程\n        try {\n            var _res_data;\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.courseList)) {\n                console.log(\"✅ 获取系列子课程成功:\", res.data.courseList);\n                setPublishSeriesCourses(res.data.courseList);\n            } else {\n                console.error(\"❌ 获取系列子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列子课程失败\");\n                setPublishSeriesCourses([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列子课程失败，请重试\");\n            setPublishSeriesCourses([]);\n        }\n    };\n    // 处理课程选择\n    const handleCourseSelect = (courseId)=>{\n        const course = publishSeriesCourses.find((c)=>c.id === courseId);\n        if (!course) {\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"课程不存在\");\n            return;\n        }\n        setSelectedCourseForPublish(course);\n        console.log(\"\\uD83D\\uDCD6 选择课程:\", course);\n        setPublishStep(3);\n    };\n    // 上一步\n    const handlePrevStep = ()=>{\n        if (publishStep === 2) {\n            setPublishStep(1);\n            setSelectedSeriesForPublish(null);\n            setPublishSeriesCourses([]);\n        } else if (publishStep === 3) {\n            setPublishStep(2);\n            setSelectedCourseForPublish(null);\n        }\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        // 获取系列课程列表\n        const series = await fetchSeriesForPublish();\n        if (series.length > 0) {\n            setSeriesList(series);\n            setIsPublishCourseModalVisible(true);\n            setPublishStep(1);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 885,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 894,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 884,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 901,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 925,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 935,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 951,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 982,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1143,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1156,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1162,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1149,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1188,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1195,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1193,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1177,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1206,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1207,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1207,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1214,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1223,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1213,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1236,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1169,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1289,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1281,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1310,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1305,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1318,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1313,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1340,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1339,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1344,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1347,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1343,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1326,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1370,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1356,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1395,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1401,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1404,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1405,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1400,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1380,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1376,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1429,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1428,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1427,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1438,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1437,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1441,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1436,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1418,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1414,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1464,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1469,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1463,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1476,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1475,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1478,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1479,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1474,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1454,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1450,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1492,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1487,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1510,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1510,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1503,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1512,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1499,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1271,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1542,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1537,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1550,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1545,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1560,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1561,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1553,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1572,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1573,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1571,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1566,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1532,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1520,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1603,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1598,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1611,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1606,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1633,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1632,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1638,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1637,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1640,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1641,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1636,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1623,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1619,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1658,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1659,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1657,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1651,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1668,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1663,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1687,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1686,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1680,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1675,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1593,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1580,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1721,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1713,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1733,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1732,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1731,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1739,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1738,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1737,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1745,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1744,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1743,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1751,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1750,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1749,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1757,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1756,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1755,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1763,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1762,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1761,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1769,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1768,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1773,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1730,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1724,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1789,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1790,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1791,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1792,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1788,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1782,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1801,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1796,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1816,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1817,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1815,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1809,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1708,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1696,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1854,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1846,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1841,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1866,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1861,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1875,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1877,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1878,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1879,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1880,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1876,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1874,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1836,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1824,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1890,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            color: \"blue\",\n                            children: [\n                                \"步骤 \",\n                                publishStep,\n                                \"/3\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1891,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1889,\n                    columnNumber: 11\n                }, void 0),\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: [\n                    publishStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第一步：选择系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1906,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"请选择要发布课程的系列\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1907,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1905,\n                                columnNumber: 13\n                            }, undefined),\n                            publishLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1912,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"加载系列课程列表...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1913,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1911,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 max-h-96 overflow-y-auto\",\n                                children: seriesList.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-all\",\n                                        onClick: ()=>handleSeriesSelect(series.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-1\",\n                                                            children: series.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1925,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                            children: series.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1926,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: series.status === 1 ? \"green\" : \"orange\",\n                                                                    children: series.status === 1 ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1928,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"blue\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        series.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1931,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1927,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1924,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"选择\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1934,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1923,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1918,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1916,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end pt-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    onClick: resetPublishModal,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1944,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1943,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1904,\n                        columnNumber: 11\n                    }, undefined),\n                    publishStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第二步：选择课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1955,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            '从系列 \"',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: selectedSeriesForPublish === null || selectedSeriesForPublish === void 0 ? void 0 : selectedSeriesForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1957,\n                                                columnNumber: 22\n                                            }, undefined),\n                                            '\" 中选择要发布的课程'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1956,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1954,\n                                columnNumber: 13\n                            }, undefined),\n                            publishLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1963,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"加载课程列表...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1964,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1962,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 max-h-96 overflow-y-auto\",\n                                children: publishSeriesCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-all\",\n                                        onClick: ()=>handleCourseSelect(course.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-1\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1976,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                            children: course.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1977,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: course.status === 1 ? \"green\" : \"orange\",\n                                                                    children: course.status === 1 ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1979,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"blue\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        course.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1982,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                course.hasVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"purple\",\n                                                                    children: \"视频\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1983,\n                                                                    columnNumber: 47\n                                                                }, undefined),\n                                                                course.hasDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    color: \"cyan\",\n                                                                    children: \"文档\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                                    lineNumber: 1984,\n                                                                    columnNumber: 50\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1978,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1975,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"选择\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1987,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1974,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1969,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1967,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: handlePrevStep,\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1997,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: resetPublishModal,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2000,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1996,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1953,\n                        columnNumber: 11\n                    }, undefined),\n                    publishStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"第三步：确认发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2011,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"确认发布课程信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2012,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2010,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"系列课程：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2018,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-blue-600\",\n                                                children: selectedSeriesForPublish === null || selectedSeriesForPublish === void 0 ? void 0 : selectedSeriesForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2019,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2017,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"课程名称：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2022,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2023,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2021,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"课程描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2026,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2027,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2025,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"当前状态：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2030,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                color: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.status) === 1 ? \"green\" : \"orange\",\n                                                className: \"ml-2\",\n                                                children: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.status) === 1 ? \"已发布\" : \"草稿\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2031,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2029,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2016,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                form: publishCourseForm,\n                                layout: \"vertical\",\n                                onFinish: handlePublishCourse,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"title\",\n                                        label: \"课程标题\",\n                                        initialValue: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.title,\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"请输入课程标题\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            placeholder: \"请输入课程标题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2049,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2043,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"description\",\n                                        label: \"课程描述\",\n                                        initialValue: selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.description,\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"请输入课程描述\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                            placeholder: \"请输入课程描述\",\n                                            rows: 3,\n                                            maxLength: 500,\n                                            showCount: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2058,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2052,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                        name: \"videoDuration\",\n                                        label: \"视频时长（秒）\",\n                                        initialValue: (selectedCourseForPublish === null || selectedCourseForPublish === void 0 ? void 0 : selectedCourseForPublish.videoDuration) || 0,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            type: \"number\",\n                                            placeholder: \"请输入视频时长（秒）\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2071,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2066,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2038,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-blue-800 font-medium mb-2\",\n                                        children: \"发布说明：\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2076,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 发布后课程将在课程市场中公开显示\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2078,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '• 课程状态将更新为\"已发布\"'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2079,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 学员可以开始学习该课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2080,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• 发布后仍可以编辑课程内容\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2081,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2077,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2075,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        onClick: handlePrevStep,\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2086,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                onClick: resetPublishModal,\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2090,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                loading: publishLoading,\n                                                onClick: ()=>publishCourseForm.submit(),\n                                                children: \"确认发布\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 2093,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2089,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2085,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 2009,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1887,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"nxIoS2cSIG1W0YedKEqAnGYUmN0=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});