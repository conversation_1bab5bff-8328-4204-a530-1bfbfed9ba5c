import { Repository } from 'typeorm';
import { PaymentOrder } from '../../util/database/mysql/payment_order/entities/payment-order.entity';
import { LockManager } from '../lock/lock.manager';
export declare class PaymentNotifyHandler {
    private readonly paymentOrderRepository;
    private readonly lockManager;
    private readonly logger;
    constructor(paymentOrderRepository: Repository<PaymentOrder>, lockManager: LockManager);
    handlePaymentSuccess(orderNo: string, paymentId: string, notifyData: any): Promise<boolean>;
    private processBusinessLogic;
}
