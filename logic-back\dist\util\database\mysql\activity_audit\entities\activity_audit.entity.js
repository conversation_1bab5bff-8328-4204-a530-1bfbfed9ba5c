"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityAudit = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let ActivityAudit = class ActivityAudit {
    id;
    activityId;
    auditorId;
    auditorName;
    result;
    reason;
    beforeStatus;
    afterStatus;
    createTime;
    updateTime;
    operationIp;
    deviceInfo;
    isDelete;
};
exports.ActivityAudit = ActivityAudit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], ActivityAudit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动ID', name: 'activity_id' }),
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    __metadata("design:type", Number)
], ActivityAudit.prototype, "activityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核人ID', name: 'auditor_id' }),
    (0, swagger_1.ApiProperty)({ description: '审核人ID' }),
    __metadata("design:type", Number)
], ActivityAudit.prototype, "auditorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核人姓名', name: 'auditor_name' }),
    (0, swagger_1.ApiProperty)({ description: '审核人姓名' }),
    __metadata("design:type", String)
], ActivityAudit.prototype, "auditorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核结果：1-通过 2-拒绝', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '审核结果：1-通过 2-拒绝', default: 0 }),
    __metadata("design:type", Number)
], ActivityAudit.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核意见', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '审核意见', required: false }),
    __metadata("design:type", String)
], ActivityAudit.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核前状态', name: 'before_status' }),
    (0, swagger_1.ApiProperty)({ description: '审核前状态' }),
    __metadata("design:type", Number)
], ActivityAudit.prototype, "beforeStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核后状态', name: 'after_status' }),
    (0, swagger_1.ApiProperty)({ description: '审核后状态' }),
    __metadata("design:type", Number)
], ActivityAudit.prototype, "afterStatus", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '审核时间', name: 'create_time' }),
    (0, swagger_1.ApiProperty)({ description: '审核时间' }),
    __metadata("design:type", Date)
], ActivityAudit.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间', name: 'update_time' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ActivityAudit.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '操作IP', nullable: true, name: 'operation_ip' }),
    (0, swagger_1.ApiProperty)({ description: '操作IP', required: false }),
    __metadata("design:type", String)
], ActivityAudit.prototype, "operationIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '设备信息', type: 'text', nullable: true, name: 'device_info' }),
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    __metadata("design:type", String)
], ActivityAudit.prototype, "deviceInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否删除', default: false, name: 'is_delete' }),
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false }),
    __metadata("design:type", Boolean)
], ActivityAudit.prototype, "isDelete", void 0);
exports.ActivityAudit = ActivityAudit = __decorate([
    (0, typeorm_1.Entity)('activity_audit')
], ActivityAudit);
//# sourceMappingURL=activity_audit.entity.js.map