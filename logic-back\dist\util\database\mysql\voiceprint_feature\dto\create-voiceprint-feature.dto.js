"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVoiceprintFeatureDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateVoiceprintFeatureDto {
    groupId;
    featureId;
    userId;
    name;
}
exports.CreateVoiceprintFeatureDto = CreateVoiceprintFeatureDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '声纹特征库ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '声纹特征库ID不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateVoiceprintFeatureDto.prototype, "groupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '特征ID不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateVoiceprintFeatureDto.prototype, "featureId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateVoiceprintFeatureDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特征名称' }),
    (0, class_validator_1.IsNotEmpty)({ message: '特征名称不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateVoiceprintFeatureDto.prototype, "name", void 0);
//# sourceMappingURL=create-voiceprint-feature.dto.js.map