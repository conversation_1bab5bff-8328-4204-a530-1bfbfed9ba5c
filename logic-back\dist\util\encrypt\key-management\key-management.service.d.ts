import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../database/redis/redis.service';
export interface KeyInfo {
    id: string;
    key: Buffer;
    iv?: Buffer;
    algorithm: string;
    createdAt: number;
    expiresAt?: number;
    isActive: boolean;
}
export interface RsaKeyPair {
    privateKey: string;
    publicKey: string;
    createdAt: number;
}
export interface RsaKeyPairVersioned {
    id: string;
    privateKey: string;
    publicKey: string;
    createdAt: number;
    expiresAt: number;
    status: 'active' | 'deprecated' | 'expired';
    needsUpdate?: boolean;
}
export declare class KeyManagementService implements OnModuleInit {
    private readonly configService;
    private readonly redisService;
    private readonly logger;
    private keys;
    private currentKeyId;
    private rsaKeyPairs;
    private activeRsaKeyId;
    private rsaKeyPair;
    private rotationInterval;
    private algorithm;
    private useRedis;
    private redisKeyPrefix;
    private keyRetentionPeriod;
    constructor(configService: ConfigService, redisService: RedisService);
    onModuleInit(): Promise<void>;
    private loadOptions;
    private initializeRsaKeyPairs;
    private setNewestKeyAsActive;
    private validateActiveRsaKeyPairWithoutRegeneration;
    private hasActiveRsaKey;
    private updateLegacyRsaKeyPair;
    private loadRsaKeyPairsFromRedis;
    private loadRsaKeyPairsFromConfig;
    private generateNewRsaKeyPair;
    private saveRsaKeyPairToRedis;
    private getKeyFingerprint;
    getPublicKeyWithVersion(): {
        keyId: string;
        publicKey: string;
    };
    getRsaKeyPair(): RsaKeyPair;
    getRsaKeyPairById(keyId: string): {
        keyPair: RsaKeyPairVersioned;
        needsUpdate: boolean;
    };
    decryptWithRsaPrivateKeyById(encryptedData: string, keyId: string): {
        data: Buffer;
        needsUpdate: boolean;
    };
    decryptWithRsaPrivateKey(encryptedData: string): Buffer;
    encryptWithRsaPublicKey(data: Buffer | string): string;
    protectSessionKey(sessionKey: any): any;
    restoreSessionKey(protectedKey: any): any;
    private initializeKeys;
    private loadKeysFromRedis;
    private loadKeysFromConfig;
    private saveKeyToRedis;
    private rotateKey;
    private getKeyLength;
    private updateCurrentKeyId;
    private shouldRotateKey;
    getCurrentKey(): KeyInfo;
    getKeyById(keyId: string): KeyInfo;
    private handleKeyRotation;
    private cleanupExpiredKeys;
    encryptWithSystemKey(data: string): string;
    decryptWithSystemKey(encryptedData: string): string;
    getActiveKeyInfo(): {
        keyId: string;
        createdAt: Date;
        expiresAt: Date;
        fingerprint: string;
        algorithm: string;
        remainingDays: number;
    } | null;
    getAllKeysInfo(): Array<{
        keyId: string;
        status: string;
        createdAt: Date;
        expiresAt: Date;
        fingerprint: string;
        isActive: boolean;
    }>;
}
