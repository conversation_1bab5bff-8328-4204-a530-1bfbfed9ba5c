{"version": 3, "file": "logger.config.js", "sourceRoot": "", "sources": ["../../../src/common/logger/logger.config.ts"], "names": [], "mappings": ";;AA6DA,gDAqGC;AAlKD,mCAAmC;AACnC,6DAA6D;AAE7D,6BAA6B;AAC7B,yBAAyB;AAGzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;IAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7C,CAAC;AAGD,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CACtC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IACvB,MAAM,EAAE,yBAAyB;CAClC,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,EACrB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE;IAC/E,IAAI,UAAU,GAAG,GAAG,SAAS,KAAK,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC;IAEzD,IAAI,OAAO,EAAE,CAAC;QACZ,UAAU,IAAI,KAAK,OAAO,GAAG,CAAC;IAChC,CAAC;IAED,UAAU,IAAI,IAAI,OAAO,EAAE,CAAC;IAG5B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,UAAU,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3C,CAAC;IAGD,IAAI,KAAK,EAAE,CAAC;QACV,UAAU,IAAI,KAAK,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1C,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IACvB,MAAM,EAAE,cAAc;CACvB,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;IAC/D,IAAI,UAAU,GAAG,GAAG,SAAS,IAAI,KAAK,EAAE,CAAC;IAEzC,IAAI,OAAO,EAAE,CAAC;QACZ,UAAU,IAAI,KAAK,OAAO,GAAG,CAAC;IAChC,CAAC;IAED,UAAU,IAAI,IAAI,OAAO,EAAE,CAAC;IAE5B,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CACH,CAAC;AAEF,SAAgB,kBAAkB;IAChC,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAE/F,MAAM,UAAU,GAAwB,EAAE,CAAC;IAG3C,IAAI,aAAa,EAAE,CAAC;QAClB,UAAU,CAAC,IAAI,CACb,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,OAAO;SACf,CAAC,CACH,CAAC;IACJ,CAAC;IAGD,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,wBAAwB,CAAC;QACtD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC;QAChD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC;QAC/C,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,MAAM;KACd,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;QACnD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAEF,OAAO;QACL,UAAU;QACV,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE;YACX,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO;YACnD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;YAClD,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;SACnC;QACD,WAAW,EAAE,KAAK;QAElB,iBAAiB,EAAE;YACjB,IAAI,eAAe,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;gBACrD,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,SAAS;aAClB,CAAC;SACH;QAED,iBAAiB,EAAE;YACjB,IAAI,eAAe,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;gBACrD,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,SAAS;aAClB,CAAC;SACH;KACF,CAAC;AACJ,CAAC"}