"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilterModule = void 0;
const common_1 = require("@nestjs/common");
const http_exception_filter_1 = require("./http-exception.filter");
const core_1 = require("@nestjs/core");
let HttpExceptionFilterModule = class HttpExceptionFilterModule {
};
exports.HttpExceptionFilterModule = HttpExceptionFilterModule;
exports.HttpExceptionFilterModule = HttpExceptionFilterModule = __decorate([
    (0, common_1.Module)({
        providers: [
            {
                provide: core_1.APP_FILTER,
                useClass: http_exception_filter_1.HttpExceptionFilter,
            }
        ],
        exports: [],
    })
], HttpExceptionFilterModule);
//# sourceMappingURL=http-exception-filter.module.js.map