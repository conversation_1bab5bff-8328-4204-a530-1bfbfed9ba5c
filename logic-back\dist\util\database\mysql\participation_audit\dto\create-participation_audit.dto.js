"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateParticipationAuditDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateParticipationAuditDto {
    activityId;
    workId;
    activityWorkId;
    userId;
    auditorId;
    auditorName;
    result;
    reason;
    beforeStatus;
    afterStatus;
    operationIp;
    deviceInfo;
}
exports.CreateParticipationAuditDto = CreateParticipationAuditDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "activityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '作品ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "workId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动作品关联ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "activityWorkId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核人ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "auditorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核人姓名' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateParticipationAuditDto.prototype, "auditorName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核结果：1-通过 2-拒绝', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "result", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核意见', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateParticipationAuditDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核前状态' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "beforeStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核后状态' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateParticipationAuditDto.prototype, "afterStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作IP', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateParticipationAuditDto.prototype, "operationIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateParticipationAuditDto.prototype, "deviceInfo", void 0);
//# sourceMappingURL=create-participation_audit.dto.js.map