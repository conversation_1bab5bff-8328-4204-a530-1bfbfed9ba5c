import { LoggerService as NestLoggerService } from '@nestjs/common';
import { Logger } from 'winston';
export declare class LoggerService implements NestLoggerService {
    private readonly logger;
    constructor(logger: Logger);
    log(message: any, context?: string): void;
    error(message: any, trace?: string, context?: string): void;
    warn(message: any, context?: string): void;
    debug(message: any, context?: string): void;
    verbose(message: any, context?: string): void;
    logHttpRequest(req: any, res: any, responseTime: number): void;
    logDatabase(operation: string, table: string, data?: any, error?: any): void;
    logPayment(action: string, data: any, error?: any): void;
    logAuth(action: string, userId?: string, details?: any, error?: any): void;
    logBusiness(module: string, action: string, data?: any, error?: any): void;
    logStartup(message: string, details?: any): void;
    logPerformance(operation: string, duration: number, details?: any): void;
    logSecurity(event: string, details: any, severity?: 'low' | 'medium' | 'high'): void;
}
