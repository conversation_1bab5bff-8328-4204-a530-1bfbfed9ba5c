"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StudentSelfAssessmentSubmissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const student_self_assessment_submission_entity_1 = require("./entities/student_self_assessment_submission.entity");
const typeorm_2 = require("typeorm");
const task_self_assessment_item_entity_1 = require("../task_self_assessment_item/entities/task_self_assessment_item.entity");
const user_info_entity_1 = require("../user_info/entities/user_info.entity");
let StudentSelfAssessmentSubmissionService = class StudentSelfAssessmentSubmissionService {
    submissionRepository;
    itemRepository;
    userInfoRepository;
    entityManager;
    constructor(submissionRepository, itemRepository, userInfoRepository, entityManager) {
        this.submissionRepository = submissionRepository;
        this.itemRepository = itemRepository;
        this.userInfoRepository = userInfoRepository;
        this.entityManager = entityManager;
    }
    async createBulk(createBulkDto) {
        return this.entityManager.transaction(async (transactionalEntityManager) => {
            const { assignmentId, studentId, submissions } = createBulkDto;
            const existingSubmissions = await transactionalEntityManager.find(student_self_assessment_submission_entity_1.StudentSelfAssessmentSubmission, {
                where: {
                    assignmentId,
                    studentId,
                    assessmentItemId: (0, typeorm_2.In)(submissions.map(s => s.assessmentItemId))
                }
            });
            const existingSubmissionsMap = new Map();
            existingSubmissions.forEach(sub => existingSubmissionsMap.set(sub.assessmentItemId, sub));
            for (const sub of submissions) {
                const existing = existingSubmissionsMap.get(sub.assessmentItemId);
                if (existing) {
                    const oldScore = existing.score;
                    const newScore = sub.score;
                    if (oldScore !== newScore) {
                        await transactionalEntityManager.update(student_self_assessment_submission_entity_1.StudentSelfAssessmentSubmission, existing.id, { score: newScore });
                        await transactionalEntityManager.increment(task_self_assessment_item_entity_1.TaskSelfAssessmentItem, { id: sub.assessmentItemId }, 'scoreSum', newScore - oldScore);
                    }
                }
                else {
                    const newSubmission = transactionalEntityManager.create(student_self_assessment_submission_entity_1.StudentSelfAssessmentSubmission, {
                        assessmentItemId: sub.assessmentItemId,
                        score: sub.score,
                        assignmentId: assignmentId,
                        studentId: studentId,
                    });
                    await transactionalEntityManager.save(newSubmission);
                    await transactionalEntityManager.increment(task_self_assessment_item_entity_1.TaskSelfAssessmentItem, { id: sub.assessmentItemId }, 'ratedCount', 1);
                    await transactionalEntityManager.increment(task_self_assessment_item_entity_1.TaskSelfAssessmentItem, { id: sub.assessmentItemId }, 'scoreSum', sub.score);
                }
            }
            return { success: true, message: '自评提交成功' };
        });
    }
    create(createStudentSelfAssessmentSubmissionDto) {
        const newSubmission = this.submissionRepository.create(createStudentSelfAssessmentSubmissionDto);
        return this.submissionRepository.save(newSubmission);
    }
    findAll() {
        return this.submissionRepository.find();
    }
    findOne(id) {
        return this.submissionRepository.findOne({ where: { id } });
    }
    update(id, updateStudentSelfAssessmentSubmissionDto) {
        return this.submissionRepository.update(id, updateStudentSelfAssessmentSubmissionDto);
    }
    remove(id) {
        return this.submissionRepository.delete(id);
    }
    async findByAssignmentAndStudent(assignmentId, studentId) {
        return this.submissionRepository.find({
            where: {
                assignmentId,
                studentId,
            },
        });
    }
    async findByAssessmentItemIdWithStudentInfo(assessmentItemId) {
        const submissions = await this.submissionRepository.find({
            where: { assessmentItemId },
            order: { score: 'DESC' },
        });
        if (submissions.length === 0) {
            return [];
        }
        const studentIds = [...new Set(submissions.map(s => s.studentId))];
        const students = await this.userInfoRepository.find({
            where: { id: (0, typeorm_2.In)(studentIds) },
            select: ['id', 'nickName', 'avatarUrl'],
        });
        const studentsMap = new Map();
        students.forEach(s => studentsMap.set(s.id, { nickName: s.nickName, avatarUrl: s.avatarUrl }));
        return submissions.map(sub => ({
            score: sub.score,
            studentId: sub.studentId,
            studentName: studentsMap.get(sub.studentId)?.nickName || `学生${sub.studentId}`,
            avatarUrl: studentsMap.get(sub.studentId)?.avatarUrl,
        }));
    }
};
exports.StudentSelfAssessmentSubmissionService = StudentSelfAssessmentSubmissionService;
exports.StudentSelfAssessmentSubmissionService = StudentSelfAssessmentSubmissionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(student_self_assessment_submission_entity_1.StudentSelfAssessmentSubmission)),
    __param(1, (0, typeorm_1.InjectRepository)(task_self_assessment_item_entity_1.TaskSelfAssessmentItem)),
    __param(2, (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfo)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.EntityManager])
], StudentSelfAssessmentSubmissionService);
//# sourceMappingURL=student_self_assessment_submission.service.js.map