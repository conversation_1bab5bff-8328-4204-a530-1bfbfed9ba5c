import { AlipayStrategy } from '../strategies/alipay.strategy';
import { WechatPayStrategy } from '../strategies/wechat-pay.strategy';
import { PaymentRefundService } from '../../util/database/mysql/payment_refund/payment-refund.service';
import { PaymentLogService } from '../../util/database/mysql/payment_log/payment-log.service';
import { LockManager } from '../lock/lock.manager';
import { NotifyService } from '../notification/notify.service';
export declare class RefundNotifyHandler {
    private readonly alipayStrategy;
    private readonly wechatPayStrategy;
    private readonly paymentRefundService;
    private readonly paymentLogService;
    private readonly lockManager;
    private readonly notifyService;
    private readonly logger;
    constructor(alipayStrategy: AlipayStrategy, wechatPayStrategy: WechatPayStrategy, paymentRefundService: PaymentRefundService, paymentLogService: PaymentLogService, lockManager: LockManager, notifyService: NotifyService);
    handleAlipayRefundNotify(data: any): Promise<string>;
    handleWechatRefundNotify(data: any): Promise<string>;
}
