export declare class CreateCourseDto {
    seriesId: number;
    title: string;
    description?: string;
    coverImage?: string;
    hasVideo?: number;
    hasDocument?: number;
    hasAudio?: number;
    videoDuration?: number;
    contentConfig?: Record<string, any>;
    teachingInfo?: any[];
    additionalResources?: any[];
    orderIndex?: number;
}
export declare class CourseResponseDto {
    id: number;
    seriesId: number;
    title: string;
    description: string;
    coverImage: string;
    hasVideo: number;
    hasDocument: number;
    hasAudio: number;
    videoDuration: number;
    contentConfig: Record<string, any>;
    teachingInfo: any[];
    additionalResources: any[];
    orderIndex: number;
    status: number;
    creatorId: number;
    createdAt: Date;
    updatedAt: Date;
}
