"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityWorkModule = void 0;
const common_1 = require("@nestjs/common");
const web_activity_work_controller_1 = require("./web_activity_work.controller");
const web_activity_work_service_1 = require("./web_activity_work.service");
const activity_work_module_1 = require("../../util/database/mysql/activity_work/activity_work.module");
const http_response_result_module_1 = require("../http_response_result/http_response_result.module");
let WebActivityWorkModule = class WebActivityWorkModule {
};
exports.WebActivityWorkModule = WebActivityWorkModule;
exports.WebActivityWorkModule = WebActivityWorkModule = __decorate([
    (0, common_1.Module)({
        imports: [
            activity_work_module_1.ActivityWorkModule,
            http_response_result_module_1.HttpResponseResultModule,
        ],
        controllers: [web_activity_work_controller_1.WebActivityWorkController],
        providers: [web_activity_work_service_1.WebActivityWorkService],
        exports: [web_activity_work_service_1.WebActivityWorkService],
    })
], WebActivityWorkModule);
//# sourceMappingURL=web_activity_work.module.js.map