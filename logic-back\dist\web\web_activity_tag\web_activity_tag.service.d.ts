import { ActivityTagService } from 'src/util/database/mysql/activity_tag/activity_tag.service';
export declare class WebActivityTagService {
    private readonly activityTagService;
    constructor(activityTagService: ActivityTagService);
    addActivityTags(activityId: number, tagIds: number[]): Promise<boolean>;
    updateActivityTags(activityId: number, tagIds: number[]): Promise<boolean>;
    deleteActivityTags(activityId: number): Promise<boolean>;
    getActivityTagIds(activityId: number): Promise<number[]>;
    getTagActivityIds(tagId: number): Promise<number[]>;
}
