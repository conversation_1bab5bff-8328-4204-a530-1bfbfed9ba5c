{"version": 3, "file": "user_role_template_task.service.js", "sourceRoot": "", "sources": ["../../../src/web/user_role/user_role_template_task.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAkE;AAClE,6CAAmD;AACnD,qCAAqC;AACrC,kJAAoI;AACpI,+KAAgK;AAChK,mKAAoJ;AACpJ,wFAA4E;AAC5E,qGAAyF;AAOlF,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAK5B;IAGA;IAGA;IAGA;IAGA;IAhBO,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAEvE,YAEU,kBAAsD,EAGtD,6BAA0E,EAG1E,yBAAkE,EAGlE,eAAkC,EAGlC,mBAA0C;QAZ1C,uBAAkB,GAAlB,kBAAkB,CAAoC;QAGtD,kCAA6B,GAA7B,6BAA6B,CAA6C;QAG1E,8BAAyB,GAAzB,yBAAyB,CAAyC;QAGlE,oBAAe,GAAf,eAAe,CAAmB;QAGlC,wBAAmB,GAAnB,mBAAmB,CAAuB;IACjD,CAAC;IAKJ,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAGnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC/E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC,CAAC;YACtG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;YAC9F,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAGjF,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAGjC,MAAM,IAAI,CAAC,uCAAuC,EAAE,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAGjC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YAGnF,MAAM,MAAM,GAAG;gBACb,2BAA2B;gBAC3B,oCAAoC;gBACpC,gCAAgC;gBAChC,QAAQ;gBACR,YAAY;aACb,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;gBAE1D,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,gBAAgB,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YAGD,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,aAAa,MAAM,CAAC,CAAC;gBAEnE,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;gBAClF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,wBAAwB,MAAM,CAAC,CAAC;gBAEvF,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;gBAC1E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,oBAAoB,MAAM,CAAC,CAAC;gBAE/E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;gBACtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,UAAU,MAAM,CAAC,CAAC;gBAE/C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,cAAc,MAAM,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAClD,CAAC;YAGD,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAE9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAMO,KAAK,CAAC,uCAAuC;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAG/B,IAAI,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC1D,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE;aACtB,CAAC,CAAC;YAGH,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBACjC,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;oBACnD,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC,CAAC;oBACV,YAAY,EAAE,WAAW;oBACzB,mBAAmB,EAAE,yBAAyB;oBAC9C,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,IAAI;oBAChB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAGtE,MAAM,2BAA2B,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC;gBAChF,KAAK,EAAE,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,EAAE;aAC1C,CAAC,CAAC;YAEH,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;gBACxE,KAAK,EAAE,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,EAAE;aAC1C,CAAC,CAAC;YAGH,MAAM,cAAc,GAAG;gBACrB,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,0BAA0B,EAAE;gBACpE,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,0BAA0B,EAAE;aACrE,CAAC;YAGF,KAAK,MAAM,UAAU,IAAI,uBAAuB,EAAE,CAAC;gBACjD,MAAM,WAAW,GAAG,sBAAsB,CAAC,MAAM,CAAC,IAAI,CACpD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CAC9F,CAAC;gBAEF,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CACzC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CAC9F,CAAC;gBAEF,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrC,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;oBAC3D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;gBACjF,CAAC;YACH,CAAC;YAGD,KAAK,MAAM,UAAU,IAAI,2BAA2B,EAAE,CAAC;gBACrD,MAAM,eAAe,GAAG,sBAAsB,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC3F,MAAM,mBAAmB,GAAG,UAAU,CAAC,WAAW,KAAK,cAAc,CAAC;gBAEtE,IAAI,CAAC,eAAe,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7C,MAAM,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;oBAC/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YAGD,KAAK,MAAM,GAAG,IAAI,sBAAsB,CAAC,UAAU,EAAE,CAAC;gBACpD,MAAM,kBAAkB,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,CAAC;gBAExF,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAExB,MAAM,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC;wBAC5C,UAAU,EAAE,eAAe,CAAC,EAAE;wBAC9B,WAAW,EAAE,GAAG;wBAChB,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC;gBACzC,CAAC;qBAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;oBAEzC,MAAM,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAC7C,EAAE,EAAE,EAAE,kBAAkB,CAAC,EAAE,EAAE,EAC7B,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,GAAG,EAAE,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC;YAGD,KAAK,MAAM,KAAK,IAAI,sBAAsB,CAAC,MAAM,EAAE,CAAC;gBAClD,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,IAAI,CACrD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW,CACxE,CAAC;gBAEF,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAExB,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;wBACxC,UAAU,EAAE,eAAe,CAAC,EAAE;wBAC9B,WAAW,EAAE,KAAK,CAAC,WAAW;wBAC9B,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACzE,CAAC;qBAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;oBAEzC,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CACzC,EAAE,EAAE,EAAE,kBAAkB,CAAC,EAAE,EAAE,EAC7B,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;YAGD,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;gBACnC,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,IAAI,CACrD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW,CACxE,CAAC;gBAEF,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAExB,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;wBACxC,UAAU,EAAE,eAAe,CAAC,EAAE;wBAC9B,WAAW,EAAE,KAAK,CAAC,WAAW;wBAC9B,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3E,CAAC;qBAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;oBAEzC,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CACzC,EAAE,EAAE,EAAE,kBAAkB,CAAC,EAAE,EAAE,EAC7B,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,yBAAyB;QACrC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAEzC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;YACzD,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAG5D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACtC,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC,CAAC;YAGJ,MAAM,cAAc,GAAG;gBACrB,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,0BAA0B,EAAE;gBACpE,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,0BAA0B,EAAE;aACrE,CAAC;YAGF,MAAM,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;YAGlC,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC7F,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAGD,MAAM,oBAAoB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAG7E,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAG/E,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC5C,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,aAAa,CAAC,MAAM,SAAS,SAAS,CAAC,MAAM,OAAO,CAAC,CAAC;YAEhF,OAAO;gBACL,UAAU,EAAE,aAAa;gBACzB,MAAM,EAAE,SAAS;aAClB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAGlE,MAAM,eAAe,GAAG;gBACtB,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS;gBAC/C,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;gBACjD,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,iBAAiB;aAClD,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,kBAAkB,EAAE;gBACtD,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE;gBAC9C,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE;gBAC/C,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE;gBACnD,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,0BAA0B,EAAE;gBACpE,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,0BAA0B,EAAE;gBACpE,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,2BAA2B,EAAE;aACzE,CAAC;YAEF,OAAO;gBACL,UAAU,EAAE,eAAe;gBAC3B,MAAM,EAAE,WAAW;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAzVY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,wDAAsB,CAAC,CAAA;IAGxC,WAAA,IAAA,0BAAgB,EAAC,2EAA+B,CAAC,CAAA;IAGjD,WAAA,IAAA,0BAAgB,EAAC,mEAA2B,CAAC,CAAA;IAG7C,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;IAGvB,WAAA,IAAA,0BAAgB,EAAC,4BAAS,CAAC,CAAA;qCAXA,oBAAU;QAGC,oBAAU;QAGd,oBAAU;QAGpB,oBAAU;QAGN,oBAAU;GAjB9B,2BAA2B,CAyVvC"}