import { XunfeiVoiceprintRecognitionService } from './xunfei_voiceprint_recognition.service';
declare class CreateGroupDto {
    groupId: string;
    groupName: string;
    groupInfo: string;
}
declare class CreateFeatureDto {
    groupId: string;
    featureId: string;
    featureInfo: string;
    audioBase64: string;
}
declare class CompareFeatureDto {
    groupId: string;
    dstFeatureId: string;
    audioBase64: string;
}
declare class SearchFeatureDto {
    groupId: string;
    audioBase64: string;
    topK?: number;
}
declare class GroupIdDto {
    groupId: string;
}
declare class DeleteFeatureDto {
    groupId: string;
    featureId: string;
}
declare class UpdateFeatureDto extends CreateFeatureDto {
}
export declare class XunfeiVoiceprintRecognitionController {
    private readonly xunfeiVoiceprintRecognitionService;
    constructor(xunfeiVoiceprintRecognitionService: XunfeiVoiceprintRecognitionService);
    createGroup(body: CreateGroupDto): Promise<any>;
    createFeature(body: CreateFeatureDto): Promise<any>;
    compareFeature(body: CompareFeatureDto): Promise<any>;
    searchFeature(body: SearchFeatureDto): Promise<any>;
    queryFeatureList(body: GroupIdDto): Promise<any>;
    updateFeature(body: UpdateFeatureDto): Promise<any>;
    deleteFeature(body: DeleteFeatureDto): Promise<any>;
    deleteGroup(body: GroupIdDto): Promise<any>;
}
export {};
