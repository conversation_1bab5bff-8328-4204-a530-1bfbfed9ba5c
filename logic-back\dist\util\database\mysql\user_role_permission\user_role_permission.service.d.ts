import { Repository } from 'typeorm';
import { CreateUserRolePermissionDto } from './dto/create-user_role_permission.dto';
import { UpdateUserRolePermissionDto } from './dto/update-user_role_permission.dto';
import { UserRolePermission } from './entities/user_role_permission.entity';
export declare class UserRolePermissionService {
    private userRolePermissionRepository;
    constructor(userRolePermissionRepository: Repository<UserRolePermission>);
    create(createUserRolePermissionDto: CreateUserRolePermissionDto): Promise<UserRolePermission>;
    findAll(): Promise<UserRolePermission[]>;
    findOne(id: number): Promise<UserRolePermission>;
    findByRole(roleId: number): Promise<UserRolePermission[]>;
    findByPermission(permissionId: number): Promise<UserRolePermission[]>;
    findByRoleAndPermission(roleId: number, permissionId: number): Promise<UserRolePermission | null>;
    update(id: number, updateUserRolePermissionDto: UpdateUserRolePermissionDto): Promise<UserRolePermission>;
    remove(id: number): Promise<void>;
    removeByRoleAndPermission(roleId: number, permissionId: number): Promise<void>;
    batchAssignPermissions(roleId: number, permissionIds: number[]): Promise<UserRolePermission[]>;
    removeAllByRole(roleId: number): Promise<void>;
}
