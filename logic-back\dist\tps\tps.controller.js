"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TPSController = void 0;
const common_1 = require("@nestjs/common");
const tps_service_1 = require("./tps.service");
const swagger_1 = require("@nestjs/swagger");
let TPSController = class TPSController {
    tpsService;
    constructor(tpsService) {
        this.tpsService = tpsService;
    }
    async createTestStudents(classId, count = 50, schoolId = 2) {
        return await this.tpsService.createTestStudents(+classId, +count, +schoolId);
    }
    async deleteByPrefix(prefix) {
        return await this.tpsService.deleteByPrefix(prefix);
    }
    async deleteCompleteTestData(timestamp) {
        return await this.tpsService.deleteCompleteTestData(timestamp);
    }
    async createCompleteTestData(schoolId, teacherCount = 5, studentCount = 30) {
        return await this.tpsService.createCompleteTestData(+schoolId, +teacherCount, +studentCount);
    }
    async createTestTeachers(schoolId, count = 20) {
        return await this.tpsService.createTestTeachers(+schoolId, +count);
    }
    async exportTestDataToCsv(timestamp) {
        return await this.tpsService.exportTestDataToCsv(timestamp);
    }
    async assignSpecialPackageToAllStudents(body) {
        return await this.tpsService.assignSpecialPackageToAllStudents(body.packageId, body.schoolId, body.operatorId, body.remark, body.showInMessageCenter);
    }
};
exports.TPSController = TPSController;
__decorate([
    (0, common_1.Get)('create-test-students'),
    (0, swagger_1.ApiOperation)({ summary: '批量创建测试学生' }),
    (0, swagger_1.ApiQuery)({ name: 'classId', description: '班级ID', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'count', description: '创建数量（默认为50）', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'schoolId', description: '学校ID（默认为2，洛基小学）', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '创建成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '批量学生创建完成，创建了50/50个学生' },
                usersCreated: { type: 'number', example: 50 },
                studentsCreated: { type: 'number', example: 50 },
                userIds: {
                    type: 'array',
                    items: { type: 'number' },
                    example: [1001, 1002, 1003]
                },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            index: { type: 'number' },
                            error: { type: 'string' }
                        }
                    },
                    nullable: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '创建失败',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '批量创建学生失败' },
                error: { type: 'string', example: '班级不存在' },
                usersCreated: { type: 'number', example: 0 },
                studentsCreated: { type: 'number', example: 0 },
                errors: { type: 'array', items: { type: 'object' } }
            }
        }
    }),
    __param(0, (0, common_1.Query)('classId')),
    __param(1, (0, common_1.Query)('count')),
    __param(2, (0, common_1.Query)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "createTestStudents", null);
__decorate([
    (0, common_1.Get)('delete-by-prefix'),
    (0, swagger_1.ApiOperation)({ summary: '通过时间戳前缀删除测试学生' }),
    (0, swagger_1.ApiQuery)({ name: 'prefix', description: '前缀(时间戳)', required: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '删除成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '批量学生删除完成，成功删除: 50/50' },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            userId: { type: 'number' },
                            error: { type: 'string' }
                        }
                    },
                    nullable: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '删除失败',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '前缀太短，至少需要2个字符' },
                error: { type: 'string', nullable: true }
            }
        }
    }),
    __param(0, (0, common_1.Query)('prefix')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "deleteByPrefix", null);
__decorate([
    (0, common_1.Get)('delete-complete-test-data'),
    (0, swagger_1.ApiOperation)({ summary: '删除完整的测试数据（教师、班级、学生）' }),
    (0, swagger_1.ApiQuery)({ name: 'timestamp', description: '时间戳，用于识别要删除的测试数据批次', required: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '删除成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '完整测试数据删除完成，删除了155个用户，5个班级' },
                data: {
                    type: 'object',
                    properties: {
                        deletedUsers: { type: 'number', example: 155 },
                        deletedClasses: { type: 'number', example: 5 },
                        totalFound: { type: 'number', example: 160 }
                    }
                },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            type: { type: 'string', enum: ['user', 'class'] },
                            id: { type: 'number' },
                            name: { type: 'string' },
                            error: { type: 'string' }
                        }
                    },
                    nullable: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '删除失败',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '时间戳太短，至少需要4个字符' },
                error: { type: 'string', nullable: true }
            }
        }
    }),
    __param(0, (0, common_1.Query)('timestamp')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "deleteCompleteTestData", null);
__decorate([
    (0, common_1.Get)('create-complete-test-data'),
    (0, swagger_1.ApiOperation)({ summary: '创建完整的教师-班级-学生测试数据' }),
    (0, swagger_1.ApiQuery)({ name: 'schoolId', description: '学校ID', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'teacherCount', description: '教师数量（默认为5）', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'studentCount', description: '每个班级的学生数量（默认为30）', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '创建成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '完整测试数据创建完成，创建了5个教师，5个班级，150个学生' },
                data: {
                    type: 'object',
                    properties: {
                        teachersCreated: { type: 'number', example: 5 },
                        classesCreated: { type: 'number', example: 5 },
                        studentsCreated: { type: 'number', example: 150 },
                        userIds: {
                            type: 'array',
                            items: { type: 'number' },
                            example: [1001, 1002, 1003]
                        },
                        details: { type: 'object', description: '详细创建信息' }
                    }
                },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            type: { type: 'string', enum: ['teacher', 'student'] },
                            index: { type: 'number' },
                            error: { type: 'string' }
                        }
                    },
                    nullable: true
                }
            }
        }
    }),
    __param(0, (0, common_1.Query)('schoolId')),
    __param(1, (0, common_1.Query)('teacherCount')),
    __param(2, (0, common_1.Query)('studentCount')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "createCompleteTestData", null);
__decorate([
    (0, common_1.Get)('create-test-teachers'),
    (0, swagger_1.ApiOperation)({ summary: '批量创建测试教师（含学校关联）' }),
    (0, swagger_1.ApiQuery)({ name: 'schoolId', description: '学校ID', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'count', description: '创建数量（默认为20）', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '创建成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '批量教师创建完成，创建了20/20个教师' },
                usersCreated: { type: 'number', example: 20 },
                userIds: {
                    type: 'array',
                    items: { type: 'number' },
                    example: [1001, 1002, 1003]
                },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            index: { type: 'number' },
                            error: { type: 'string' }
                        }
                    },
                    nullable: true
                }
            }
        }
    }),
    __param(0, (0, common_1.Query)('schoolId')),
    __param(1, (0, common_1.Query)('count')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "createTestTeachers", null);
__decorate([
    (0, common_1.Get)('export-test-data-csv'),
    (0, swagger_1.ApiOperation)({ summary: '导出测试数据为CSV格式（防止科学计数法）' }),
    (0, swagger_1.ApiQuery)({ name: 'timestamp', description: '时间戳，用于筛选特定批次的数据', required: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '导出成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '成功导出155条测试数据' },
                data: {
                    type: 'object',
                    properties: {
                        csvContent: { type: 'string', description: 'CSV文件内容' },
                        filename: { type: 'string', example: 'test_data_12345678.csv' },
                        count: { type: 'number', example: 155 }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '导出失败',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '时间戳太短，至少需要4个字符' },
                error: { type: 'string', nullable: true }
            }
        }
    }),
    __param(0, (0, common_1.Query)('timestamp')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "exportTestDataToCsv", null);
__decorate([
    (0, common_1.Post)('assign-special-package-to-all-students'),
    (0, swagger_1.ApiOperation)({ summary: '为全校学生分配特殊套餐' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['packageId', 'schoolId', 'operatorId'],
            properties: {
                packageId: { type: 'number', description: '套餐ID' },
                schoolId: { type: 'number', description: '学校ID' },
                operatorId: { type: 'number', description: '操作员ID' },
                remark: { type: 'string', description: '备注信息', nullable: true },
                showInMessageCenter: { type: 'boolean', description: '是否在消息中心显示', default: true }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分配成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '成功为全校150名学生分配特殊套餐' },
                data: {
                    type: 'object',
                    properties: {
                        packageInfo: {
                            type: 'object',
                            properties: {
                                id: { type: 'number', example: 1 },
                                name: { type: 'string', example: '特殊套餐' },
                                points: { type: 'number', example: 100 },
                                validityDays: { type: 'number', example: 30 }
                            }
                        },
                        assignedCount: { type: 'number', example: 150 },
                        totalStudents: { type: 'number', example: 150 },
                        skippedCount: { type: 'number', example: 0 },
                        errors: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    userId: { type: 'number' },
                                    studentNumber: { type: 'string' },
                                    error: { type: 'string' }
                                }
                            },
                            nullable: true
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '分配失败',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '套餐不存在或已下架' },
                error: { type: 'string', nullable: true }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TPSController.prototype, "assignSpecialPackageToAllStudents", null);
exports.TPSController = TPSController = __decorate([
    (0, swagger_1.ApiTags)('TPS工具'),
    (0, common_1.Controller)('tps'),
    __metadata("design:paramtypes", [tps_service_1.TPSService])
], TPSController);
//# sourceMappingURL=tps.controller.js.map