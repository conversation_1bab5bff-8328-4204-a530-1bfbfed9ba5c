{"version": 3, "file": "submit-events-task.dto.js", "sourceRoot": "", "sources": ["../../../../../../src/util/database/mysql/activity_events_task/dto/submit-events-task.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAA6E;AAE7E,MAAa,mBAAmB;IAI9B,MAAM,CAAU;IAKhB,QAAQ,CAAU;IAKlB,eAAe,CAAU;IAKzB,cAAc,CAAU;IAKxB,UAAU,CAAU;IAKpB,aAAa,CAAU;IAKvB,YAAY,CAAU;IAKtB,QAAQ,CAAU;IAKlB,QAAQ,CAAU;IAKlB,gBAAgB,CAAU;IAK1B,YAAY,CAAU;IAKtB,eAAe,CAAU;IAKzB,gBAAgB,CAAU;IAK1B,oBAAoB,CAAU;IAK9B,MAAM,CAAU;IAIhB,UAAU,CAAW;CACtB;AA/ED,kDA+EC;AA3EC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;mDACvB;AAKhB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACvD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;;qDACpB;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;4DACX;AAKzB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;;2DACd;AAKxB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;uDAChB;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;0DACd;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;yDACd;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;qDAClB;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;qDACjB;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;6DACV;AAK1B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;yDACd;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACvD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;;4DACb;AAKzB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;6DACV;AAK1B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACzD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;;iEACV;AAK9B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;mDACpB;AAIhB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IACvD,IAAA,4BAAU,GAAE;;uDACQ"}