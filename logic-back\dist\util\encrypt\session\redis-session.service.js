"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RedisSessionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisSessionService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../database/redis/redis.service");
const encryption_service_1 = require("../encryption.service");
let RedisSessionService = RedisSessionService_1 = class RedisSessionService {
    redisService;
    logger = new common_1.Logger(RedisSessionService_1.name);
    SESSION_KEY_PREFIX = 'encryption:session:';
    STANDARD_SESSION_TTL = 30 * 60;
    SECURE_SESSION_TTL = 5 * 60;
    constructor(redisService) {
        this.redisService = redisService;
        this.logger.log('Redis会话存储服务已初始化');
    }
    getSessionKey(sessionId) {
        return `${this.SESSION_KEY_PREFIX}${sessionId}`;
    }
    async storeSession(sessionId, sessionData, sessionType = encryption_service_1.SessionType.STANDARD) {
        try {
            const key = this.getSessionKey(sessionId);
            const ttl = sessionType === encryption_service_1.SessionType.SECURE
                ? this.SECURE_SESSION_TTL
                : this.STANDARD_SESSION_TTL;
            const serializedData = JSON.stringify({
                ...sessionData,
                sessionType,
                createdAt: Date.now()
            });
            await this.redisService.getClient().set(key, serializedData, 'EX', ttl);
            this.logger.log(`会话 ${sessionId} 已存储到Redis (类型: ${sessionType}, TTL: ${ttl}秒)`);
            return true;
        }
        catch (error) {
            this.logger.error(`存储会话 ${sessionId} 失败: ${error.message}`, error.stack);
            return false;
        }
    }
    async getSession(sessionId) {
        try {
            const key = this.getSessionKey(sessionId);
            const data = await this.redisService.getClient().get(key);
            if (!data) {
                this.logger.warn(`会话 ${sessionId} 不存在或已过期`);
                return null;
            }
            return JSON.parse(data);
        }
        catch (error) {
            this.logger.error(`获取会话 ${sessionId} 失败: ${error.message}`, error.stack);
            return null;
        }
    }
    async refreshSession(sessionId, sessionType = encryption_service_1.SessionType.STANDARD) {
        try {
            const key = this.getSessionKey(sessionId);
            const ttl = sessionType === encryption_service_1.SessionType.SECURE
                ? this.SECURE_SESSION_TTL
                : this.STANDARD_SESSION_TTL;
            const data = await this.redisService.getClient().get(key);
            if (!data) {
                this.logger.warn(`无法刷新会话 ${sessionId}: 会话不存在或已过期`);
                return false;
            }
            const currentTTL = await this.redisService.getClient().ttl(key);
            const currentTime = new Date();
            const futureExpireTime = new Date(currentTime.getTime() + ttl * 1000);
            this.logger.log(`会话 ${sessionId} 当前TTL: ${currentTTL}秒，剩余有效期: ${Math.floor(currentTTL / 60)}分${currentTTL % 60}秒`);
            await this.redisService.getClient().expire(key, ttl);
            this.logger.log(`会话 ${sessionId} TTL已刷新 (${ttl}秒)，新过期时间: ${futureExpireTime.toISOString()}`);
            return true;
        }
        catch (error) {
            this.logger.error(`刷新会话 ${sessionId} 失败: ${error.message}`, error.stack);
            return false;
        }
    }
    async removeSession(sessionId) {
        try {
            const key = this.getSessionKey(sessionId);
            await this.redisService.getClient().del(key);
            this.logger.log(`会话 ${sessionId} 已从Redis中删除`);
            return true;
        }
        catch (error) {
            this.logger.error(`删除会话 ${sessionId} 失败: ${error.message}`, error.stack);
            return false;
        }
    }
    async hasSession(sessionId) {
        try {
            const key = this.getSessionKey(sessionId);
            const exists = await this.redisService.getClient().exists(key);
            return exists === 1;
        }
        catch (error) {
            this.logger.error(`检查会话 ${sessionId} 失败: ${error.message}`, error.stack);
            return false;
        }
    }
    async getSessionCount() {
        try {
            const keys = await this.redisService.getClient().keys(`${this.SESSION_KEY_PREFIX}*`);
            return keys.length;
        }
        catch (error) {
            this.logger.error(`获取会话数量失败: ${error.message}`, error.stack);
            return 0;
        }
    }
};
exports.RedisSessionService = RedisSessionService;
exports.RedisSessionService = RedisSessionService = RedisSessionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], RedisSessionService);
//# sourceMappingURL=redis-session.service.js.map