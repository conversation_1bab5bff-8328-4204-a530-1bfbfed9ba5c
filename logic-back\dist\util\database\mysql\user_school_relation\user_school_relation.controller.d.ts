import { UserSchoolRelationService } from './user_school_relation.service';
import { CreateUserSchoolRelationDto } from './dto/create-user_school_relation.dto';
import { UpdateUserSchoolRelationDto } from './dto/update-user_school_relation.dto';
import { UserSchoolRelation } from './entities/user_school_relation.entity';
export declare class UserSchoolRelationController {
    private readonly relationService;
    constructor(relationService: UserSchoolRelationService);
    create(createDto: CreateUserSchoolRelationDto): Promise<UserSchoolRelation>;
    findAll(): Promise<UserSchoolRelation[]>;
    findByUser(userId: string): Promise<UserSchoolRelation[]>;
    findBySchool(schoolId: string): Promise<UserSchoolRelation[]>;
    findStudentsOfSchool(schoolId: string): Promise<UserSchoolRelation[]>;
    findTeachersOfSchool(schoolId: string): Promise<import("../user_info/entities/user_info.entity").UserInfo[]>;
    checkUserSchoolRelation(userId: string, schoolId: string): Promise<UserSchoolRelation | null>;
    findOne(id: string): Promise<UserSchoolRelation>;
    update(id: string, updateDto: UpdateUserSchoolRelationDto): Promise<UserSchoolRelation>;
    updateRoleType(id: string, roleType: string): Promise<UserSchoolRelation>;
    remove(id: string): Promise<void>;
    removeByUserAndSchool(userId: string, schoolId: string): Promise<void>;
    removeAllByUser(userId: string): Promise<void>;
    removeAllBySchool(schoolId: string): Promise<void>;
}
