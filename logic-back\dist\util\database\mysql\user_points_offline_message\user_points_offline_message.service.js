"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPointsOfflineMessageService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_points_offline_message_entity_1 = require("./entities/user_points_offline_message.entity");
let UserPointsOfflineMessageService = class UserPointsOfflineMessageService {
    userPointsOfflineMessageRepository;
    constructor(userPointsOfflineMessageRepository) {
        this.userPointsOfflineMessageRepository = userPointsOfflineMessageRepository;
    }
    async create(createUserPointsOfflineMessageDto) {
        const message = this.userPointsOfflineMessageRepository.create(createUserPointsOfflineMessageDto);
        return await this.userPointsOfflineMessageRepository.save(message);
    }
    async findAll() {
        return await this.userPointsOfflineMessageRepository.find({
            order: { createTime: 'DESC' }
        });
    }
    async findByUserId(userId) {
        return await this.userPointsOfflineMessageRepository.find({
            where: { userId },
            order: { createTime: 'DESC' }
        });
    }
    async findOne(id) {
        const message = await this.userPointsOfflineMessageRepository.findOne({ where: { id } });
        if (!message) {
            throw new common_1.NotFoundException(`积分消息ID为${id}的记录不存在`);
        }
        return message;
    }
    async update(id, updateUserPointsOfflineMessageDto) {
        const message = await this.findOne(id);
        Object.assign(message, updateUserPointsOfflineMessageDto);
        return await this.userPointsOfflineMessageRepository.save(message);
    }
    async remove(id) {
        const result = await this.userPointsOfflineMessageRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`积分消息ID为${id}的记录不存在`);
        }
    }
    async markAsSent(id) {
        const message = await this.findOne(id);
        message.status = 1;
        return await this.userPointsOfflineMessageRepository.save(message);
    }
    async findPendingMessages() {
        return await this.userPointsOfflineMessageRepository.find({
            where: { status: 0 },
            order: { createTime: 'ASC' }
        });
    }
    async getPendingMessage(userId) {
        return await this.userPointsOfflineMessageRepository.findOne({
            where: { userId, status: 0 },
        });
    }
};
exports.UserPointsOfflineMessageService = UserPointsOfflineMessageService;
exports.UserPointsOfflineMessageService = UserPointsOfflineMessageService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_points_offline_message_entity_1.UserPointsOfflineMessage)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserPointsOfflineMessageService);
//# sourceMappingURL=user_points_offline_message.service.js.map