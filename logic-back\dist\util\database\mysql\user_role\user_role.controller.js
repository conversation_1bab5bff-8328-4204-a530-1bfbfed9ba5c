"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRoleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_role_service_1 = require("./user_role.service");
const create_user_role_dto_1 = require("./dto/create-user_role.dto");
const update_user_role_dto_1 = require("./dto/update-user_role.dto");
const user_role_entity_1 = require("./entities/user_role.entity");
let UserRoleController = class UserRoleController {
    userRoleService;
    constructor(userRoleService) {
        this.userRoleService = userRoleService;
    }
    async create(createUserRoleDto) {
        return await this.userRoleService.create(createUserRoleDto);
    }
    async findAll() {
        return await this.userRoleService.findAll();
    }
    async findActive() {
        return await this.userRoleService.findActive();
    }
    async findDefault() {
        return await this.userRoleService.findDefault();
    }
    async findByCode(code) {
        return await this.userRoleService.findByCode(code);
    }
    async findOne(id) {
        return await this.userRoleService.findOne(+id);
    }
    async update(id, updateUserRoleDto) {
        return await this.userRoleService.update(+id, updateUserRoleDto);
    }
    async updateStatus(id, status) {
        return await this.userRoleService.updateStatus(+id, +status);
    }
    async setDefault(id) {
        return await this.userRoleService.setDefault(+id);
    }
    async remove(id) {
        await this.userRoleService.remove(+id);
    }
    async findList(params) {
        return await this.userRoleService.findList(params);
    }
};
exports.UserRoleController = UserRoleController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建用户角色' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: user_role_entity_1.UserRole }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '角色名称或编码已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_role_dto_1.CreateUserRoleDto]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有用户角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_role_entity_1.UserRole] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有启用状态的用户角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [user_role_entity_1.UserRole] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "findActive", null);
__decorate([
    (0, common_1.Get)('default'),
    (0, swagger_1.ApiOperation)({ summary: '获取默认角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_role_entity_1.UserRole }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "findDefault", null);
__decorate([
    (0, common_1.Get)('code/:code'),
    (0, swagger_1.ApiOperation)({ summary: '根据角色编码获取角色' }),
    (0, swagger_1.ApiParam)({ name: 'code', description: '角色编码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_role_entity_1.UserRole }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '角色不存在' }),
    __param(0, (0, common_1.Param)('code')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "findByCode", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取用户角色' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_role_entity_1.UserRole }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '角色不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户角色' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_role_entity_1.UserRole }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '角色不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '角色名称或编码已存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '系统角色不能修改为自定义角色' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_role_dto_1.UpdateUserRoleDto]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status/:status'),
    (0, swagger_1.ApiOperation)({ summary: '更新角色状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    (0, swagger_1.ApiParam)({ name: 'status', description: '状态：0-禁用，1-启用' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_role_entity_1.UserRole }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '角色不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '系统角色或默认角色不能禁用' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/set-default'),
    (0, swagger_1.ApiOperation)({ summary: '设置为默认角色' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置成功', type: user_role_entity_1.UserRole }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '角色不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '禁用状态的角色不能设为默认角色' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "setDefault", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '删除用户角色' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '角色不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '系统角色或默认角色不能删除' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('condition'),
    (0, swagger_1.ApiOperation)({ summary: '条件查询用户角色列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [user_role_entity_1.UserRole] }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserRoleController.prototype, "findList", null);
exports.UserRoleController = UserRoleController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/用户角色(user_role)'),
    (0, common_1.Controller)('user-role'),
    __metadata("design:paramtypes", [user_role_service_1.UserRoleService])
], UserRoleController);
//# sourceMappingURL=user_role.controller.js.map