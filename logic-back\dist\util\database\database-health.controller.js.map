{"version": 3, "file": "database-health.controller.js", "sourceRoot": "", "sources": ["../../../src/util/database/database-health.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAuD;AACvD,6CAAqE;AACrE,6EAAwE;AAIjE,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAEhB;IADnB,YACmB,wBAAkD;QAAlD,6BAAwB,GAAxB,wBAAwB,CAA0B;IAClE,CAAC;IAsBE,AAAN,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,CAAC;YAC7E,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE,MAAM;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAiBK,AAAN,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,2BAA2B,EAAE,CAAC;YACjF,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,QAAQ,MAAM,CAAC,mBAAmB,QAAQ;gBACnD,IAAI,EAAE,MAAM;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,CAAC;YAC7E,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,eAAe,GAAG,GAAG,CAAC;YAE/F,OAAO;gBACL,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBAC3B,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;gBAC1C,IAAI,EAAE;oBACJ,OAAO,EAAE,SAAS;oBAClB,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,eAAe,EAAE;oBACvE,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC;oBACrF,GAAG,MAAM;iBACV;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,UAAU,GAAG,KAAK,CAAC,OAAO;gBACnC,IAAI,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA5GY,4DAAwB;AAyB7B;IApBL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,YAAY;QACrB,WAAW,EAAE,iBAAiB;KAC/B,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE;gBACzD,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE;gBACzD,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE;gBACjE,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE;gBACzD,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE;gBACnD,WAAW,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE;aACvD;SACF;KACF,CAAC;IACD,IAAA,YAAG,EAAC,mBAAmB,CAAC;;;;mEAkBxB;AAiBK;IAfL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,UAAU;QACnB,WAAW,EAAE,4BAA4B;KAC1C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,UAAU;QACvB,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,mBAAmB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE;aAC/D;SACF;KACF,CAAC;IACD,IAAA,aAAI,EAAC,0BAA0B,CAAC;;;;sEAkBhC;AAOK;IALL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE,aAAa;KAC3B,CAAC;IACD,IAAA,YAAG,EAAC,QAAQ,CAAC;;;;2DAyBb;mCA3GU,wBAAwB;IAFpC,IAAA,iBAAO,EAAC,SAAS,CAAC;IAClB,IAAA,mBAAU,EAAC,wBAAwB,CAAC;qCAGU,qDAAwB;GAF1D,wBAAwB,CA4GpC"}