"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipationAuditController = void 0;
const common_1 = require("@nestjs/common");
const participation_audit_service_1 = require("./participation_audit.service");
const create_participation_audit_dto_1 = require("./dto/create-participation_audit.dto");
const update_participation_audit_dto_1 = require("./dto/update-participation_audit.dto");
const swagger_1 = require("@nestjs/swagger");
const participation_audit_entity_1 = require("./entities/participation_audit.entity");
let ParticipationAuditController = class ParticipationAuditController {
    participationAuditService;
    constructor(participationAuditService) {
        this.participationAuditService = participationAuditService;
    }
    create(createParticipationAuditDto) {
        return this.participationAuditService.create(createParticipationAuditDto);
    }
    findAll() {
        return this.participationAuditService.findAll();
    }
    findOne(id) {
        return this.participationAuditService.findOne(+id);
    }
    update(id, updateParticipationAuditDto) {
        return this.participationAuditService.update(+id, updateParticipationAuditDto);
    }
    remove(id) {
        return this.participationAuditService.remove(+id);
    }
};
exports.ParticipationAuditController = ParticipationAuditController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建审核记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: participation_audit_entity_1.ParticipationAudit }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_participation_audit_dto_1.CreateParticipationAuditDto]),
    __metadata("design:returntype", void 0)
], ParticipationAuditController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有审核记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [participation_audit_entity_1.ParticipationAudit] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ParticipationAuditController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取单个审核记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '审核记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: participation_audit_entity_1.ParticipationAudit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '审核记录未找到' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ParticipationAuditController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新审核记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '审核记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: participation_audit_entity_1.ParticipationAudit }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '审核记录未找到' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_participation_audit_dto_1.UpdateParticipationAuditDto]),
    __metadata("design:returntype", void 0)
], ParticipationAuditController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除审核记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '审核记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '审核记录未找到' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ParticipationAuditController.prototype, "remove", null);
exports.ParticipationAuditController = ParticipationAuditController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/参赛作品审核管理(participation_audit)'),
    (0, common_1.Controller)('participation-audit'),
    __metadata("design:paramtypes", [participation_audit_service_1.ParticipationAuditService])
], ParticipationAuditController);
//# sourceMappingURL=participation_audit.controller.js.map