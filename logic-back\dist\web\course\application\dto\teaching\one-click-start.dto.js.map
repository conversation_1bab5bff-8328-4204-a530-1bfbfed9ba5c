{"version": 3, "file": "one-click-start.dto.js", "sourceRoot": "", "sources": ["../../../../../../src/web/course/application/dto/teaching/one-click-start.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAyD;AACzD,yDAAyC;AAKzC,MAAa,gBAAgB;IAU3B,QAAQ,CAAS;IAWjB,OAAO,CAAS;CAGjB;AAxBD,4CAwBC;AAdC;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACnC,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC/B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAChC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;kDACF;AAWjB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACnC,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC/B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAChC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;iDACH;AAQlB,MAAa,eAAe;IAE1B,MAAM,CAAS;IAGf,QAAQ,CAAS;IAGjB,SAAS,CAAS;IAGlB,OAAO,CAAS;IAGhB,gBAAgB,CAAS;CAC1B;AAfD,0CAeC;AAbC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;+CACpC;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;iDAC5C;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;kDACpD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;gDACtD;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;yDAC3B;AAM3B,MAAa,mBAAmB;IAE9B,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,SAAS,CAAS;IAGlB,YAAY,CAAS;IAGrB,gBAAgB,CAAS;IAGzB,YAAY,CAAS;IAerB,YAAY,CAAoB;IAQhC,gBAAgB,CAAW;IAQ3B,eAAe,CAAW;CAC3B;AAjDD,kDAiDC;AA/CC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;uDAC9C;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;uDAC5C;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;sDAC3C;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;yDAC7B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;6DAC3B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;yDAC1C;AAerB;IAbC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,IAAI,EAAE,CAAC,eAAe,CAAC;QACvB,OAAO,EAAE;YACP;gBACE,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,aAAa;gBACvB,SAAS,EAAE,sBAAsB;gBACjC,OAAO,EAAE,sBAAsB;gBAC/B,gBAAgB,EAAE,EAAE;aACrB;SACF;KACF,CAAC;;yDAC8B;AAQhC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;KAChB,CAAC;;6DACyB;AAQ3B;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;KAChB,CAAC;;4DACwB;AAM5B,MAAa,oBAAoB;IAE/B,OAAO,CAAU;IAGjB,gBAAgB,CAAS;IAGzB,eAAe,CAAS;IAGxB,YAAY,CAAS;IAGrB,eAAe,CAAU;IAGzB,aAAa,CAAS;IAGtB,eAAe,CAAS;IAGxB,kBAAkB,CAAS;IAG3B,OAAO,CAAsB;CAC9B;AA3BD,oDA2BC;AAzBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;qDACnC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DAC3B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;6DAC9B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;0DAC/B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;6DAC9B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;2DAClD;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;6DAC/B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gEAC9B;AAG3B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;8BACvD,mBAAmB;qDAAC;AAM/B,MAAa,wBAAwB;IAEnC,IAAI,CAAS;IAGb,OAAO,CAAS;IAGhB,IAAI,CAAuB;CAC5B;AATD,4DASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;sDACvC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;yDAC1C;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC;8BAC7D,oBAAoB;sDAAC"}