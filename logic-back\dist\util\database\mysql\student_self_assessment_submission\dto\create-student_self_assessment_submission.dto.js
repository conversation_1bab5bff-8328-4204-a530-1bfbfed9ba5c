"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStudentSelfAssessmentSubmissionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CreateStudentSelfAssessmentSubmissionDto {
    assessmentItemId;
    assignmentId;
    studentId;
    score;
}
exports.CreateStudentSelfAssessmentSubmissionDto = CreateStudentSelfAssessmentSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的自评项ID' }),
    __metadata("design:type", Number)
], CreateStudentSelfAssessmentSubmissionDto.prototype, "assessmentItemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的学生任务完成情况ID' }),
    __metadata("design:type", Number)
], CreateStudentSelfAssessmentSubmissionDto.prototype, "assignmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生ID' }),
    __metadata("design:type", Number)
], CreateStudentSelfAssessmentSubmissionDto.prototype, "studentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分数' }),
    __metadata("design:type", Number)
], CreateStudentSelfAssessmentSubmissionDto.prototype, "score", void 0);
//# sourceMappingURL=create-student_self_assessment_submission.dto.js.map