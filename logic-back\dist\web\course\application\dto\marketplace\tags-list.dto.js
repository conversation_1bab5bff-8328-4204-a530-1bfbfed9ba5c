"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsListResponseDto = exports.TagsListDataDto = exports.TagsPaginationDto = exports.TagDetailDto = exports.GetTagsListQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class GetTagsListQueryDto {
    page = 1;
    pageSize = 50;
    category;
    status;
    keyword;
}
exports.GetTagsListQueryDto = GetTagsListQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '页码，默认1', example: 1, minimum: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], GetTagsListQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量，默认50', example: 50, minimum: 1, maximum: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], GetTagsListQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签分类：0=难度，1=类型，2=特色，3=其他', example: 0, enum: [0, 1, 2, 3] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1, 2, 3]),
    __metadata("design:type", Number)
], GetTagsListQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签状态：0=禁用，1=启用', example: 1, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1]),
    __metadata("design:type", Number)
], GetTagsListQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签名称搜索', example: '入门' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetTagsListQueryDto.prototype, "keyword", void 0);
class TagDetailDto {
    id;
    name;
    color;
    category;
    categoryLabel;
    description;
    usageCount;
    status;
    statusLabel;
    createdAt;
}
exports.TagDetailDto = TagDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签ID', example: 1 }),
    __metadata("design:type", Number)
], TagDetailDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签名称', example: '编程' }),
    __metadata("design:type", String)
], TagDetailDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签颜色', example: '#007bff' }),
    __metadata("design:type", String)
], TagDetailDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类：0=难度，1=类型，2=特色，3=其他', example: 1 }),
    __metadata("design:type", Number)
], TagDetailDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类标签', example: '类型' }),
    __metadata("design:type", String)
], TagDetailDto.prototype, "categoryLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签描述', example: '编程相关课程' }),
    __metadata("design:type", String)
], TagDetailDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用次数', example: 25 }),
    __metadata("design:type", Number)
], TagDetailDto.prototype, "usageCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签状态：0=禁用，1=启用', example: 1 }),
    __metadata("design:type", Number)
], TagDetailDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签状态标签', example: '启用' }),
    __metadata("design:type", String)
], TagDetailDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-10T10:30:00Z' }),
    __metadata("design:type", String)
], TagDetailDto.prototype, "createdAt", void 0);
class TagsPaginationDto {
    page;
    pageSize;
    total;
    totalPages;
    hasNext;
    hasPrev;
}
exports.TagsPaginationDto = TagsPaginationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码', example: 1 }),
    __metadata("design:type", Number)
], TagsPaginationDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 50 }),
    __metadata("design:type", Number)
], TagsPaginationDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总记录数', example: 12 }),
    __metadata("design:type", Number)
], TagsPaginationDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数', example: 1 }),
    __metadata("design:type", Number)
], TagsPaginationDto.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有下一页', example: false }),
    __metadata("design:type", Boolean)
], TagsPaginationDto.prototype, "hasNext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有上一页', example: false }),
    __metadata("design:type", Boolean)
], TagsPaginationDto.prototype, "hasPrev", void 0);
class TagsListDataDto {
    list;
    pagination;
}
exports.TagsListDataDto = TagsListDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签列表', type: [TagDetailDto] }),
    __metadata("design:type", Array)
], TagsListDataDto.prototype, "list", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分页信息', type: TagsPaginationDto }),
    __metadata("design:type", TagsPaginationDto)
], TagsListDataDto.prototype, "pagination", void 0);
class TagsListResponseDto {
    code;
    message;
    data;
}
exports.TagsListResponseDto = TagsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], TagsListResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], TagsListResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签列表数据', type: () => TagsListDataDto }),
    __metadata("design:type", TagsListDataDto)
], TagsListResponseDto.prototype, "data", void 0);
//# sourceMappingURL=tags-list.dto.js.map