"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserRoleTemplateTaskService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRoleTemplateTaskService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const role_permission_template_entity_1 = require("../../util/database/mysql/role_permission_templates/entities/role_permission_template.entity");
const role_template_extension_permission_entity_1 = require("../../util/database/mysql/role_template_extension_permission/entities/role_template_extension_permission.entity");
const role_template_block_permission_entity_1 = require("../../util/database/mysql/role_template_block_permission/entities/role_template_block_permission.entity");
const block_entity_1 = require("../../util/database/mysql/block/entities/block.entity");
const extension_entity_1 = require("../../util/database/mysql/extensions/entities/extension.entity");
let UserRoleTemplateTaskService = UserRoleTemplateTaskService_1 = class UserRoleTemplateTaskService {
    templateRepository;
    extensionPermissionRepository;
    blockPermissionRepository;
    blockRepository;
    extensionRepository;
    logger = new common_1.Logger(UserRoleTemplateTaskService_1.name);
    constructor(templateRepository, extensionPermissionRepository, blockPermissionRepository, blockRepository, extensionRepository) {
        this.templateRepository = templateRepository;
        this.extensionPermissionRepository = extensionPermissionRepository;
        this.blockPermissionRepository = blockPermissionRepository;
        this.blockRepository = blockRepository;
        this.extensionRepository = extensionRepository;
    }
    async onModuleInit() {
        this.logger.log('用户角色模板任务初始化开始...');
        try {
            await this.executeStartupTask();
            this.logger.log('用户角色模板任务初始化完成');
        }
        catch (error) {
            this.logger.error(`用户角色模板任务初始化失败: ${error.message}`, error.stack);
        }
    }
    async executeStartupTask() {
        try {
            this.logger.log('执行用户角色模板启动任务...');
            this.logger.log(`Template Repository available: ${!!this.templateRepository}`);
            this.logger.log(`Extension Permission Repository available: ${!!this.extensionPermissionRepository}`);
            this.logger.log(`Block Permission Repository available: ${!!this.blockPermissionRepository}`);
            this.logger.log(`Block Repository available: ${!!this.blockRepository}`);
            this.logger.log(`Extension Repository available: ${!!this.extensionRepository}`);
            await this.checkDatabaseTables();
            await this.ensureSpecialTemplateWithAllPermissions();
        }
        catch (error) {
            this.logger.error(`用户角色模板启动任务执行失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async checkDatabaseTables() {
        try {
            this.logger.log('开始检查数据库表结构...');
            const queryRunner = this.templateRepository.manager.connection.createQueryRunner();
            const tables = [
                'role_permission_templates',
                'role_template_extension_permission',
                'role_template_block_permission',
                'blocks',
                'extensions'
            ];
            for (const table of tables) {
                const exists = await queryRunner.hasTable(table);
                this.logger.log(`数据库表 ${table} ${exists ? '存在' : '不存在'}`);
                if (!exists) {
                    this.logger.warn(`表 ${table} 不存在，这可能导致操作失败`);
                }
            }
            try {
                const templateCount = await this.templateRepository.count();
                this.logger.log(`RolePermissionTemplate 表中有 ${templateCount} 条记录`);
                const extensionPermissionCount = await this.extensionPermissionRepository.count();
                this.logger.log(`RoleTemplateExtensionPermission 表中有 ${extensionPermissionCount} 条记录`);
                const blockPermissionCount = await this.blockPermissionRepository.count();
                this.logger.log(`RoleTemplateBlockPermission 表中有 ${blockPermissionCount} 条记录`);
                const blockCount = await this.blockRepository.count();
                this.logger.log(`Block 表中有 ${blockCount} 条记录`);
                const extensionCount = await this.extensionRepository.count();
                this.logger.log(`Extension 表中有 ${extensionCount} 条记录`);
            }
            catch (error) {
                this.logger.error(`检查表记录数失败: ${error.message}`);
            }
            await queryRunner.release();
        }
        catch (error) {
            this.logger.error(`检查数据库表结构失败: ${error.message}`, error.stack);
        }
    }
    async ensureSpecialTemplateWithAllPermissions() {
        try {
            this.logger.log('检查特殊权限模板...');
            let specialTemplate = await this.templateRepository.findOne({
                where: { userId: -1 }
            });
            if (!specialTemplate) {
                this.logger.log('创建新的特殊权限模板...');
                specialTemplate = await this.templateRepository.save({
                    roleId: 4,
                    userId: -1,
                    templateName: '系统默认全权限模板',
                    templateDescription: '此模板包含所有积木和扩展的权限，由系统自动维护',
                    isDefault: false,
                    isOfficial: true,
                    createTime: new Date(),
                    updateTime: new Date()
                });
            }
            const allExtensionsAndBlocks = await this.getAllExtensionsAndBlocks();
            const currentExtensionPermissions = await this.extensionPermissionRepository.find({
                where: { templateId: specialTemplate.id }
            });
            const currentBlockPermissions = await this.blockPermissionRepository.find({
                where: { templateId: specialTemplate.id }
            });
            const requiredBlocks = [
                { extensionId: 'logicleapUrl', blockId: 'logicleapUrl_getImageUrl' },
                { extensionId: 'logicleapUrl', blockId: 'logicleapUrl_getVoiceUrl' }
            ];
            for (const permission of currentBlockPermissions) {
                const blockExists = allExtensionsAndBlocks.blocks.some(block => block.extensionId === permission.extensionId && block.blockId === permission.blockId);
                const isRequiredBlock = requiredBlocks.some(block => block.extensionId === permission.extensionId && block.blockId === permission.blockId);
                if (!blockExists && !isRequiredBlock) {
                    await this.blockPermissionRepository.delete(permission.id);
                    this.logger.log(`删除不存在的积木权限: ${permission.extensionId}_${permission.blockId}`);
                }
            }
            for (const permission of currentExtensionPermissions) {
                const extensionExists = allExtensionsAndBlocks.extensions.includes(permission.extensionId);
                const isRequiredExtension = permission.extensionId === 'logicleapUrl';
                if (!extensionExists && !isRequiredExtension) {
                    await this.extensionPermissionRepository.delete(permission.id);
                    this.logger.log(`删除不存在的扩展权限: ${permission.extensionId}`);
                }
            }
            for (const ext of allExtensionsAndBlocks.extensions) {
                const existingPermission = currentExtensionPermissions.find(p => p.extensionId === ext);
                if (!existingPermission) {
                    await this.extensionPermissionRepository.save({
                        templateId: specialTemplate.id,
                        extensionId: ext,
                        isEnabled: true
                    });
                    this.logger.log(`为特殊模板添加扩展权限: ${ext}`);
                }
                else if (!existingPermission.isEnabled) {
                    await this.extensionPermissionRepository.update({ id: existingPermission.id }, { isEnabled: true });
                    this.logger.log(`更新特殊模板扩展权限状态: ${ext}`);
                }
            }
            for (const block of allExtensionsAndBlocks.blocks) {
                const existingPermission = currentBlockPermissions.find(p => p.blockId === block.blockId && p.extensionId === block.extensionId);
                if (!existingPermission) {
                    await this.blockPermissionRepository.save({
                        templateId: specialTemplate.id,
                        extensionId: block.extensionId,
                        blockId: block.blockId,
                        isEnabled: true
                    });
                    this.logger.log(`为特殊模板添加积木块权限: ${block.extensionId}_${block.blockId}`);
                }
                else if (!existingPermission.isEnabled) {
                    await this.blockPermissionRepository.update({ id: existingPermission.id }, { isEnabled: true });
                    this.logger.log(`更新特殊模板积木块权限状态: ${block.extensionId}_${block.blockId}`);
                }
            }
            for (const block of requiredBlocks) {
                const existingPermission = currentBlockPermissions.find(p => p.blockId === block.blockId && p.extensionId === block.extensionId);
                if (!existingPermission) {
                    await this.blockPermissionRepository.save({
                        templateId: specialTemplate.id,
                        extensionId: block.extensionId,
                        blockId: block.blockId,
                        isEnabled: true
                    });
                    this.logger.log(`为特殊模板添加必需积木块权限: ${block.extensionId}_${block.blockId}`);
                }
                else if (!existingPermission.isEnabled) {
                    await this.blockPermissionRepository.update({ id: existingPermission.id }, { isEnabled: true });
                    this.logger.log(`更新特殊模板必需积木块权限状态: ${block.extensionId}_${block.blockId}`);
                }
            }
            this.logger.log('特殊权限模板更新完成，确保所有积木和扩展权限都已启用');
        }
        catch (error) {
            this.logger.error(`更新特殊权限模板失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getAllExtensionsAndBlocks() {
        try {
            this.logger.log('开始获取系统中所有可用的扩展和积木块...');
            const extensions = await this.extensionRepository.find();
            const extensionIds = extensions.map(ext => ext.extensionId);
            const blocks = await this.blockRepository.find();
            const blocksList = blocks.map(block => ({
                extensionId: block.extensionId,
                blockId: block.blockId
            }));
            const requiredBlocks = [
                { extensionId: 'logicleapUrl', blockId: 'logicleapUrl_getImageUrl' },
                { extensionId: 'logicleapUrl', blockId: 'logicleapUrl_getVoiceUrl' }
            ];
            const allBlocks = [...blocksList];
            for (const block of requiredBlocks) {
                if (!allBlocks.some(b => b.extensionId === block.extensionId && b.blockId === block.blockId)) {
                    allBlocks.push(block);
                    this.logger.log(`添加必需的积木块: ${block.extensionId}_${block.blockId}`);
                }
            }
            const extensionsFromBlocks = [...new Set(allBlocks.map(b => b.extensionId))];
            const allExtensions = [...new Set([...extensionIds, ...extensionsFromBlocks])];
            if (!allExtensions.includes('logicleapUrl')) {
                allExtensions.push('logicleapUrl');
                this.logger.log('添加必需的扩展: logicleapUrl');
            }
            this.logger.log(`发现系统中有 ${allExtensions.length} 个扩展和 ${allBlocks.length} 个积木块`);
            return {
                extensions: allExtensions,
                blocks: allBlocks
            };
        }
        catch (error) {
            this.logger.error(`获取系统积木和扩展信息失败: ${error.message}`, error.stack);
            const basicExtensions = [
                'motion', 'looks', 'sound', 'events', 'control',
                'sensing', 'operators', 'variables', 'procedures',
                'music', 'pen', 'logicleapUrl', 'logicleapAnswer'
            ];
            const basicBlocks = [
                { extensionId: 'motion', blockId: 'motion_movesteps' },
                { extensionId: 'looks', blockId: 'looks_say' },
                { extensionId: 'sound', blockId: 'sound_play' },
                { extensionId: 'control', blockId: 'control_wait' },
                { extensionId: 'logicleapUrl', blockId: 'logicleapUrl_getImageUrl' },
                { extensionId: 'logicleapUrl', blockId: 'logicleapUrl_getVoiceUrl' },
                { extensionId: 'logicleapAnswer', blockId: 'logicleapAnswer_setAnswer' }
            ];
            return {
                extensions: basicExtensions,
                blocks: basicBlocks
            };
        }
    }
};
exports.UserRoleTemplateTaskService = UserRoleTemplateTaskService;
exports.UserRoleTemplateTaskService = UserRoleTemplateTaskService = UserRoleTemplateTaskService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_permission_template_entity_1.RolePermissionTemplate)),
    __param(1, (0, typeorm_1.InjectRepository)(role_template_extension_permission_entity_1.RoleTemplateExtensionPermission)),
    __param(2, (0, typeorm_1.InjectRepository)(role_template_block_permission_entity_1.RoleTemplateBlockPermission)),
    __param(3, (0, typeorm_1.InjectRepository)(block_entity_1.Block)),
    __param(4, (0, typeorm_1.InjectRepository)(extension_entity_1.Extension)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], UserRoleTemplateTaskService);
//# sourceMappingURL=user_role_template_task.service.js.map