"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/lib/create-h.js":
/*!*************************************************!*\
  !*** ./node_modules/hastscript/lib/create-h.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createH: () => (/* binding */ createH)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').RootContent} RootContent\n *\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n * @typedef {{[property: string]: PropertyValue | Style}} Properties\n *   Acceptable value for element properties.\n *\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {Array<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nfunction createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive && createAdjustMap(caseSensitive)\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    let index = -1\n    /** @type {Result} */\n    let node\n\n    if (selector === undefined || selector === null) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n      // Normalize the name.\n      node.tagName = node.tagName.toLowerCase()\n      if (adjust && own.call(adjust, node.tagName)) {\n        node.tagName = adjust[node.tagName]\n      }\n\n      // Handle props.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        /** @type {string} */\n        let key\n\n        for (key in properties) {\n          if (own.call(properties, key)) {\n            addProperty(schema, node.properties, key, properties[key])\n          }\n        }\n      }\n    }\n\n    // Handle children.\n    while (++index < children.length) {\n      addChild(node.children, children[index])\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {Array<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  let index = -1\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === undefined || value === null) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = value.concat()\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    while (++index < result.length) {\n      // Assume no booleans in array.\n      const value = /** @type {number | string} */ (\n        parsePrimitive(info, info.property, result[index])\n      )\n      finalResult[index] = value\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    const value = /** @type {number | string} */ (result)\n    result = properties.className.concat(value)\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  let index = -1\n\n  if (value === undefined || value === null) {\n    // Empty.\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    while (++index < value.length) {\n      addChild(nodes, value[index])\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} value\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(value) {\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      result.push([key, value[key]].join(': '))\n    }\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {Array<string>} values\n *   List of properly cased keys.\n * @returns {Record<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Record<string, string>} */\n  const result = {}\n  let index = -1\n\n  while (++index < values.length) {\n    result[values[index].toLowerCase()] = values[index]\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvY3JlYXRlLWguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLHFCQUFxQjtBQUNsQyxhQUFhLDRCQUE0QjtBQUN6QztBQUNBLGFBQWEscUNBQXFDO0FBQ2xELGFBQWEsdUNBQXVDO0FBQ3BEOztBQUVBO0FBQ0EsYUFBYSxnQkFBZ0I7QUFDN0I7QUFDQTtBQUNBLGFBQWEsaUJBQWlCO0FBQzlCO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekM7QUFDQSxhQUFhLDhDQUE4QztBQUMzRDtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUM7QUFDQSxjQUFjLDRDQUE0QztBQUMxRDtBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQ7QUFDQSxhQUFhLGtEQUFrRDtBQUMvRDtBQUNBLGFBQWEsK0JBQStCO0FBQzVDO0FBQ0EsYUFBYSxxQ0FBcUM7QUFDbEQ7QUFDQTs7QUFFc0Q7QUFDQTtBQUNGO0FBQ0U7O0FBRXRELGNBQWM7O0FBRWQ7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLDJCQUEyQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGtCQUFrQjtBQUMvQixhQUFhLFVBQVU7QUFDdkIsZUFBZTtBQUNmO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxZQUFZO0FBQ3pCLGFBQWEsVUFBVTtBQUN2QixlQUFlO0FBQ2Y7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFVBQVU7QUFDdkIsZUFBZTtBQUNmO0FBQ0EsYUFBYSwyQkFBMkI7QUFDeEM7QUFDQSxhQUFhLHVDQUF1QztBQUNwRDtBQUNBLGFBQWEsVUFBVTtBQUN2QjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2Qjs7QUFFQTtBQUNBLGNBQWM7QUFDZDtBQUNBLCtCQUErQixPQUFPO0FBQ3RDO0FBQ0EsTUFBTTtBQUNOLGFBQWEsdUVBQWE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1IsbUJBQW1CLFFBQVE7QUFDM0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsZ0NBQWdDO0FBQ2hDOztBQUVBO0FBQ0E7QUFDQSw0QkFBNEIseUJBQXlCO0FBQ3JEOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSw4QkFBOEIsZ0JBQWdCOztBQUU5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLFlBQVk7QUFDdkI7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLHVCQUF1QjtBQUNsQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxlQUFlLDBEQUFJO0FBQ25CO0FBQ0EsYUFBYSxlQUFlO0FBQzVCOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNkRBQU07QUFDckIsTUFBTTtBQUNOLGVBQWUsNkRBQU07QUFDckIsTUFBTTtBQUNOLGVBQWUsNkRBQU0sQ0FBQyw2REFBTTtBQUM1QixNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0EsZUFBZSx3QkFBd0I7QUFDdkM7O0FBRUE7QUFDQTtBQUNBLCtCQUErQixpQkFBaUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsaUJBQWlCO0FBQzlDO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0osZ0JBQWdCLG1DQUFtQztBQUNuRCxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsV0FBVyxnQkFBZ0I7QUFDM0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHVCQUF1QiwrREFBUyxZQUFZLCtEQUFTO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWEsZUFBZTtBQUM1QjtBQUNBLGFBQWEsUUFBUTtBQUNyQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHdCQUF3QjtBQUN4Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGVBQWU7QUFDMUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9oYXN0c2NyaXB0L2xpYi9jcmVhdGUtaC5qcz9iMzFhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Ob2Rlc30gTm9kZXNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUm9vdENvbnRlbnR9IFJvb3RDb250ZW50XG4gKlxuICogQHR5cGVkZWYge2ltcG9ydCgncHJvcGVydHktaW5mb3JtYXRpb24nKS5JbmZvfSBJbmZvXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdwcm9wZXJ0eS1pbmZvcm1hdGlvbicpLlNjaGVtYX0gU2NoZW1hXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiB7RWxlbWVudCB8IFJvb3R9IFJlc3VsdFxuICogICBSZXN1bHQgZnJvbSBhIGBoYCAob3IgYHNgKSBjYWxsLlxuICpcbiAqIEB0eXBlZGVmIHtudW1iZXIgfCBzdHJpbmd9IFN0eWxlVmFsdWVcbiAqICAgVmFsdWUgZm9yIGEgQ1NTIHN0eWxlIGZpZWxkLlxuICogQHR5cGVkZWYge1JlY29yZDxzdHJpbmcsIFN0eWxlVmFsdWU+fSBTdHlsZVxuICogICBTdXBwb3J0ZWQgdmFsdWUgb2YgYSBgc3R5bGVgIHByb3AuXG4gKiBAdHlwZWRlZiB7Ym9vbGVhbiB8IG51bWJlciB8IHN0cmluZyB8IG51bGwgfCB1bmRlZmluZWR9IFByaW1pdGl2ZVZhbHVlXG4gKiAgIFByaW1pdGl2ZSBwcm9wZXJ0eSB2YWx1ZS5cbiAqIEB0eXBlZGVmIHtBcnJheTxudW1iZXIgfCBzdHJpbmc+fSBBcnJheVZhbHVlXG4gKiAgIExpc3Qgb2YgcHJvcGVydHkgdmFsdWVzIGZvciBzcGFjZS0gb3IgY29tbWEgc2VwYXJhdGVkIHZhbHVlcyAoc3VjaCBhcyBgY2xhc3NOYW1lYCkuXG4gKiBAdHlwZWRlZiB7QXJyYXlWYWx1ZSB8IFByaW1pdGl2ZVZhbHVlfSBQcm9wZXJ0eVZhbHVlXG4gKiAgIFByaW1pdGl2ZSB2YWx1ZSBvciBsaXN0IHZhbHVlLlxuICogQHR5cGVkZWYge3tbcHJvcGVydHk6IHN0cmluZ106IFByb3BlcnR5VmFsdWUgfCBTdHlsZX19IFByb3BlcnRpZXNcbiAqICAgQWNjZXB0YWJsZSB2YWx1ZSBmb3IgZWxlbWVudCBwcm9wZXJ0aWVzLlxuICpcbiAqIEB0eXBlZGVmIHtudW1iZXIgfCBzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBQcmltaXRpdmVDaGlsZFxuICogICBQcmltaXRpdmUgY2hpbGRyZW4sIGVpdGhlciBpZ25vcmVkIChudWxsaXNoKSwgb3IgdHVybmVkIGludG8gdGV4dCBub2Rlcy5cbiAqIEB0eXBlZGVmIHtBcnJheTxBcnJheUNoaWxkTmVzdGVkIHwgTm9kZXMgfCBQcmltaXRpdmVDaGlsZD59IEFycmF5Q2hpbGRcbiAqICAgTGlzdCBvZiBjaGlsZHJlbi5cbiAqIEB0eXBlZGVmIHtBcnJheTxOb2RlcyB8IFByaW1pdGl2ZUNoaWxkPn0gQXJyYXlDaGlsZE5lc3RlZFxuICogICBMaXN0IG9mIGNoaWxkcmVuIChkZWVwKS5cbiAqIEB0eXBlZGVmIHtBcnJheUNoaWxkIHwgTm9kZXMgfCBQcmltaXRpdmVDaGlsZH0gQ2hpbGRcbiAqICAgQWNjZXB0YWJsZSBjaGlsZCB2YWx1ZS5cbiAqL1xuXG5pbXBvcnQge3BhcnNlIGFzIGNvbW1hc30gZnJvbSAnY29tbWEtc2VwYXJhdGVkLXRva2VucydcbmltcG9ydCB7cGFyc2VTZWxlY3Rvcn0gZnJvbSAnaGFzdC11dGlsLXBhcnNlLXNlbGVjdG9yJ1xuaW1wb3J0IHtmaW5kLCBub3JtYWxpemV9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuaW1wb3J0IHtwYXJzZSBhcyBzcGFjZXN9IGZyb20gJ3NwYWNlLXNlcGFyYXRlZC10b2tlbnMnXG5cbmNvbnN0IG93biA9IHt9Lmhhc093blByb3BlcnR5XG5cbi8qKlxuICogQHBhcmFtIHtTY2hlbWF9IHNjaGVtYVxuICogICBTY2hlbWEgdG8gdXNlLlxuICogQHBhcmFtIHtzdHJpbmd9IGRlZmF1bHRUYWdOYW1lXG4gKiAgIERlZmF1bHQgdGFnIG5hbWUuXG4gKiBAcGFyYW0ge0FycmF5PHN0cmluZz4gfCB1bmRlZmluZWR9IFtjYXNlU2Vuc2l0aXZlXVxuICogICBDYXNlLXNlbnNpdGl2ZSB0YWcgbmFtZXMgKGRlZmF1bHQ6IGB1bmRlZmluZWRgKS5cbiAqIEByZXR1cm5zXG4gKiAgIGBoYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUgoc2NoZW1hLCBkZWZhdWx0VGFnTmFtZSwgY2FzZVNlbnNpdGl2ZSkge1xuICBjb25zdCBhZGp1c3QgPSBjYXNlU2Vuc2l0aXZlICYmIGNyZWF0ZUFkanVzdE1hcChjYXNlU2Vuc2l0aXZlKVxuXG4gIC8qKlxuICAgKiBIeXBlcnNjcmlwdCBjb21wYXRpYmxlIERTTCBmb3IgY3JlYXRpbmcgdmlydHVhbCBoYXN0IHRyZWVzLlxuICAgKlxuICAgKiBAb3ZlcmxvYWRcbiAgICogQHBhcmFtIHtudWxsIHwgdW5kZWZpbmVkfSBbc2VsZWN0b3JdXG4gICAqIEBwYXJhbSB7Li4uQ2hpbGR9IGNoaWxkcmVuXG4gICAqIEByZXR1cm5zIHtSb290fVxuICAgKlxuICAgKiBAb3ZlcmxvYWRcbiAgICogQHBhcmFtIHtzdHJpbmd9IHNlbGVjdG9yXG4gICAqIEBwYXJhbSB7UHJvcGVydGllc30gcHJvcGVydGllc1xuICAgKiBAcGFyYW0gey4uLkNoaWxkfSBjaGlsZHJlblxuICAgKiBAcmV0dXJucyB7RWxlbWVudH1cbiAgICpcbiAgICogQG92ZXJsb2FkXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBzZWxlY3RvclxuICAgKiBAcGFyYW0gey4uLkNoaWxkfSBjaGlsZHJlblxuICAgKiBAcmV0dXJucyB7RWxlbWVudH1cbiAgICpcbiAgICogQHBhcmFtIHtzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbc2VsZWN0b3JdXG4gICAqICAgU2VsZWN0b3IuXG4gICAqIEBwYXJhbSB7Q2hpbGQgfCBQcm9wZXJ0aWVzIHwgbnVsbCB8IHVuZGVmaW5lZH0gW3Byb3BlcnRpZXNdXG4gICAqICAgUHJvcGVydGllcyAob3IgZmlyc3QgY2hpbGQpIChkZWZhdWx0OiBgdW5kZWZpbmVkYCkuXG4gICAqIEBwYXJhbSB7Li4uQ2hpbGR9IGNoaWxkcmVuXG4gICAqICAgQ2hpbGRyZW4uXG4gICAqIEByZXR1cm5zIHtSZXN1bHR9XG4gICAqICAgUmVzdWx0LlxuICAgKi9cbiAgZnVuY3Rpb24gaChzZWxlY3RvciwgcHJvcGVydGllcywgLi4uY2hpbGRyZW4pIHtcbiAgICBsZXQgaW5kZXggPSAtMVxuICAgIC8qKiBAdHlwZSB7UmVzdWx0fSAqL1xuICAgIGxldCBub2RlXG5cbiAgICBpZiAoc2VsZWN0b3IgPT09IHVuZGVmaW5lZCB8fCBzZWxlY3RvciA9PT0gbnVsbCkge1xuICAgICAgbm9kZSA9IHt0eXBlOiAncm9vdCcsIGNoaWxkcmVuOiBbXX1cbiAgICAgIC8vIFByb3BlcnRpZXMgYXJlIG5vdCBzdXBwb3J0ZWQgZm9yIHJvb3RzLlxuICAgICAgY29uc3QgY2hpbGQgPSAvKiogQHR5cGUge0NoaWxkfSAqLyAocHJvcGVydGllcylcbiAgICAgIGNoaWxkcmVuLnVuc2hpZnQoY2hpbGQpXG4gICAgfSBlbHNlIHtcbiAgICAgIG5vZGUgPSBwYXJzZVNlbGVjdG9yKHNlbGVjdG9yLCBkZWZhdWx0VGFnTmFtZSlcbiAgICAgIC8vIE5vcm1hbGl6ZSB0aGUgbmFtZS5cbiAgICAgIG5vZGUudGFnTmFtZSA9IG5vZGUudGFnTmFtZS50b0xvd2VyQ2FzZSgpXG4gICAgICBpZiAoYWRqdXN0ICYmIG93bi5jYWxsKGFkanVzdCwgbm9kZS50YWdOYW1lKSkge1xuICAgICAgICBub2RlLnRhZ05hbWUgPSBhZGp1c3Rbbm9kZS50YWdOYW1lXVxuICAgICAgfVxuXG4gICAgICAvLyBIYW5kbGUgcHJvcHMuXG4gICAgICBpZiAoaXNDaGlsZChwcm9wZXJ0aWVzKSkge1xuICAgICAgICBjaGlsZHJlbi51bnNoaWZ0KHByb3BlcnRpZXMpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICAgICAgbGV0IGtleVxuXG4gICAgICAgIGZvciAoa2V5IGluIHByb3BlcnRpZXMpIHtcbiAgICAgICAgICBpZiAob3duLmNhbGwocHJvcGVydGllcywga2V5KSkge1xuICAgICAgICAgICAgYWRkUHJvcGVydHkoc2NoZW1hLCBub2RlLnByb3BlcnRpZXMsIGtleSwgcHJvcGVydGllc1trZXldKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEhhbmRsZSBjaGlsZHJlbi5cbiAgICB3aGlsZSAoKytpbmRleCA8IGNoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgYWRkQ2hpbGQobm9kZS5jaGlsZHJlbiwgY2hpbGRyZW5baW5kZXhdKVxuICAgIH1cblxuICAgIGlmIChub2RlLnR5cGUgPT09ICdlbGVtZW50JyAmJiBub2RlLnRhZ05hbWUgPT09ICd0ZW1wbGF0ZScpIHtcbiAgICAgIG5vZGUuY29udGVudCA9IHt0eXBlOiAncm9vdCcsIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVufVxuICAgICAgbm9kZS5jaGlsZHJlbiA9IFtdXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vZGVcbiAgfVxuXG4gIHJldHVybiBoXG59XG5cbi8qKlxuICogQ2hlY2sgaWYgc29tZXRoaW5nIGlzIHByb3BlcnRpZXMgb3IgYSBjaGlsZC5cbiAqXG4gKiBAcGFyYW0ge0NoaWxkIHwgUHJvcGVydGllc30gdmFsdWVcbiAqICAgVmFsdWUgdG8gY2hlY2suXG4gKiBAcmV0dXJucyB7dmFsdWUgaXMgQ2hpbGR9XG4gKiAgIFdoZXRoZXIgYHZhbHVlYCBpcyBkZWZpbml0ZWx5IGEgY2hpbGQuXG4gKi9cbmZ1bmN0aW9uIGlzQ2hpbGQodmFsdWUpIHtcbiAgLy8gTmV2ZXIgcHJvcGVydGllcyBpZiBub3QgYW4gb2JqZWN0LlxuICBpZiAodmFsdWUgPT09IG51bGwgfHwgdHlwZW9mIHZhbHVlICE9PSAnb2JqZWN0JyB8fCBBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICAvLyBOZXZlciBub2RlIHdpdGhvdXQgYHR5cGVgOyB0aGF04oCZcyB0aGUgbWFpbiBkaXNjcmltaW5hdG9yLlxuICBpZiAodHlwZW9mIHZhbHVlLnR5cGUgIT09ICdzdHJpbmcnKSByZXR1cm4gZmFsc2VcblxuICAvLyBTbG93ZXIgY2hlY2s6IG5ldmVyIHByb3BlcnR5IHZhbHVlIGlmIG9iamVjdCBvciBhcnJheSB3aXRoXG4gIC8vIG5vbi1udW1iZXIvc3RyaW5ncy5cbiAgY29uc3QgcmVjb3JkID0gLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCB1bmtub3duPn0gKi8gKHZhbHVlKVxuICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXModmFsdWUpXG5cbiAgZm9yIChjb25zdCBrZXkgb2Yga2V5cykge1xuICAgIGNvbnN0IHZhbHVlID0gcmVjb3JkW2tleV1cblxuICAgIGlmICh2YWx1ZSAmJiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnKSB7XG4gICAgICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWUpKSByZXR1cm4gdHJ1ZVxuXG4gICAgICBjb25zdCBsaXN0ID0gLyoqIEB0eXBlIHtBcnJheTx1bmtub3duPn0gKi8gKHZhbHVlKVxuXG4gICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgbGlzdCkge1xuICAgICAgICBpZiAodHlwZW9mIGl0ZW0gIT09ICdudW1iZXInICYmIHR5cGVvZiBpdGVtICE9PSAnc3RyaW5nJykge1xuICAgICAgICAgIHJldHVybiB0cnVlXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBBbHNvIHNlZSBlbXB0eSBgY2hpbGRyZW5gIGFzIGEgbm9kZS5cbiAgaWYgKCdjaGlsZHJlbicgaW4gdmFsdWUgJiYgQXJyYXkuaXNBcnJheSh2YWx1ZS5jaGlsZHJlbikpIHtcbiAgICByZXR1cm4gdHJ1ZVxuICB9XG5cbiAgLy8gRGVmYXVsdCB0byBwcm9wZXJ0aWVzLCBzb21lb25lIGNhbiBhbHdheXMgcGFzcyBhbiBlbXB0eSBvYmplY3QsXG4gIC8vIHB1dCBgZGF0YToge31gIGluIGEgbm9kZSxcbiAgLy8gb3Igd3JhcCBpdCBpbiBhbiBhcnJheS5cbiAgcmV0dXJuIGZhbHNlXG59XG5cbi8qKlxuICogQHBhcmFtIHtTY2hlbWF9IHNjaGVtYVxuICogICBTY2hlbWEuXG4gKiBAcGFyYW0ge1Byb3BlcnRpZXN9IHByb3BlcnRpZXNcbiAqICAgUHJvcGVydGllcyBvYmplY3QuXG4gKiBAcGFyYW0ge3N0cmluZ30ga2V5XG4gKiAgIFByb3BlcnR5IG5hbWUuXG4gKiBAcGFyYW0ge1Byb3BlcnR5VmFsdWUgfCBTdHlsZX0gdmFsdWVcbiAqICAgUHJvcGVydHkgdmFsdWUuXG4gKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICogICBOb3RoaW5nLlxuICovXG5mdW5jdGlvbiBhZGRQcm9wZXJ0eShzY2hlbWEsIHByb3BlcnRpZXMsIGtleSwgdmFsdWUpIHtcbiAgY29uc3QgaW5mbyA9IGZpbmQoc2NoZW1hLCBrZXkpXG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7UHJvcGVydHlWYWx1ZX0gKi9cbiAgbGV0IHJlc3VsdFxuXG4gIC8vIElnbm9yZSBudWxsaXNoIGFuZCBOYU4gdmFsdWVzLlxuICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gbnVsbCkgcmV0dXJuXG5cbiAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcicpIHtcbiAgICAvLyBJZ25vcmUgTmFOLlxuICAgIGlmIChOdW1iZXIuaXNOYU4odmFsdWUpKSByZXR1cm5cblxuICAgIHJlc3VsdCA9IHZhbHVlXG4gIH1cbiAgLy8gQm9vbGVhbnMuXG4gIGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nKSB7XG4gICAgcmVzdWx0ID0gdmFsdWVcbiAgfVxuICAvLyBIYW5kbGUgbGlzdCB2YWx1ZXMuXG4gIGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycpIHtcbiAgICBpZiAoaW5mby5zcGFjZVNlcGFyYXRlZCkge1xuICAgICAgcmVzdWx0ID0gc3BhY2VzKHZhbHVlKVxuICAgIH0gZWxzZSBpZiAoaW5mby5jb21tYVNlcGFyYXRlZCkge1xuICAgICAgcmVzdWx0ID0gY29tbWFzKHZhbHVlKVxuICAgIH0gZWxzZSBpZiAoaW5mby5jb21tYU9yU3BhY2VTZXBhcmF0ZWQpIHtcbiAgICAgIHJlc3VsdCA9IHNwYWNlcyhjb21tYXModmFsdWUpLmpvaW4oJyAnKSlcbiAgICB9IGVsc2Uge1xuICAgICAgcmVzdWx0ID0gcGFyc2VQcmltaXRpdmUoaW5mbywgaW5mby5wcm9wZXJ0eSwgdmFsdWUpXG4gICAgfVxuICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgcmVzdWx0ID0gdmFsdWUuY29uY2F0KClcbiAgfSBlbHNlIHtcbiAgICByZXN1bHQgPSBpbmZvLnByb3BlcnR5ID09PSAnc3R5bGUnID8gc3R5bGUodmFsdWUpIDogU3RyaW5nKHZhbHVlKVxuICB9XG5cbiAgaWYgKEFycmF5LmlzQXJyYXkocmVzdWx0KSkge1xuICAgIC8qKiBAdHlwZSB7QXJyYXk8bnVtYmVyIHwgc3RyaW5nPn0gKi9cbiAgICBjb25zdCBmaW5hbFJlc3VsdCA9IFtdXG5cbiAgICB3aGlsZSAoKytpbmRleCA8IHJlc3VsdC5sZW5ndGgpIHtcbiAgICAgIC8vIEFzc3VtZSBubyBib29sZWFucyBpbiBhcnJheS5cbiAgICAgIGNvbnN0IHZhbHVlID0gLyoqIEB0eXBlIHtudW1iZXIgfCBzdHJpbmd9ICovIChcbiAgICAgICAgcGFyc2VQcmltaXRpdmUoaW5mbywgaW5mby5wcm9wZXJ0eSwgcmVzdWx0W2luZGV4XSlcbiAgICAgIClcbiAgICAgIGZpbmFsUmVzdWx0W2luZGV4XSA9IHZhbHVlXG4gICAgfVxuXG4gICAgcmVzdWx0ID0gZmluYWxSZXN1bHRcbiAgfVxuXG4gIC8vIENsYXNzIG5hbWVzICh3aGljaCBjYW4gYmUgYWRkZWQgYm90aCBvbiB0aGUgYHNlbGVjdG9yYCBhbmQgaGVyZSkuXG4gIGlmIChpbmZvLnByb3BlcnR5ID09PSAnY2xhc3NOYW1lJyAmJiBBcnJheS5pc0FycmF5KHByb3BlcnRpZXMuY2xhc3NOYW1lKSkge1xuICAgIC8vIEFzc3VtZSBubyBib29sZWFucyBpbiBgY2xhc3NOYW1lYC5cbiAgICBjb25zdCB2YWx1ZSA9IC8qKiBAdHlwZSB7bnVtYmVyIHwgc3RyaW5nfSAqLyAocmVzdWx0KVxuICAgIHJlc3VsdCA9IHByb3BlcnRpZXMuY2xhc3NOYW1lLmNvbmNhdCh2YWx1ZSlcbiAgfVxuXG4gIHByb3BlcnRpZXNbaW5mby5wcm9wZXJ0eV0gPSByZXN1bHRcbn1cblxuLyoqXG4gKiBAcGFyYW0ge0FycmF5PFJvb3RDb250ZW50Pn0gbm9kZXNcbiAqICAgQ2hpbGRyZW4uXG4gKiBAcGFyYW0ge0NoaWxkfSB2YWx1ZVxuICogICBDaGlsZC5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIGFkZENoaWxkKG5vZGVzLCB2YWx1ZSkge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHZhbHVlID09PSBudWxsKSB7XG4gICAgLy8gRW1wdHkuXG4gIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSB7XG4gICAgbm9kZXMucHVzaCh7dHlwZTogJ3RleHQnLCB2YWx1ZTogU3RyaW5nKHZhbHVlKX0pXG4gIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICB3aGlsZSAoKytpbmRleCA8IHZhbHVlLmxlbmd0aCkge1xuICAgICAgYWRkQ2hpbGQobm9kZXMsIHZhbHVlW2luZGV4XSlcbiAgICB9XG4gIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiAndHlwZScgaW4gdmFsdWUpIHtcbiAgICBpZiAodmFsdWUudHlwZSA9PT0gJ3Jvb3QnKSB7XG4gICAgICBhZGRDaGlsZChub2RlcywgdmFsdWUuY2hpbGRyZW4pXG4gICAgfSBlbHNlIHtcbiAgICAgIG5vZGVzLnB1c2godmFsdWUpXG4gICAgfVxuICB9IGVsc2Uge1xuICAgIHRocm93IG5ldyBFcnJvcignRXhwZWN0ZWQgbm9kZSwgbm9kZXMsIG9yIHN0cmluZywgZ290IGAnICsgdmFsdWUgKyAnYCcpXG4gIH1cbn1cblxuLyoqXG4gKiBQYXJzZSBhIHNpbmdsZSBwcmltaXRpdmVzLlxuICpcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogICBQcm9wZXJ0eSBpbmZvcm1hdGlvbi5cbiAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gKiAgIFByb3BlcnR5IG5hbWUuXG4gKiBAcGFyYW0ge1ByaW1pdGl2ZVZhbHVlfSB2YWx1ZVxuICogICBQcm9wZXJ0eSB2YWx1ZS5cbiAqIEByZXR1cm5zIHtQcmltaXRpdmVWYWx1ZX1cbiAqICAgUHJvcGVydHkgdmFsdWUuXG4gKi9cbmZ1bmN0aW9uIHBhcnNlUHJpbWl0aXZlKGluZm8sIG5hbWUsIHZhbHVlKSB7XG4gIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgaWYgKGluZm8ubnVtYmVyICYmIHZhbHVlICYmICFOdW1iZXIuaXNOYU4oTnVtYmVyKHZhbHVlKSkpIHtcbiAgICAgIHJldHVybiBOdW1iZXIodmFsdWUpXG4gICAgfVxuXG4gICAgaWYgKFxuICAgICAgKGluZm8uYm9vbGVhbiB8fCBpbmZvLm92ZXJsb2FkZWRCb29sZWFuKSAmJlxuICAgICAgKHZhbHVlID09PSAnJyB8fCBub3JtYWxpemUodmFsdWUpID09PSBub3JtYWxpemUobmFtZSkpXG4gICAgKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB2YWx1ZVxufVxuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIGBzdHlsZWAgb2JqZWN0IGFzIGEgc3RyaW5nLlxuICpcbiAqIEBwYXJhbSB7U3R5bGV9IHZhbHVlXG4gKiAgIFN0eWxlIG9iamVjdC5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIENTUyBzdHJpbmcuXG4gKi9cbmZ1bmN0aW9uIHN0eWxlKHZhbHVlKSB7XG4gIC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn0gKi9cbiAgY29uc3QgcmVzdWx0ID0gW11cbiAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gIGxldCBrZXlcblxuICBmb3IgKGtleSBpbiB2YWx1ZSkge1xuICAgIGlmIChvd24uY2FsbCh2YWx1ZSwga2V5KSkge1xuICAgICAgcmVzdWx0LnB1c2goW2tleSwgdmFsdWVba2V5XV0uam9pbignOiAnKSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gcmVzdWx0LmpvaW4oJzsgJylcbn1cblxuLyoqXG4gKiBDcmVhdGUgYSBtYXAgdG8gYWRqdXN0IGNhc2luZy5cbiAqXG4gKiBAcGFyYW0ge0FycmF5PHN0cmluZz59IHZhbHVlc1xuICogICBMaXN0IG9mIHByb3Blcmx5IGNhc2VkIGtleXMuXG4gKiBAcmV0dXJucyB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn1cbiAqICAgTWFwIG9mIGxvd2VyY2FzZSBrZXlzIHRvIHVwcGVyY2FzZSBrZXlzLlxuICovXG5mdW5jdGlvbiBjcmVhdGVBZGp1c3RNYXAodmFsdWVzKSB7XG4gIC8qKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge31cbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IHZhbHVlcy5sZW5ndGgpIHtcbiAgICByZXN1bHRbdmFsdWVzW2luZGV4XS50b0xvd2VyQ2FzZSgpXSA9IHZhbHVlc1tpbmRleF1cbiAgfVxuXG4gIHJldHVybiByZXN1bHRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/create-h.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/hastscript/lib/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: () => (/* binding */ h),\n/* harmony export */   s: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _create_h_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-h.js */ \"(ssr)/./node_modules/hastscript/lib/create-h.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n/**\n * @typedef {import('./create-h.js').Child} Child\n *   Acceptable child value.\n * @typedef {import('./create-h.js').Properties} Properties\n *   Acceptable value for element properties.\n * @typedef {import('./create-h.js').Result} Result\n *   Result from a `h` (or `s`) call.\n */\n\n// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\n\n\n\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst h = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst s = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: () => (/* binding */ svgCaseSensitiveTagNames)\n/* harmony export */ });\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9oYXN0c2NyaXB0L2xpYi9zdmctY2FzZS1zZW5zaXRpdmUtdGFnLW5hbWVzLmpzPzY4YjgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHN2Z0Nhc2VTZW5zaXRpdmVUYWdOYW1lcyA9IFtcbiAgJ2FsdEdseXBoJyxcbiAgJ2FsdEdseXBoRGVmJyxcbiAgJ2FsdEdseXBoSXRlbScsXG4gICdhbmltYXRlQ29sb3InLFxuICAnYW5pbWF0ZU1vdGlvbicsXG4gICdhbmltYXRlVHJhbnNmb3JtJyxcbiAgJ2NsaXBQYXRoJyxcbiAgJ2ZlQmxlbmQnLFxuICAnZmVDb2xvck1hdHJpeCcsXG4gICdmZUNvbXBvbmVudFRyYW5zZmVyJyxcbiAgJ2ZlQ29tcG9zaXRlJyxcbiAgJ2ZlQ29udm9sdmVNYXRyaXgnLFxuICAnZmVEaWZmdXNlTGlnaHRpbmcnLFxuICAnZmVEaXNwbGFjZW1lbnRNYXAnLFxuICAnZmVEaXN0YW50TGlnaHQnLFxuICAnZmVEcm9wU2hhZG93JyxcbiAgJ2ZlRmxvb2QnLFxuICAnZmVGdW5jQScsXG4gICdmZUZ1bmNCJyxcbiAgJ2ZlRnVuY0cnLFxuICAnZmVGdW5jUicsXG4gICdmZUdhdXNzaWFuQmx1cicsXG4gICdmZUltYWdlJyxcbiAgJ2ZlTWVyZ2UnLFxuICAnZmVNZXJnZU5vZGUnLFxuICAnZmVNb3JwaG9sb2d5JyxcbiAgJ2ZlT2Zmc2V0JyxcbiAgJ2ZlUG9pbnRMaWdodCcsXG4gICdmZVNwZWN1bGFyTGlnaHRpbmcnLFxuICAnZmVTcG90TGlnaHQnLFxuICAnZmVUaWxlJyxcbiAgJ2ZlVHVyYnVsZW5jZScsXG4gICdmb3JlaWduT2JqZWN0JyxcbiAgJ2dseXBoUmVmJyxcbiAgJ2xpbmVhckdyYWRpZW50JyxcbiAgJ3JhZGlhbEdyYWRpZW50JyxcbiAgJ3NvbGlkQ29sb3InLFxuICAndGV4dEFyZWEnLFxuICAndGV4dFBhdGgnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n");

/***/ })

};
;