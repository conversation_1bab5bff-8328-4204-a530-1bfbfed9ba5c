import { EncryptionService } from './encryption.service';
export declare class SecureExampleController {
    private readonly encryptionService;
    constructor(encryptionService: EncryptionService);
    standardEncryption(data: any): {
        message: string;
        receivedData: any;
        timestamp: string;
        sessionType: string;
    };
    secureEncryption(data: any): {
        message: string;
        receivedData: any;
        timestamp: string;
        sessionType: string;
        warning: string;
    };
    securePartialEncryption(data: any): {
        message: string;
        publicInfo: string;
        sensitiveData: {
            cardNumber: string;
            cvv: string;
            expiryDate: string;
        };
        user: {
            name: string;
            email: string;
            creditCard: {
                type: string;
                lastFourDigits: string;
            };
        };
        timestamp: string;
    };
    getSessionStats(): {
        message: string;
    };
}
