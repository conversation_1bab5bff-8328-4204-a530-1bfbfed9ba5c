"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiVoiceprintRecognitionController = void 0;
const common_1 = require("@nestjs/common");
const ai_voiceprint_recognition_service_1 = require("./ai_voiceprint_recognition.service");
const swagger_1 = require("@nestjs/swagger");
const http_response_result_service_1 = require("../../web/http_response_result/http_response_result.service");
const scratch_config_service_1 = require("../config/scratch.config.service");
const user_point_service_1 = require("../../web/user_point/user_point.service");
const voiceprint_group_service_1 = require("../../util/database/mysql/voiceprint_group/voiceprint_group.service");
const voiceprint_group_entity_1 = require("../../util/database/mysql/voiceprint_group/entities/voiceprint_group.entity");
const voiceprint_feature_service_1 = require("../../util/database/mysql/voiceprint_feature/voiceprint_feature.service");
const voiceprint_feature_entity_1 = require("../../util/database/mysql/voiceprint_feature/entities/voiceprint-feature.entity");
let AiVoiceprintRecognitionController = class AiVoiceprintRecognitionController {
    aiVoiceprintRecognitionService;
    httpResponseResultService;
    scratchConfigService;
    userPointService;
    voiceprintGroupService;
    voiceprintFeatureService;
    constructor(aiVoiceprintRecognitionService, httpResponseResultService, scratchConfigService, userPointService, voiceprintGroupService, voiceprintFeatureService) {
        this.aiVoiceprintRecognitionService = aiVoiceprintRecognitionService;
        this.httpResponseResultService = httpResponseResultService;
        this.scratchConfigService = scratchConfigService;
        this.userPointService = userPointService;
        this.voiceprintGroupService = voiceprintGroupService;
        this.voiceprintFeatureService = voiceprintFeatureService;
    }
    async createGroup(token, body) {
        const { groupId, groupName, groupInfo, tabId } = body;
        console.log('创建声纹特征库请求参数:', { groupId, groupName, groupInfo, tabId });
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        if (!groupName) {
            return this.httpResponseResultService.error('特征库名称不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        console.log('解析的用户ID:', userId);
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            console.log('已存在任务:', userTabJobMapping);
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = this.scratchConfigService.getPointsConfig('voiceprint', 'create');
            console.log('积分配置:', points);
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            console.log('积分检查结果:', deductPoints);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            console.error('检查积分失败:', err);
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            groupName,
            groupInfo: groupInfo || '',
            userId,
            tabId
        };
        console.log('准备创建声纹特征库任务:', data);
        try {
            console.log('调用aiVoiceprintRecognitionService.createVoiceprintGroup');
            const result = await this.aiVoiceprintRecognitionService.createVoiceprintGroup(data);
            console.log('讯飞API创建声纹特征库结果:', result);
            console.log('准备保存到数据库');
            const createDto = {
                groupName: groupName,
                description: groupInfo || `声纹库 ${groupName} 由用户 ${userId} 创建`
            };
            console.log('创建DTO:', createDto);
            console.log('创建声纹特征库实体');
            const voiceprintGroupEntity = new voiceprint_group_entity_1.VoiceprintGroupEntity();
            voiceprintGroupEntity.userId = userId;
            voiceprintGroupEntity.groupId = groupId;
            voiceprintGroupEntity.groupName = groupName;
            voiceprintGroupEntity.description = groupInfo || `声纹库 ${groupName} 由用户 ${userId} 创建`;
            console.log('声纹特征库实体:', voiceprintGroupEntity);
            try {
                console.log('开始保存实体到数据库');
                if (!voiceprintGroupEntity.userId || !voiceprintGroupEntity.groupId || !voiceprintGroupEntity.groupName) {
                    console.error('实体缺少必要字段:', voiceprintGroupEntity);
                    throw new Error('实体缺少必要字段');
                }
                const existingGroup = await this.voiceprintGroupService.findByGroupId(groupId);
                if (existingGroup) {
                    console.log('数据库中已存在相同groupId的记录，跳过保存:', existingGroup);
                    return this.httpResponseResultService.success(result, '创建声纹特征库成功');
                }
                const savedEntity = await this.voiceprintGroupService['voiceprintGroupRepository'].save(voiceprintGroupEntity);
                console.log('保存到数据库成功:', savedEntity);
            }
            catch (dbError) {
                console.error('保存到数据库失败:', dbError);
                return this.httpResponseResultService.success({
                    ...result,
                    warning: '声纹特征库API创建成功，但数据库保存失败'
                }, '创建声纹特征库成功，但数据库同步失败');
            }
            return this.httpResponseResultService.success(result, '创建声纹特征库成功');
        }
        catch (error) {
            console.error('创建声纹特征库失败:', error);
            return this.httpResponseResultService.error(`创建声纹特征库失败: ${error.message}`);
        }
    }
    async createFeature(token, body) {
        const { groupId, featureId, featureInfo, audioBase64, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        if (!featureId) {
            return this.httpResponseResultService.error('特征ID不能为空');
        }
        if (!audioBase64) {
            return this.httpResponseResultService.error('音频数据不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = this.scratchConfigService.getPointsConfig('voiceprint', 'create');
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            featureId,
            featureInfo: featureInfo || '',
            audioBase64,
            userId,
            tabId
        };
        try {
            console.log(`开始创建声纹特征，groupId: ${groupId}, featureId: ${featureId}`);
            try {
                const library = await this.voiceprintGroupService.findByGroupId(groupId);
                if (!library) {
                    console.log(`声纹特征库 ${groupId} 不存在，创建失败`);
                    return this.httpResponseResultService.error(`特征库 ${groupId} 不存在，请先创建特征库`);
                }
                console.log(`声纹特征库 ${groupId} 存在，继续创建特征`);
            }
            catch (groupError) {
                console.error(`检查特征库失败: ${groupError.message}`);
                return this.httpResponseResultService.error(`检查特征库失败: ${groupError.message}`);
            }
            const result = await this.aiVoiceprintRecognitionService.createVoiceprintFeature(data);
            console.log('讯飞API创建声纹特征成功，准备保存到数据库');
            const voiceprintFeature = new voiceprint_feature_entity_1.VoiceprintFeature();
            voiceprintFeature.userId = userId;
            voiceprintFeature.groupId = groupId;
            voiceprintFeature.featureId = featureId;
            voiceprintFeature.name = featureInfo || `声纹特征 ${featureId}`;
            console.log('声纹特征对象创建成功:', voiceprintFeature);
            try {
                const savedFeature = await this.voiceprintFeatureService.create(voiceprintFeature);
                console.log('声纹特征保存到数据库成功:', savedFeature);
            }
            catch (dbError) {
                console.error('保存声纹特征到数据库失败:', dbError);
                return this.httpResponseResultService.success({
                    ...result,
                    warning: '声纹API调用成功，但数据库保存失败'
                }, '添加声纹特征成功，但数据库同步失败');
            }
            return this.httpResponseResultService.success(result, '添加声纹特征成功');
        }
        catch (error) {
            console.error('创建声纹特征失败:', error);
            return this.httpResponseResultService.error(`添加声纹特征失败: ${error.message}`);
        }
    }
    async compareFeature(token, body) {
        const { groupId, dstFeatureId, audioBase64, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        if (!dstFeatureId) {
            return this.httpResponseResultService.error('目标特征ID不能为空');
        }
        if (!audioBase64) {
            return this.httpResponseResultService.error('音频数据不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = this.scratchConfigService.getPointsConfig('voiceprint', 'compare');
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            dstFeatureId,
            audioBase64,
            userId,
            tabId
        };
        return this.aiVoiceprintRecognitionService.compareVoiceprint(data);
    }
    async searchFeature(token, body) {
        const { groupId, audioBase64, topK, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        if (!audioBase64) {
            return this.httpResponseResultService.error('音频数据不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = this.scratchConfigService.getPointsConfig('voiceprint', 'search');
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            audioBase64,
            topK: topK || 3,
            userId,
            tabId
        };
        return this.aiVoiceprintRecognitionService.searchVoiceprint(data);
    }
    async queryFeatureList(token, body) {
        const { groupId, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = { points: 0 };
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            userId,
            tabId
        };
        return this.aiVoiceprintRecognitionService.queryVoiceprintFeatureList(data);
    }
    async getUserVoiceprintLibraries(token) {
        try {
            const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
            const userId = tokenPayload.id;
            if (!userId) {
                return this.httpResponseResultService.error('没有找到用户id');
            }
            const libraries = await this.voiceprintGroupService.findByUserId(userId);
            return this.httpResponseResultService.success(libraries, '查询成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(`查询声纹特征库失败: ${error.message}`);
        }
    }
    async getJobStatus(jobId) {
        return this.aiVoiceprintRecognitionService.getJobStatus(jobId);
    }
    async updateFeature(token, body) {
        const { groupId, featureId, featureInfo, audioBase64, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        if (!featureId) {
            return this.httpResponseResultService.error('特征ID不能为空');
        }
        if (!audioBase64) {
            return this.httpResponseResultService.error('音频数据不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = this.scratchConfigService.getPointsConfig('voiceprint', 'create');
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            featureId,
            featureInfo: featureInfo || '',
            audioBase64,
            userId,
            tabId
        };
        return this.aiVoiceprintRecognitionService.updateVoiceprintFeature(data);
    }
    async deleteFeature(token, body) {
        const { groupId, featureId, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        if (!featureId) {
            return this.httpResponseResultService.error('特征ID不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        const userTabJobMapping = await this.aiVoiceprintRecognitionService.getUserTabJobMapping(userId);
        if (userTabJobMapping) {
            return this.httpResponseResultService.error('任务已存在');
        }
        try {
            const points = { points: 0 };
            const deductPoints = await this.userPointService.checkSufficientPackagePoints(userId, points.points);
            if (!deductPoints.sufficient) {
                return this.httpResponseResultService.error('积分不足');
            }
        }
        catch (err) {
            return this.httpResponseResultService.error('检查积分失败');
        }
        const data = {
            groupId,
            featureId,
            userId,
            tabId
        };
        try {
            const result = await this.aiVoiceprintRecognitionService.deleteVoiceprintFeature(data);
            try {
                const feature = await this.voiceprintFeatureService.findByFeatureIdAndGroupId(featureId, groupId);
                if (feature) {
                    await this.voiceprintFeatureService.remove(feature.id);
                }
            }
            catch (dbError) {
                console.error(`删除声纹特征数据库记录失败: ${dbError.message}`);
            }
            return result;
        }
        catch (error) {
            return this.httpResponseResultService.error(`删除声纹特征失败: ${error.message}`);
        }
    }
    async deleteGroup(token, body) {
        const { groupId, tabId } = body;
        if (!tabId) {
            return this.httpResponseResultService.error('没有找到标签页id');
        }
        if (!groupId) {
            return this.httpResponseResultService.error('特征库ID不能为空');
        }
        const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        const userId = tokenPayload.id;
        if (!userId) {
            return this.httpResponseResultService.error('没有找到用户id');
        }
        try {
            const libraries = await this.voiceprintGroupService.findByUserId(userId, { groupId });
            if (!libraries || libraries.length === 0) {
                return this.httpResponseResultService.error('未找到对应的声纹特征库');
            }
            const library = libraries[0];
            try {
                await this.voiceprintFeatureService.removeByGroupId(groupId);
            }
            catch (featuresError) {
                console.error(`删除特征库下声纹特征失败: ${featuresError.message}`);
            }
            const result = await this.voiceprintGroupService.delete(library.id);
            if (!result) {
                return this.httpResponseResultService.error('删除声纹特征库失败');
            }
            return this.httpResponseResultService.success(null, '删除声纹特征库成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(`删除声纹特征库失败: ${error.message}`);
        }
    }
    async getVoiceprintFeatures(token, groupId) {
        try {
            const tokenPayload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
            const userId = tokenPayload.id;
            if (!userId) {
                return this.httpResponseResultService.error('没有找到用户id');
            }
            if (!groupId) {
                return this.httpResponseResultService.error('特征库ID不能为空');
            }
            const features = await this.voiceprintFeatureService.findByGroupId(groupId);
            return this.httpResponseResultService.success(features, '查询成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(`查询声纹特征失败: ${error.message}`);
        }
    }
};
exports.AiVoiceprintRecognitionController = AiVoiceprintRecognitionController;
__decorate([
    (0, common_1.Post)('create-group'),
    (0, swagger_1.ApiOperation)({ summary: '创建声纹特征库', description: '创建一个新的声纹特征库' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'groupName', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID，自定义，用于后续操作' },
                groupName: { type: 'string', description: '特征库名称' },
                groupInfo: { type: 'string', description: '特征库附加信息' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "createGroup", null);
__decorate([
    (0, common_1.Post)('create-feature'),
    (0, swagger_1.ApiOperation)({ summary: '添加音频特征', description: '将声纹样本添加到特征库' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'featureId', 'audioBase64', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                featureId: { type: 'string', description: '特征ID，自定义，用于后续操作' },
                featureInfo: { type: 'string', description: '特征附加信息' },
                audioBase64: { type: 'string', description: 'Base64编码的音频数据' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "createFeature", null);
__decorate([
    (0, common_1.Post)('compare-feature'),
    (0, swagger_1.ApiOperation)({ summary: '声纹比对(1:1)', description: '将声纹样本与指定的特征进行1:1比对' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'dstFeatureId', 'audioBase64', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                dstFeatureId: { type: 'string', description: '目标特征ID' },
                audioBase64: { type: 'string', description: 'Base64编码的音频数据' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '比对成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "compareFeature", null);
__decorate([
    (0, common_1.Post)('search-feature'),
    (0, swagger_1.ApiOperation)({ summary: '声纹检索(1:N)', description: '将声纹样本在特征库中进行1:N检索' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'audioBase64', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                audioBase64: { type: 'string', description: 'Base64编码的音频数据' },
                topK: { type: 'number', description: '返回结果数量，默认为3' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检索成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "searchFeature", null);
__decorate([
    (0, common_1.Post)('query-feature-list'),
    (0, swagger_1.ApiOperation)({ summary: '查询特征列表', description: '查询特定特征库中的所有特征' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "queryFeatureList", null);
__decorate([
    (0, common_1.Get)('voiceprint-libraries'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前用户的声纹特征库列表', description: '查询当前用户的所有声纹特征库' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "getUserVoiceprintLibraries", null);
__decorate([
    (0, common_1.Get)(':jobId'),
    (0, swagger_1.ApiOperation)({ summary: '获取任务状态', description: '根据任务ID获取声纹识别任务的执行状态' }),
    (0, swagger_1.ApiParam)({ name: 'jobId', description: '任务ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回任务状态' }),
    __param(0, (0, common_1.Param)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "getJobStatus", null);
__decorate([
    (0, common_1.Post)('update-feature'),
    (0, swagger_1.ApiOperation)({ summary: '更新声纹特征', description: '更新指定的声纹特征' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'featureId', 'audioBase64', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                featureId: { type: 'string', description: '特征ID' },
                featureInfo: { type: 'string', description: '特征附加信息' },
                audioBase64: { type: 'string', description: 'Base64编码的音频数据' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "updateFeature", null);
__decorate([
    (0, common_1.Post)('delete-feature'),
    (0, swagger_1.ApiOperation)({ summary: '删除声纹特征', description: '删除指定的声纹特征' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'featureId', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                featureId: { type: 'string', description: '特征ID' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "deleteFeature", null);
__decorate([
    (0, common_1.Post)('delete-group'),
    (0, swagger_1.ApiOperation)({ summary: '删除声纹特征库', description: '删除指定的声纹特征库' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['groupId', 'tabId'],
            properties: {
                groupId: { type: 'string', description: '特征库ID' },
                tabId: { type: 'string', description: '标签页ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "deleteGroup", null);
__decorate([
    (0, common_1.Get)('voiceprint-features/:groupId'),
    (0, swagger_1.ApiOperation)({ summary: '获取特征库下的所有声纹特征', description: '查询特定声纹特征库下的所有声纹特征' }),
    (0, swagger_1.ApiParam)({ name: 'groupId', description: '特征库ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Param)('groupId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AiVoiceprintRecognitionController.prototype, "getVoiceprintFeatures", null);
exports.AiVoiceprintRecognitionController = AiVoiceprintRecognitionController = __decorate([
    (0, swagger_1.ApiTags)('Scratch/AI声纹识别'),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, common_1.Controller)('api/ai-voiceprint-recognition'),
    __metadata("design:paramtypes", [ai_voiceprint_recognition_service_1.AiVoiceprintRecognitionService,
        http_response_result_service_1.HttpResponseResultService,
        scratch_config_service_1.ScratchConfigService,
        user_point_service_1.UserPointService,
        voiceprint_group_service_1.VoiceprintGroupService,
        voiceprint_feature_service_1.VoiceprintFeatureService])
], AiVoiceprintRecognitionController);
//# sourceMappingURL=ai_voiceprint_recognition.controller.js.map