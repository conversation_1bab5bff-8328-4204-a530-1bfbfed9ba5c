2025-08-01 09:27:16.600 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 09:27:16.629 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.630 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.630 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.630 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.630 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.631 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.631 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.631 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.631 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.631 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.632 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.632 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.632 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.633 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.634 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.634 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.634 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.635 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.636 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:27:16.636 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY","stack":[null]}
2025-08-01 09:32:05.337 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:32:05.339 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:32:05.338Z"}
2025-08-01 09:32:59.511 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:32:59.512 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:32:59.512Z"}
2025-08-01 09:33:05.383 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:33:05.383 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:33:05.383Z"}
2025-08-01 09:34:05.393 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:34:05.394 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:34:05.393Z"}
2025-08-01 09:35:05.403 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:35:05.403 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:35:05.403Z"}
2025-08-01 09:36:06.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:36:06.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:36:06.236Z"}
2025-08-01 09:37:07.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:37:07.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:37:07.240Z"}
2025-08-01 09:38:08.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:38:08.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:38:08.247Z"}
2025-08-01 09:39:09.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:39:09.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:39:09.243Z"}
2025-08-01 09:40:10.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:40:10.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:40:10.249Z"}
2025-08-01 09:41:11.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:41:11.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:41:11.244Z"}
2025-08-01 09:42:12.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:42:12.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:42:12.249Z"}
2025-08-01 09:43:13.254 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:43:13.255 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:43:13.254Z"}
2025-08-01 09:44:14.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:44:14.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:44:14.248Z"}
2025-08-01 09:45:15.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:45:15.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:45:15.235Z"}
2025-08-01 09:46:16.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:46:16.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:46:16.241Z"}
2025-08-01 09:47:17.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:47:17.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:47:17.247Z"}
2025-08-01 09:48:18.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:48:18.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:48:18.245Z"}
2025-08-01 09:49:19.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:49:19.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:49:19.240Z"}
2025-08-01 09:50:20.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:50:20.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:50:20.237Z"}
2025-08-01 09:51:21.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:51:21.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:51:21.240Z"}
2025-08-01 09:52:22.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 09:52:22.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":4916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T01:52:22.243Z"}
2025-08-01 10:07:45.329 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:07:45.401 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.401 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.401 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.401 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.402 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.403 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.403 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.403 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.403 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.403 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.404 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:07:45.404 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY","stack":[null]}
2025-08-01 10:08:36.309 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:08:36.310 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:08:36.309Z"}
2025-08-01 10:09:37.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:09:37.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:09:37.246Z"}
2025-08-01 10:10:38.258 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:10:38.258 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:10:38.258Z"}
2025-08-01 10:11:39.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:11:39.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:11:39.239Z"}
2025-08-01 10:12:40.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:12:40.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:12:40.234Z"}
2025-08-01 10:13:41.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:13:41.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:13:41.245Z"}
2025-08-01 10:14:42.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:14:42.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:14:42.237Z"}
2025-08-01 10:15:43.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:15:43.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:15:43.243Z"}
2025-08-01 10:16:43.266 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:16:43.266 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:16:43.266Z"}
2025-08-01 10:17:43.278 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:17:43.279 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:17:43.279Z"}
2025-08-01 10:18:43.298 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:18:43.300 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:18:43.298Z"}
2025-08-01 10:19:43.314 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:19:43.314 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:19:43.314Z"}
2025-08-01 10:20:43.332 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:20:43.335 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:20:43.334Z"}
2025-08-01 10:21:43.361 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:21:43.361 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:21:43.361Z"}
2025-08-01 10:22:43.381 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:22:43.381 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:22:43.381Z"}
2025-08-01 10:23:43.401 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:23:43.402 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:23:43.402Z"}
2025-08-01 10:24:43.420 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:24:43.421 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:24:43.420Z"}
2025-08-01 10:25:43.446 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:25:43.447 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:25:43.446Z"}
2025-08-01 10:26:43.460 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:26:43.460 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:26:43.460Z"}
2025-08-01 10:27:43.472 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:27:43.473 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:27:43.472Z"}
2025-08-01 10:28:43.490 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:28:43.490 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:28:43.490Z"}
2025-08-01 10:29:43.504 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:29:43.505 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:29:43.505Z"}
2025-08-01 10:30:43.520 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:30:43.520 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:30:43.520Z"}
2025-08-01 10:31:43.537 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:31:43.538 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:31:43.537Z"}
2025-08-01 10:32:43.558 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:32:43.558 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:32:43.558Z"}
2025-08-01 10:33:43.573 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:33:43.574 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:33:43.573Z"}
2025-08-01 10:34:43.589 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:34:43.590 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:34:43.590Z"}
2025-08-01 10:35:43.610 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:35:43.612 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:35:43.610Z"}
2025-08-01 10:36:43.626 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:36:43.627 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:36:43.626Z"}
2025-08-01 10:37:43.640 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:37:43.640 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:37:43.640Z"}
2025-08-01 10:38:43.656 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:38:43.656 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:38:43.656Z"}
2025-08-01 10:39:43.673 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:39:43.673 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:39:43.673Z"}
2025-08-01 10:40:43.685 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:40:43.686 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:40:43.685Z"}
2025-08-01 10:41:43.700 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:41:43.701 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:41:43.700Z"}
2025-08-01 10:42:43.710 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:42:43.710 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:42:43.710Z"}
2025-08-01 10:43:43.722 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:43:43.723 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:43:43.722Z"}
2025-08-01 10:44:43.738 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:44:43.738 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:44:43.738Z"}
2025-08-01 10:45:43.755 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:45:43.756 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:45:43.755Z"}
2025-08-01 10:46:43.781 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:46:43.781 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:46:43.781Z"}
2025-08-01 10:47:43.795 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:47:43.796 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:47:43.795Z"}
2025-08-01 10:48:43.808 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:48:43.809 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:48:43.809Z"}
2025-08-01 10:49:43.827 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:49:43.828 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:49:43.827Z"}
2025-08-01 10:50:43.838 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:50:43.838 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:50:43.838Z"}
2025-08-01 10:51:43.851 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:51:43.851 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:51:43.851Z"}
2025-08-01 10:52:43.869 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:52:43.869 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:52:43.869Z"}
2025-08-01 10:53:43.885 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:53:43.885 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:53:43.885Z"}
2025-08-01 10:54:43.901 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:54:43.902 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:54:43.901Z"}
2025-08-01 10:55:43.915 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:55:43.917 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:55:43.915Z"}
2025-08-01 10:56:43.931 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:56:43.932 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:56:43.931Z"}
2025-08-01 10:57:43.946 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:57:43.947 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:57:43.946Z"}
2025-08-01 10:58:43.964 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:58:43.964 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:58:43.964Z"}
2025-08-01 10:59:43.978 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 10:59:43.980 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T02:59:43.979Z"}
2025-08-01 11:00:43.993 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:00:43.994 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:00:43.993Z"}
2025-08-01 11:01:44.010 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:01:44.010 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:01:44.010Z"}
2025-08-01 11:02:44.022 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:02:44.022 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:02:44.022Z"}
2025-08-01 11:03:44.038 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:03:44.038 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:03:44.038Z"}
2025-08-01 11:04:44.055 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:04:44.057 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:04:44.055Z"}
2025-08-01 11:05:44.074 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:05:44.077 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:05:44.075Z"}
2025-08-01 11:06:44.093 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:06:44.093 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:06:44.093Z"}
2025-08-01 11:07:44.108 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:07:44.108 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:07:44.108Z"}
2025-08-01 11:08:44.129 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:08:44.129 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:08:44.129Z"}
2025-08-01 11:09:44.141 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:09:44.141 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:09:44.141Z"}
2025-08-01 11:10:44.152 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:10:44.152 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:10:44.152Z"}
2025-08-01 11:11:44.168 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-01 11:11:44.168 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25916,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T03:11:44.168Z"}
