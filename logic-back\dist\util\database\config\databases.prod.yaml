mysql:
  type: mysql
  host: ************ # 可以修改为生产环境主机地址
  port: 13306
  username: logicleap
  password: "123456" # 生产环境密码
  database: logicleap # 生产环境数据库名
  charset: utf8mb4
  entities:
    - "dist/**/*.entity{.ts,.js}"
  synchronize: false # 生产环境禁用自动同步
  # 优化连接池配置
  extra:
    connectionLimit: 30        # 减少连接数，避免耗尽
    waitForConnections: true
    queueLimit: 100           # 减少队列限制
    acquireTimeout: 30000     # 减少获取连接超时时间
    timeout: 30000            # 减少查询超时时间
    reconnect: true           # 自动重连
    maxReconnects: 3          # 最大重连次数
    # 添加连接释放配置
    idleTimeout: 300000       # 空闲连接5分钟后释放
    evictInterval: 60000      # 每分钟检查一次空闲连接
    maxLifetime: 1800000      # 连接最大生存时间30分钟
  # 添加重试机制
  retryAttempts: 3
  retryDelay: 3000
  keepConnectionAlive: true
  connectTimeout: 30000

redis:
  host: ************
  port: 26379
  password: '12345678'
  db: 0