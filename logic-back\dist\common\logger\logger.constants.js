"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOG_CONFIG = exports.LOG_FILES = exports.LOG_CONTEXTS = exports.LOG_LEVELS = void 0;
exports.LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    HTTP: 'http',
    VERBOSE: 'verbose',
    DEBUG: 'debug',
    SILLY: 'silly'
};
exports.LOG_CONTEXTS = {
    HTTP: 'HTTP',
    DATABASE: 'Database',
    AUTH: 'Auth',
    PAYMENT: 'Payment',
    BUSINESS: 'Business',
    STARTUP: 'Startup',
    PERFORMANCE: 'Performance',
    SECURITY: 'Security',
    WEBSOCKET: 'WebSocket',
    SCHEDULER: 'Scheduler',
    CACHE: 'Cache',
    FILE: 'File',
    EMAIL: 'Email',
    SMS: 'SMS'
};
exports.LOG_FILES = {
    APPLICATION: 'application',
    ERROR: 'error',
    HTTP: 'http',
    DATABASE: 'database',
    SECURITY: 'security',
    PERFORMANCE: 'performance',
    EXCEPTIONS: 'exceptions',
    REJECTIONS: 'rejections'
};
exports.LOG_CONFIG = {
    development: {
        level: exports.LOG_LEVELS.DEBUG,
        console: true,
        file: true
    },
    dev: {
        level: exports.LOG_LEVELS.DEBUG,
        console: true,
        file: true
    },
    production: {
        level: exports.LOG_LEVELS.INFO,
        console: false,
        file: true
    },
    prod: {
        level: exports.LOG_LEVELS.INFO,
        console: false,
        file: true
    },
    test: {
        level: exports.LOG_LEVELS.ERROR,
        console: false,
        file: false
    }
};
//# sourceMappingURL=logger.constants.js.map