"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentNotifyHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentNotifyHandler = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_order_entity_1 = require("../../util/database/mysql/payment_order/entities/payment-order.entity");
const lock_manager_1 = require("../lock/lock.manager");
let PaymentNotifyHandler = PaymentNotifyHandler_1 = class PaymentNotifyHandler {
    paymentOrderRepository;
    lockManager;
    logger = new common_1.Logger(PaymentNotifyHandler_1.name);
    constructor(paymentOrderRepository, lockManager) {
        this.paymentOrderRepository = paymentOrderRepository;
        this.lockManager = lockManager;
    }
    async handlePaymentSuccess(orderNo, paymentId, notifyData) {
        try {
            return await this.lockManager.withDistributedLock(`payment:notify:${orderNo}`, async () => {
                const order = await this.paymentOrderRepository.findOne({
                    where: { businessOrderId: orderNo },
                });
                if (!order) {
                    this.logger.warn(`订单不存在: ${orderNo}`);
                    return false;
                }
                if (order.status === 'success') {
                    this.logger.log(`订单已处理成功，忽略重复通知: ${orderNo}`);
                    return true;
                }
                return await this.lockManager.withPessimisticRowLock('payment_order', { id: order.id }, async (manager) => {
                    await manager.update(payment_order_entity_1.PaymentOrder, { id: order.id }, {
                        status: 'success',
                        paidAt: new Date(),
                        channelOrderId: paymentId,
                        notifyData,
                    });
                    await this.processBusinessLogic(order);
                    return true;
                });
            }, 10000);
        }
        catch (error) {
            this.logger.error(`处理支付通知失败: ${error.message}`, error.stack);
            return false;
        }
    }
    async processBusinessLogic(order) {
        try {
            this.logger.log(`订单 ${order.businessOrderId} 支付成功，处理业务逻辑`);
        }
        catch (error) {
            this.logger.error(`处理业务逻辑失败: ${error.message}`, error.stack);
        }
    }
};
exports.PaymentNotifyHandler = PaymentNotifyHandler;
exports.PaymentNotifyHandler = PaymentNotifyHandler = PaymentNotifyHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_order_entity_1.PaymentOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        lock_manager_1.LockManager])
], PaymentNotifyHandler);
//# sourceMappingURL=payment-notify.handler.js.map