import { ActivityWorkService } from 'src/util/database/mysql/activity_work/activity_work.service';
import { ActivityWork } from 'src/util/database/mysql/activity_work/entities/activity_work.entity';
export declare class WebActivityWorkService {
    private readonly activityWorkService;
    constructor(activityWorkService: ActivityWorkService);
    addActivityWorks(activityId: number, works: {
        workId: number;
        userId?: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
        status?: number;
        contentType?: number;
        isImage?: boolean;
    }[]): Promise<boolean>;
    addImageToActivity(activityId: number, works: {
        workId: number;
        userId?: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
        status?: number;
        contentType?: number;
    }[]): Promise<boolean>;
    updateActivityWorks(activityId: number, works: {
        workId: number;
        userId?: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
        status?: number;
        contentType?: number;
    }[]): Promise<boolean>;
    checkUserSubmitted(activityId: number, userId?: number): Promise<boolean>;
    getActivityWorks(activityId: number, filters?: {
        isAwarded?: boolean;
        category?: string;
        userId?: number;
        status?: number;
        contentType?: number;
    }): Promise<ActivityWork[]>;
    deleteActivityWork(id: number): Promise<boolean>;
    deleteActivityWorks(activityId: number, workIds: number[]): Promise<boolean>;
    setWorkAwarded(id: number, isAwarded: boolean): Promise<boolean>;
    updateWorkCategory(id: number, category: string): Promise<boolean>;
    updateWorkStatus(id: number, status: number): Promise<boolean>;
    cancelUserSubmission(id: number, userId: number): Promise<boolean>;
}
