import { Repository } from 'typeorm';
import { CreateUserWorkLikeDto } from './dto/create-user_work_like.dto';
import { UpdateUserWorkLikeDto } from './dto/update-user_work_like.dto';
import { UserWorkLike } from './entities/user_work_like.entity';
export declare class UserWorkLikeService {
    private readonly userWorkLikeRepository;
    constructor(userWorkLikeRepository: Repository<UserWorkLike>);
    create(createUserWorkLikeDto: CreateUserWorkLikeDto): Promise<UserWorkLike>;
    findAll(): Promise<UserWorkLike[]>;
    findOne(id: number): Promise<UserWorkLike>;
    update(id: number, updateUserWorkLikeDto: UpdateUserWorkLikeDto): Promise<UserWorkLike>;
    remove(id: number): Promise<void>;
    findByUserIdAndTargetId(userId: number, targetId: number, targetType: number): Promise<UserWorkLike | null>;
    findByUserId(userId: number): Promise<UserWorkLike[]>;
    findByTargetId(targetId: number, targetType: number): Promise<UserWorkLike[]>;
    toggleLike(userId: number, targetId: number, targetType: number): Promise<UserWorkLike>;
}
