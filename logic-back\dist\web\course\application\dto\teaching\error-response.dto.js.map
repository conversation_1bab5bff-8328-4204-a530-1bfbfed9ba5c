{"version": 3, "file": "error-response.dto.js", "sourceRoot": "", "sources": ["../../../../../../src/web/course/application/dto/teaching/error-response.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAK9C,MAAa,0BAA0B;IAErC,MAAM,CAAS;IAGf,QAAQ,CAAS;IAGjB,OAAO,CAAS;IAGhB,OAAO,CAAS;IAGhB,UAAU,CAAS;CACpB;AAfD,gEAeC;AAbC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;;0DAC5D;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;4DACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;2DAClC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;2DACrD;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DACpC;AAMrB,MAAa,yBAAyB;IAEpC,MAAM,CAAS;IAGf,QAAQ,CAAS;IAGjB,OAAO,CAAS;IAGhB,SAAS,CAAS;IAGlB,gBAAgB,CAAS;IAGzB,iBAAiB,CAAS;CAC3B;AAlBD,8DAkBC;AAhBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;;yDAC/C;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;2DACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;0DAClC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;4DACjC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;mEAC7B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;oEAC9C;AAM5B,MAAa,qBAAqB;IAEhC,QAAQ,CAAS;IAGjB,MAAM,CAAS;CAChB;AAND,sDAMC;AAJC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;uDACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;;qDACnD;AAMjB,MAAa,6BAA6B;IAExC,MAAM,CAAS;IAGf,SAAS,CAAS;IAGlB,OAAO,CAAS;CACjB;AATD,sEASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;6DAC7C;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;gEACjC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DAClC;AAMlB,MAAa,iBAAiB;IAE5B,MAAM,CAAS;IAGf,OAAO,CAAS;IAGhB,YAAY,CAAS;CACtB;AATD,8CASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;;iDACjD;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;kDAClC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;uDAC5B;AAMvB,MAAa,yBAAyB;IAEpC,MAAM,CAAS;IAGf,QAAQ,CAAS;IAGjB,eAAe,CAAW;CAC3B;AATD,8DASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;;yDACnD;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;2DACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC;;kEACvD;AAM5B,MAAa,kBAAkB;IAE7B,SAAS,CAAS;IAGlB,KAAK,CAAS;IAGd,gBAAgB,CAAS;CAC1B;AATD,gDASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;qDAC7C;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;iDAC7C;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;4DAC5B;AAM3B,MAAa,wBAAwB;IAEnC,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,SAAS,CAAS;IAGlB,YAAY,CAAS;IAGrB,gBAAgB,CAAS;IAGzB,YAAY,CAAS;IAGrB,YAAY,CAAQ;IAGpB,gBAAgB,CAAuB;IAGvC,eAAe,CAAW;CAC3B;AA3BD,4DA2BC;AAzBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;4DAC9C;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;4DAC5C;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;2DAC3C;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DAC7B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;kEAC3B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;8DAC1C;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;8DACnC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;;kEAC7B;AAGvC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;;iEAC7B;AAM5B,MAAa,qBAAqB;IAEhC,OAAO,CAAU;IAGjB,gBAAgB,CAAS;IAGzB,eAAe,CAAS;IAGxB,YAAY,CAAS;IAGrB,eAAe,CAAU;IAGzB,aAAa,CAAS;IAGtB,eAAe,CAAS;IAGxB,kBAAkB,CAAS;IAG3B,OAAO,CAA2B;CACnC;AA3BD,sDA2BC;AAzBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;sDACnC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;+DAC3B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;8DAC9B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2DAC/B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;8DAC/B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;4DAClD;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DAC/B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iEAC9B;AAG3B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;8BAC5D,wBAAwB;sDAAC"}