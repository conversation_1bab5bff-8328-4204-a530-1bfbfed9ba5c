import { Activity } from '../../activity/entities/activity.entity';
export declare class ActivitySubmit {
    id: number;
    activityId: number;
    userId: number;
    userName: string;
    userPhone: string;
    agreementAccepted: boolean;
    parentConsentAccepted: boolean;
    parentSignaturePath: string;
    signatureTime: Date;
    signatureIp: string;
    status: number;
    reviewReason: string;
    reviewerId: number;
    reviewTime: Date;
    createTime: Date;
    updateTime: Date;
    isDelete: boolean;
    remark: string;
    activity: Activity;
}
