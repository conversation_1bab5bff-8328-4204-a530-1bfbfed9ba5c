"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceprintGroupModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const voiceprint_group_entity_1 = require("./entities/voiceprint_group.entity");
const voiceprint_group_service_1 = require("./voiceprint_group.service");
const voiceprint_group_controller_1 = require("./voiceprint_group.controller");
const xunfei_voiceprint_recognition_module_1 = require("../../../ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.module");
let VoiceprintGroupModule = class VoiceprintGroupModule {
};
exports.VoiceprintGroupModule = VoiceprintGroupModule;
exports.VoiceprintGroupModule = VoiceprintGroupModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([voiceprint_group_entity_1.VoiceprintGroupEntity]),
            (0, common_1.forwardRef)(() => xunfei_voiceprint_recognition_module_1.XunfeiVoiceprintRecognitionModule)
        ],
        controllers: [voiceprint_group_controller_1.VoiceprintGroupController],
        providers: [voiceprint_group_service_1.VoiceprintGroupService],
        exports: [voiceprint_group_service_1.VoiceprintGroupService]
    })
], VoiceprintGroupModule);
//# sourceMappingURL=voiceprint_group.module.js.map