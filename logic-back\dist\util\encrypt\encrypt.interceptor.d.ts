import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { EncryptionService } from './encryption.service';
export declare class EncryptInterceptor implements NestInterceptor {
    private reflector;
    private encryptionService;
    constructor(reflector: Reflector, encryptionService: EncryptionService);
    intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>>;
}
