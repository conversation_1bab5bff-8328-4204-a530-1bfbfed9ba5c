"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-parse5";
exports.ids = ["vendor-chunks/hast-util-to-parse5"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-parse5/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-to-parse5/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toParse5: () => (/* binding */ toParse5)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Doctype} Doctype\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').RootContent} RootContent\n * @typedef {import('hast').Text} Text\n *\n * @typedef {import('parse5').DefaultTreeAdapterMap['document']} Parse5Document\n * @typedef {import('parse5').DefaultTreeAdapterMap['documentFragment']} Parse5Fragment\n * @typedef {import('parse5').DefaultTreeAdapterMap['element']} Parse5Element\n * @typedef {import('parse5').DefaultTreeAdapterMap['node']} Parse5Nodes\n * @typedef {import('parse5').DefaultTreeAdapterMap['documentType']} Parse5Doctype\n * @typedef {import('parse5').DefaultTreeAdapterMap['commentNode']} Parse5Comment\n * @typedef {import('parse5').DefaultTreeAdapterMap['textNode']} Parse5Text\n * @typedef {import('parse5').DefaultTreeAdapterMap['parentNode']} Parse5Parent\n * @typedef {import('parse5').Token.Attribute} Parse5Attribute\n *\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {Space | null | undefined} [space='html']\n *   Which space the document is in (default: `'html'`).\n *\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it.\n *\n * @typedef {Exclude<Parse5Nodes, Parse5Document | Parse5Fragment>} Parse5Content\n *\n * @typedef {'html' | 'svg'} Space\n */\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\nconst own = {}.hasOwnProperty\n\nconst one = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {handlers: {root, element, text, comment, doctype}})\n\n/**\n * Transform a hast tree to a `parse5` AST.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Parse5Nodes}\n *   `parse5` node.\n */\nfunction toParse5(tree, options) {\n  const settings = options || emptyOptions\n  const space = settings.space\n  return one(tree, space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html)\n}\n\n/**\n * @param {Root} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Document}\n *   Parse5 node.\n */\nfunction root(node, schema) {\n  /** @type {Parse5Document} */\n  const result = {\n    nodeName: '#document',\n    // @ts-expect-error: `parse5` uses enums, which are actually strings.\n    mode: (node.data || {}).quirksMode ? 'quirks' : 'no-quirks',\n    childNodes: []\n  }\n  result.childNodes = all(node.children, result, schema)\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Root} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Fragment}\n *   Parse5 node.\n */\nfunction fragment(node, schema) {\n  /** @type {Parse5Fragment} */\n  const result = {nodeName: '#document-fragment', childNodes: []}\n  result.childNodes = all(node.children, result, schema)\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Doctype} node\n *   Node (hast) to transform.\n * @returns {Parse5Doctype}\n *   Parse5 node.\n */\nfunction doctype(node) {\n  /** @type {Parse5Doctype} */\n  const result = {\n    nodeName: '#documentType',\n    name: 'html',\n    publicId: '',\n    systemId: '',\n    parentNode: null\n  }\n\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Text} node\n *   Node (hast) to transform.\n * @returns {Parse5Text}\n *   Parse5 node.\n */\nfunction text(node) {\n  /** @type {Parse5Text} */\n  const result = {\n    nodeName: '#text',\n    value: node.value,\n    parentNode: null\n  }\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Comment} node\n *   Node (hast) to transform.\n * @returns {Parse5Comment}\n *   Parse5 node.\n */\nfunction comment(node) {\n  /** @type {Parse5Comment} */\n  const result = {\n    nodeName: '#comment',\n    data: node.value,\n    parentNode: null\n  }\n\n  patch(node, result)\n\n  return result\n}\n\n/**\n * @param {Element} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Element}\n *   Parse5 node.\n */\nfunction element(node, schema) {\n  const parentSchema = schema\n  let currentSchema = parentSchema\n\n  if (\n    node.type === 'element' &&\n    node.tagName.toLowerCase() === 'svg' &&\n    parentSchema.space === 'html'\n  ) {\n    currentSchema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n  }\n\n  /** @type {Array<Parse5Attribute>} */\n  const attrs = []\n  /** @type {string} */\n  let prop\n\n  if (node.properties) {\n    for (prop in node.properties) {\n      if (prop !== 'children' && own.call(node.properties, prop)) {\n        const result = createProperty(\n          currentSchema,\n          prop,\n          node.properties[prop]\n        )\n\n        if (result) {\n          attrs.push(result)\n        }\n      }\n    }\n  }\n\n  const space = currentSchema.space\n  // `html` and `svg` both have a space.\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(space)\n\n  /** @type {Parse5Element} */\n  const result = {\n    nodeName: node.tagName,\n    tagName: node.tagName,\n    attrs,\n    // @ts-expect-error: `parse5` types are wrong.\n    namespaceURI: web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces[space],\n    childNodes: [],\n    parentNode: null\n  }\n  result.childNodes = all(node.children, result, currentSchema)\n  patch(node, result)\n\n  if (node.tagName === 'template' && node.content) {\n    // @ts-expect-error: `parse5` types are wrong.\n    result.content = fragment(node.content, currentSchema)\n  }\n\n  return result\n}\n\n/**\n * Handle a property.\n *\n * @param {Schema} schema\n *   Current schema.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Parse5Attribute | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(schema, prop, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_4__.find)(schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === false ||\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value)) ||\n    (!value && info.boolean)\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value)\n  }\n\n  /** @type {Parse5Attribute} */\n  const attribute = {\n    name: info.attribute,\n    value: value === true ? '' : String(value)\n  }\n\n  if (info.space && info.space !== 'html' && info.space !== 'svg') {\n    const index = attribute.name.indexOf(':')\n\n    if (index < 0) {\n      attribute.prefix = ''\n    } else {\n      attribute.name = attribute.name.slice(index + 1)\n      attribute.prefix = info.attribute.slice(0, index)\n    }\n\n    attribute.namespace = web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces[info.space]\n  }\n\n  return attribute\n}\n\n/**\n * Transform all hast nodes.\n *\n * @param {Array<RootContent>} children\n *   List of children.\n * @param {Parse5Parent} parentNode\n *   `parse5` parent node.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Array<Parse5Content>}\n *   Transformed children.\n */\nfunction all(children, parentNode, schema) {\n  let index = -1\n  /** @type {Array<Parse5Content>} */\n  const results = []\n\n  if (children) {\n    while (++index < children.length) {\n      /** @type {Parse5Content} */\n      const child = one(children[index], schema)\n\n      child.parentNode = parentNode\n\n      results.push(child)\n    }\n  }\n\n  return results\n}\n\n/**\n * Add position info from `from` to `to`.\n *\n * @param {Nodes} from\n *   hast node.\n * @param {Parse5Nodes} to\n *   `parse5` node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  const position = from.position\n\n  if (position && position.start && position.end) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(typeof position.start.offset === 'number')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(typeof position.end.offset === 'number')\n\n    to.sourceCodeLocation = {\n      startLine: position.start.line,\n      startCol: position.start.column,\n      startOffset: position.start.offset,\n      endLine: position.end.line,\n      endCol: position.end.column,\n      endOffset: position.end.offset\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-parse5/lib/index.js\n");

/***/ })

};
;