"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeyManagementModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const key_management_service_1 = require("./key-management.service");
const redis_module_1 = require("../../database/redis/redis.module");
const schedule_1 = require("@nestjs/schedule");
let KeyManagementModule = class KeyManagementModule {
};
exports.KeyManagementModule = KeyManagementModule;
exports.KeyManagementModule = KeyManagementModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            redis_module_1.RedisModule,
            schedule_1.ScheduleModule.forRoot()
        ],
        providers: [key_management_service_1.KeyManagementService],
        exports: [key_management_service_1.KeyManagementService]
    })
], KeyManagementModule);
//# sourceMappingURL=key-management.module.js.map