import { ConnectionMonitorService } from './connection-monitor.service';
export declare class DatabaseHealthController {
    private readonly connectionMonitorService;
    constructor(connectionMonitorService: ConnectionMonitorService);
    getConnectionStatus(): Promise<{
        code: number;
        message: string;
        data: {
            totalConnections: any;
            freeConnections: any;
            acquiringConnections: any;
            connectionLimit: any;
            queueLimit: any;
            isConnected: boolean;
        };
        timestamp: string;
    } | {
        code: number;
        message: any;
        data: null;
        timestamp: string;
    }>;
    releaseIdleConnections(): Promise<{
        code: number;
        message: string;
        data: {
            releasedConnections: any;
        };
        timestamp: string;
    } | {
        code: number;
        message: any;
        data: null;
        timestamp: string;
    }>;
    healthCheck(): Promise<{
        code: number;
        message: string;
        data: {
            totalConnections: any;
            freeConnections: any;
            acquiringConnections: any;
            connectionLimit: any;
            queueLimit: any;
            isConnected: boolean;
            healthy: boolean;
            connectionUsage: string;
            usagePercentage: number;
        };
        timestamp: string;
    } | {
        code: number;
        message: string;
        data: {
            healthy: boolean;
        };
        timestamp: string;
    }>;
}
