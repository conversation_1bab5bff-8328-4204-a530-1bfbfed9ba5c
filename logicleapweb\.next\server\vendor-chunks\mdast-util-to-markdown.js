"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2Jsb2NrcXVvdGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxxQkFBcUI7QUFDakMsWUFBWSxrQkFBa0I7QUFDOUI7O0FBRUE7QUFDQSxXQUFXLFlBQVk7QUFDdkIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxLQUFLO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYmxvY2txdW90ZS5qcz9mNTIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QmxvY2txdW90ZSwgUGFyZW50c30gZnJvbSAnbWRhc3QnXG4gKiBAaW1wb3J0IHtJbmZvLCBNYXAsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtCbG9ja3F1b3RlfSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBibG9ja3F1b3RlKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcignYmxvY2txdW90ZScpXG4gIGNvbnN0IHRyYWNrZXIgPSBzdGF0ZS5jcmVhdGVUcmFja2VyKGluZm8pXG4gIHRyYWNrZXIubW92ZSgnPiAnKVxuICB0cmFja2VyLnNoaWZ0KDIpXG4gIGNvbnN0IHZhbHVlID0gc3RhdGUuaW5kZW50TGluZXMoXG4gICAgc3RhdGUuY29udGFpbmVyRmxvdyhub2RlLCB0cmFja2VyLmN1cnJlbnQoKSksXG4gICAgbWFwXG4gIClcbiAgZXhpdCgpXG4gIHJldHVybiB2YWx1ZVxufVxuXG4vKiogQHR5cGUge01hcH0gKi9cbmZ1bmN0aW9uIG1hcChsaW5lLCBfLCBibGFuaykge1xuICByZXR1cm4gJz4nICsgKGJsYW5rID8gJycgOiAnICcpICsgbGluZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */\n\n\n\n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1QixZQUFZLGFBQWE7QUFDekI7O0FBRTBEOztBQUUxRDtBQUNBLFdBQVcsT0FBTztBQUNsQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0seUVBQWM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYnJlYWsuanM/NTNiMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0JyZWFrLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbmltcG9ydCB7cGF0dGVybkluU2NvcGV9IGZyb20gJy4uL3V0aWwvcGF0dGVybi1pbi1zY29wZS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0JyZWFrfSBfXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFyZEJyZWFrKF8sIF8xLCBzdGF0ZSwgaW5mbykge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgc3RhdGUudW5zYWZlLmxlbmd0aCkge1xuICAgIC8vIElmIHdlIGNhbuKAmXQgcHV0IGVvbHMgaW4gdGhpcyBjb25zdHJ1Y3QgKHNldGV4dCBoZWFkaW5ncywgdGFibGVzKSwgdXNlIGFcbiAgICAvLyBzcGFjZSBpbnN0ZWFkLlxuICAgIGlmIChcbiAgICAgIHN0YXRlLnVuc2FmZVtpbmRleF0uY2hhcmFjdGVyID09PSAnXFxuJyAmJlxuICAgICAgcGF0dGVybkluU2NvcGUoc3RhdGUuc3RhY2ssIHN0YXRlLnVuc2FmZVtpbmRleF0pXG4gICAgKSB7XG4gICAgICByZXR1cm4gL1sgXFx0XS8udGVzdChpbmZvLmJlZm9yZSkgPyAnJyA6ICcgJ1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiAnXFxcXFxcbidcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(ssr)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */\n\n\n\n\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction code(node, _, state, info) {\n  const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */\n\n\n\n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction definition(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */\n\n\n\n\n\nemphasis.peek = emphasisPeek\n\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction emphasis(node, _, state, info) {\n  const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)\n  }\n\n  const after = tracker.move(marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @import {Html} from 'mdast'\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {Html} node\n * @returns {string}\n */\nfunction html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxNQUFNO0FBQ2xCOztBQUVBOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvaHRtbC5qcz80YjdiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SHRtbH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaHRtbC5wZWVrID0gaHRtbFBlZWtcblxuLyoqXG4gKiBAcGFyYW0ge0h0bWx9IG5vZGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBodG1sKG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUudmFsdWUgfHwgJydcbn1cblxuLyoqXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5mdW5jdGlvbiBodG1sUGVlaygpIHtcbiAgcmV0dXJuICc8J1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */\n\n\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction image(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */\nconst handle = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */\n\n\n\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction link(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? '<' : '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */\n\n\n\n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction listItem(node, parent, state, info) {\n  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)\n  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */\n\n\n\n\n\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? bullet === '.'\n      ? ')'\n      : '.'\n    : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state)\n  let useDifferentMarker =\n    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6QixZQUFZLGVBQWU7QUFDM0I7O0FBRW1EO0FBQ1c7QUFDSTtBQUNuQjs7QUFFL0M7QUFDQSxXQUFXLE1BQU07QUFDakIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsOEJBQThCLGlGQUFrQixVQUFVLGtFQUFXO0FBQ3JFLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sNkVBQWdCO0FBQ3RCO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFTO0FBQ2pCOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9saXN0LmpzPzZlODMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge0xpc3QsIFBhcmVudHN9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7Y2hlY2tCdWxsZXR9IGZyb20gJy4uL3V0aWwvY2hlY2stYnVsbGV0LmpzJ1xuaW1wb3J0IHtjaGVja0J1bGxldE90aGVyfSBmcm9tICcuLi91dGlsL2NoZWNrLWJ1bGxldC1vdGhlci5qcydcbmltcG9ydCB7Y2hlY2tCdWxsZXRPcmRlcmVkfSBmcm9tICcuLi91dGlsL2NoZWNrLWJ1bGxldC1vcmRlcmVkLmpzJ1xuaW1wb3J0IHtjaGVja1J1bGV9IGZyb20gJy4uL3V0aWwvY2hlY2stcnVsZS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0xpc3R9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGlzdChub2RlLCBwYXJlbnQsIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcignbGlzdCcpXG4gIGNvbnN0IGJ1bGxldEN1cnJlbnQgPSBzdGF0ZS5idWxsZXRDdXJyZW50XG4gIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICBsZXQgYnVsbGV0ID0gbm9kZS5vcmRlcmVkID8gY2hlY2tCdWxsZXRPcmRlcmVkKHN0YXRlKSA6IGNoZWNrQnVsbGV0KHN0YXRlKVxuICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgY29uc3QgYnVsbGV0T3RoZXIgPSBub2RlLm9yZGVyZWRcbiAgICA/IGJ1bGxldCA9PT0gJy4nXG4gICAgICA/ICcpJ1xuICAgICAgOiAnLidcbiAgICA6IGNoZWNrQnVsbGV0T3RoZXIoc3RhdGUpXG4gIGxldCB1c2VEaWZmZXJlbnRNYXJrZXIgPVxuICAgIHBhcmVudCAmJiBzdGF0ZS5idWxsZXRMYXN0VXNlZCA/IGJ1bGxldCA9PT0gc3RhdGUuYnVsbGV0TGFzdFVzZWQgOiBmYWxzZVxuXG4gIGlmICghbm9kZS5vcmRlcmVkKSB7XG4gICAgY29uc3QgZmlyc3RMaXN0SXRlbSA9IG5vZGUuY2hpbGRyZW4gPyBub2RlLmNoaWxkcmVuWzBdIDogdW5kZWZpbmVkXG5cbiAgICAvLyBJZiB0aGVyZeKAmXMgYW4gZW1wdHkgZmlyc3QgbGlzdCBpdGVtIGRpcmVjdGx5IGluIHR3byBsaXN0IGl0ZW1zLFxuICAgIC8vIHdlIGhhdmUgdG8gdXNlIGEgZGlmZmVyZW50IGJ1bGxldDpcbiAgICAvL1xuICAgIC8vIGBgYG1hcmtkb3duXG4gICAgLy8gKiAtICpcbiAgICAvLyBgYGBcbiAgICAvL1xuICAgIC8vIOKApmJlY2F1c2Ugb3RoZXJ3aXNlIGl0IHdvdWxkIGJlY29tZSBvbmUgYmlnIHRoZW1hdGljIGJyZWFrLlxuICAgIGlmIChcbiAgICAgIC8vIEJ1bGxldCBjb3VsZCBiZSB1c2VkIGFzIGEgdGhlbWF0aWMgYnJlYWsgbWFya2VyOlxuICAgICAgKGJ1bGxldCA9PT0gJyonIHx8IGJ1bGxldCA9PT0gJy0nKSAmJlxuICAgICAgLy8gRW1wdHkgZmlyc3QgbGlzdCBpdGVtOlxuICAgICAgZmlyc3RMaXN0SXRlbSAmJlxuICAgICAgKCFmaXJzdExpc3RJdGVtLmNoaWxkcmVuIHx8ICFmaXJzdExpc3RJdGVtLmNoaWxkcmVuWzBdKSAmJlxuICAgICAgLy8gRGlyZWN0bHkgaW4gdHdvIG90aGVyIGxpc3QgaXRlbXM6XG4gICAgICBzdGF0ZS5zdGFja1tzdGF0ZS5zdGFjay5sZW5ndGggLSAxXSA9PT0gJ2xpc3QnICYmXG4gICAgICBzdGF0ZS5zdGFja1tzdGF0ZS5zdGFjay5sZW5ndGggLSAyXSA9PT0gJ2xpc3RJdGVtJyAmJlxuICAgICAgc3RhdGUuc3RhY2tbc3RhdGUuc3RhY2subGVuZ3RoIC0gM10gPT09ICdsaXN0JyAmJlxuICAgICAgc3RhdGUuc3RhY2tbc3RhdGUuc3RhY2subGVuZ3RoIC0gNF0gPT09ICdsaXN0SXRlbScgJiZcbiAgICAgIC8vIFRoYXQgYXJlIGVhY2ggdGhlIGZpcnN0IGNoaWxkLlxuICAgICAgc3RhdGUuaW5kZXhTdGFja1tzdGF0ZS5pbmRleFN0YWNrLmxlbmd0aCAtIDFdID09PSAwICYmXG4gICAgICBzdGF0ZS5pbmRleFN0YWNrW3N0YXRlLmluZGV4U3RhY2subGVuZ3RoIC0gMl0gPT09IDAgJiZcbiAgICAgIHN0YXRlLmluZGV4U3RhY2tbc3RhdGUuaW5kZXhTdGFjay5sZW5ndGggLSAzXSA9PT0gMFxuICAgICkge1xuICAgICAgdXNlRGlmZmVyZW50TWFya2VyID0gdHJ1ZVxuICAgIH1cblxuICAgIC8vIElmIHRoZXJl4oCZcyBhIHRoZW1hdGljIGJyZWFrIGF0IHRoZSBzdGFydCBvZiB0aGUgZmlyc3QgbGlzdCBpdGVtLFxuICAgIC8vIHdlIGhhdmUgdG8gdXNlIGEgZGlmZmVyZW50IGJ1bGxldDpcbiAgICAvL1xuICAgIC8vIGBgYG1hcmtkb3duXG4gICAgLy8gKiAtLS1cbiAgICAvLyBgYGBcbiAgICAvL1xuICAgIC8vIOKApmJlY2F1c2Ugb3RoZXJ3aXNlIGl0IHdvdWxkIGJlY29tZSBvbmUgYmlnIHRoZW1hdGljIGJyZWFrLlxuICAgIGlmIChjaGVja1J1bGUoc3RhdGUpID09PSBidWxsZXQgJiYgZmlyc3RMaXN0SXRlbSkge1xuICAgICAgbGV0IGluZGV4ID0gLTFcblxuICAgICAgd2hpbGUgKCsraW5kZXggPCBub2RlLmNoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBpdGVtID0gbm9kZS5jaGlsZHJlbltpbmRleF1cblxuICAgICAgICBpZiAoXG4gICAgICAgICAgaXRlbSAmJlxuICAgICAgICAgIGl0ZW0udHlwZSA9PT0gJ2xpc3RJdGVtJyAmJlxuICAgICAgICAgIGl0ZW0uY2hpbGRyZW4gJiZcbiAgICAgICAgICBpdGVtLmNoaWxkcmVuWzBdICYmXG4gICAgICAgICAgaXRlbS5jaGlsZHJlblswXS50eXBlID09PSAndGhlbWF0aWNCcmVhaydcbiAgICAgICAgKSB7XG4gICAgICAgICAgdXNlRGlmZmVyZW50TWFya2VyID0gdHJ1ZVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBpZiAodXNlRGlmZmVyZW50TWFya2VyKSB7XG4gICAgYnVsbGV0ID0gYnVsbGV0T3RoZXJcbiAgfVxuXG4gIHN0YXRlLmJ1bGxldEN1cnJlbnQgPSBidWxsZXRcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5jb250YWluZXJGbG93KG5vZGUsIGluZm8pXG4gIHN0YXRlLmJ1bGxldExhc3RVc2VkID0gYnVsbGV0XG4gIHN0YXRlLmJ1bGxldEN1cnJlbnQgPSBidWxsZXRDdXJyZW50XG4gIGV4aXQoKVxuICByZXR1cm4gdmFsdWVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGFBQWE7QUFDekIsWUFBWSxvQkFBb0I7QUFDaEM7O0FBRUE7QUFDQSxXQUFXLFdBQVc7QUFDdEIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9wYXJhZ3JhcGguanM/YzcyZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyYWdyYXBoLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7UGFyYWdyYXBofSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJhZ3JhcGgobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdwYXJhZ3JhcGgnKVxuICBjb25zdCBzdWJleGl0ID0gc3RhdGUuZW50ZXIoJ3BocmFzaW5nJylcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5jb250YWluZXJQaHJhc2luZyhub2RlLCBpbmZvKVxuICBzdWJleGl0KClcbiAgZXhpdCgpXG4gIHJldHVybiB2YWx1ZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */\n\n\n\n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some(function (d) {\n    return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d)\n  })\n\n  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  return container.call(state, node, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6QixZQUFZLGVBQWU7QUFDM0I7O0FBRTRDOztBQUU1QztBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBLFdBQVcsNkRBQVE7QUFDbkIsR0FBRzs7QUFFSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanM/NzZkMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyZW50cywgUm9vdH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtwaHJhc2luZ30gZnJvbSAnbWRhc3QtdXRpbC1waHJhc2luZydcblxuLyoqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgLy8gTm90ZTogYGh0bWxgIG5vZGVzIGFyZSBhbWJpZ3VvdXMuXG4gIGNvbnN0IGhhc1BocmFzaW5nID0gbm9kZS5jaGlsZHJlbi5zb21lKGZ1bmN0aW9uIChkKSB7XG4gICAgcmV0dXJuIHBocmFzaW5nKGQpXG4gIH0pXG5cbiAgY29uc3QgY29udGFpbmVyID0gaGFzUGhyYXNpbmcgPyBzdGF0ZS5jb250YWluZXJQaHJhc2luZyA6IHN0YXRlLmNvbnRhaW5lckZsb3dcbiAgcmV0dXJuIGNvbnRhaW5lci5jYWxsKHN0YXRlLCBub2RlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */\n\n\n\n\n\nstrong.peek = strongPeek\n\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction strong(node, _, state, info) {\n  const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker + marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)\n  }\n\n  const after = tracker.move(marker + marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */\n\n/**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxhQUFhO0FBQ3pCLFlBQVksZUFBZTtBQUMzQjs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvdGV4dC5qcz9hYmFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBUZXh0fSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7VGV4dH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICByZXR1cm4gc3RhdGUuc2FmZShub2RlLnZhbHVlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */\n\n\n\n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction thematicBreak(_, _1, state) {\n  const value = (\n    (0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksd0JBQXdCO0FBQ3BDOztBQUVvRTtBQUNyQjs7QUFFL0M7QUFDQSxXQUFXLGVBQWU7QUFDMUIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSxJQUFJLDhEQUFTO0FBQ2IsV0FBVyxtRkFBbUI7O0FBRTlCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzPzEzYzgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFRoZW1hdGljQnJlYWt9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7Y2hlY2tSdWxlUmVwZXRpdGlvbn0gZnJvbSAnLi4vdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMnXG5pbXBvcnQge2NoZWNrUnVsZX0gZnJvbSAnLi4vdXRpbC9jaGVjay1ydWxlLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7VGhlbWF0aWNCcmVha30gX1xuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfMVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0aGVtYXRpY0JyZWFrKF8sIF8xLCBzdGF0ZSkge1xuICBjb25zdCB2YWx1ZSA9IChcbiAgICBjaGVja1J1bGUoc3RhdGUpICsgKHN0YXRlLm9wdGlvbnMucnVsZVNwYWNlcyA/ICcgJyA6ICcnKVxuICApLnJlcGVhdChjaGVja1J1bGVSZXBldGl0aW9uKHN0YXRlKSlcblxuICByZXR1cm4gc3RhdGUub3B0aW9ucy5ydWxlU3BhY2VzID8gdmFsdWUuc2xpY2UoMCwgLTEpIDogdmFsdWVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcz81NDhiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0T3JkZXJlZCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0T3JkZXJlZChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldE9yZGVyZWQgfHwgJy4nXG5cbiAgaWYgKG1hcmtlciAhPT0gJy4nICYmIG1hcmtlciAhPT0gJyknKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5idWxsZXRPcmRlcmVkYCwgZXhwZWN0ZWQgYC5gIG9yIGApYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBulletOther(state) {\n  const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3RoZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUU2Qzs7QUFFN0M7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUCxpQkFBaUIsNkRBQVc7QUFDNUI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LW90aGVyLmpzPzk3NDciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG5pbXBvcnQge2NoZWNrQnVsbGV0fSBmcm9tICcuL2NoZWNrLWJ1bGxldC5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXRPdGhlcihzdGF0ZSkge1xuICBjb25zdCBidWxsZXQgPSBjaGVja0J1bGxldChzdGF0ZSlcbiAgY29uc3QgYnVsbGV0T3RoZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldE90aGVyXG5cbiAgaWYgKCFidWxsZXRPdGhlcikge1xuICAgIHJldHVybiBidWxsZXQgPT09ICcqJyA/ICctJyA6ICcqJ1xuICB9XG5cbiAgaWYgKGJ1bGxldE90aGVyICE9PSAnKicgJiYgYnVsbGV0T3RoZXIgIT09ICcrJyAmJiBidWxsZXRPdGhlciAhPT0gJy0nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIGJ1bGxldE90aGVyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldE90aGVyYCwgZXhwZWN0ZWQgYCpgLCBgK2AsIG9yIGAtYCdcbiAgICApXG4gIH1cblxuICBpZiAoYnVsbGV0T3RoZXIgPT09IGJ1bGxldCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdFeHBlY3RlZCBgYnVsbGV0YCAoYCcgK1xuICAgICAgICBidWxsZXQgK1xuICAgICAgICAnYCkgYW5kIGBidWxsZXRPdGhlcmAgKGAnICtcbiAgICAgICAgYnVsbGV0T3RoZXIgK1xuICAgICAgICAnYCkgdG8gYmUgZGlmZmVyZW50J1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBidWxsZXRPdGhlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LmpzPzBlMmMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldCB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnKycgJiYgbWFya2VyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldGAsIGV4cGVjdGVkIGAqYCwgYCtgLCBvciBgLWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nfunction checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcz8xZGU2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snZW1waGFzaXMnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0VtcGhhc2lzKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuZW1waGFzaXMgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgZW1waGFzaXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5lbXBoYXNpc2AsIGV4cGVjdGVkIGAqYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nfunction checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcz83ZjJmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snZmVuY2UnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0ZlbmNlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuZmVuY2UgfHwgJ2AnXG5cbiAgaWYgKG1hcmtlciAhPT0gJ2AnICYmIG1hcmtlciAhPT0gJ34nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgY29kZSB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmZlbmNlYCwgZXhwZWN0ZWQgYGAgYCBgYCBvciBgfmAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nfunction checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'one'\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWxpc3QtaXRlbS1pbmRlbnQuanM/MjQ2MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2xpc3RJdGVtSW5kZW50J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tMaXN0SXRlbUluZGVudChzdGF0ZSkge1xuICBjb25zdCBzdHlsZSA9IHN0YXRlLm9wdGlvbnMubGlzdEl0ZW1JbmRlbnQgfHwgJ29uZSdcblxuICBpZiAoc3R5bGUgIT09ICd0YWInICYmIHN0eWxlICE9PSAnb25lJyAmJiBzdHlsZSAhPT0gJ21peGVkJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBzdHlsZSArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5saXN0SXRlbUluZGVudGAsIGV4cGVjdGVkIGB0YWJgLCBgb25lYCwgb3IgYG1peGVkYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gc3R5bGVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nfunction checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcz9mMzg4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncXVvdGUnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1F1b3RlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMucXVvdGUgfHwgJ1wiJ1xuXG4gIGlmIChtYXJrZXIgIT09ICdcIicgJiYgbWFya2VyICE9PSBcIidcIikge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHRpdGxlIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucXVvdGVgLCBleHBlY3RlZCBgXCJgLCBvciBgXFwnYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nfunction checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stcnVsZS1yZXBldGl0aW9uLmpzPzFlYWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydydWxlUmVwZXRpdGlvbiddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUnVsZVJlcGV0aXRpb24oc3RhdGUpIHtcbiAgY29uc3QgcmVwZXRpdGlvbiA9IHN0YXRlLm9wdGlvbnMucnVsZVJlcGV0aXRpb24gfHwgM1xuXG4gIGlmIChyZXBldGl0aW9uIDwgMykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHJ1bGVzIHdpdGggcmVwZXRpdGlvbiBgJyArXG4gICAgICAgIHJlcGV0aXRpb24gK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucnVsZVJlcGV0aXRpb25gLCBleHBlY3RlZCBgM2Agb3IgbW9yZSdcbiAgICApXG4gIH1cblxuICByZXR1cm4gcmVwZXRpdGlvblxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nfunction checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUuanM/NWU5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3J1bGUnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1J1bGUoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5ydWxlIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICctJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHJ1bGVzIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucnVsZWAsIGV4cGVjdGVkIGAqYCwgYC1gLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nfunction checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stc3Ryb25nLmpzP2YwZmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydzdHJvbmcnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1N0cm9uZyhzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnN0cm9uZyB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBzdHJvbmcgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5zdHJvbmdgLCBleHBlY3RlZCBgKmAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)\n/* harmony export */ });\n/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */\nfunction encodeCharacterReference(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLHFEQUFxRDtBQUNyRCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2VuY29kZS1jaGFyYWN0ZXItcmVmZXJlbmNlLmpzPzdhN2IiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFbmNvZGUgYSBjb2RlIHBvaW50IGFzIGEgY2hhcmFjdGVyIHJlZmVyZW5jZS5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gY29kZVxuICogICBDb2RlIHBvaW50IHRvIGVuY29kZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIEVuY29kZWQgY2hhcmFjdGVyIHJlZmVyZW5jZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuY29kZUNoYXJhY3RlclJlZmVyZW5jZShjb2RlKSB7XG4gIHJldHVybiAnJiN4JyArIGNvZGUudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKCkgKyAnOydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)\n/* harmony export */ });\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/**\n * @import {EncodeSides} from '../types.js'\n */\n\n\n\n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */\n// Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nfunction encodeInfo(outside, inside, marker) {\n  const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside)\n  const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside)\n\n  // Letter outside:\n  if (outsideKind === undefined) {\n    return insideKind === undefined\n      ? // Letter inside:\n        // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === '_'\n        ? {inside: true, outside: true}\n        : {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (letter, whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: encode outer (letter)\n          {inside: false, outside: true}\n  }\n\n  // Whitespace outside:\n  if (outsideKind === 1) {\n    return insideKind === undefined\n      ? // Letter inside: already forms.\n        {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: already forms.\n          {inside: false, outside: false}\n  }\n\n  // Punctuation outside:\n  return insideKind === undefined\n    ? // Letter inside: already forms.\n      {inside: false, outside: false}\n    : insideKind === 1\n      ? // Whitespace inside: encode inner (whitespace).\n        {inside: true, outside: false}\n      : // Punctuation inside: already forms.\n        {inside: false, outside: false}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatCodeAsIndented(node, state) {\n  return Boolean(\n    state.options.fences === false &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxNQUFNO0FBQ2xCOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcz9mM2MzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtDb2RlfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7Q29kZX0gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q29kZUFzSW5kZW50ZWQobm9kZSwgc3RhdGUpIHtcbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgc3RhdGUub3B0aW9ucy5mZW5jZXMgPT09IGZhbHNlICYmXG4gICAgICBub2RlLnZhbHVlICYmXG4gICAgICAvLyBJZiB0aGVyZeKAmXMgbm8gaW5mb+KAplxuICAgICAgIW5vZGUubGFuZyAmJlxuICAgICAgLy8gQW5kIHRoZXJl4oCZcyBhIG5vbi13aGl0ZXNwYWNlIGNoYXJhY3RlcuKAplxuICAgICAgL1teIFxcclxcbl0vLnRlc3Qobm9kZS52YWx1ZSkgJiZcbiAgICAgIC8vIEFuZCB0aGUgdmFsdWUgZG9lc27igJl0IHN0YXJ0IG9yIGVuZCBpbiBhIGJsYW5r4oCmXG4gICAgICAhL15bXFx0IF0qKD86W1xcclxcbl18JCl8KD86XnxbXFxyXFxuXSlbXFx0IF0qJC8udGVzdChub2RlLnZhbHVlKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function (node) {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtaGVhZGluZy1hcy1zZXRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQjs7QUFFNEM7QUFDQzs7QUFFN0M7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLEVBQUUsd0RBQUs7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxrREFBSTtBQUNqQjtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLE1BQU0sOERBQVE7QUFDZDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtaGVhZGluZy1hcy1zZXRleHQuanM/ZDk3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7SGVhZGluZ30gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtFWElULCB2aXNpdH0gZnJvbSAndW5pc3QtdXRpbC12aXNpdCdcbmltcG9ydCB7dG9TdHJpbmd9IGZyb20gJ21kYXN0LXV0aWwtdG8tc3RyaW5nJ1xuXG4vKipcbiAqIEBwYXJhbSB7SGVhZGluZ30gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0SGVhZGluZ0FzU2V0ZXh0KG5vZGUsIHN0YXRlKSB7XG4gIGxldCBsaXRlcmFsV2l0aEJyZWFrID0gZmFsc2VcblxuICAvLyBMb29rIGZvciBsaXRlcmFscyB3aXRoIGEgbGluZSBicmVhay5cbiAgLy8gTm90ZSB0aGF0IHRoaXMgYWxzb1xuICB2aXNpdChub2RlLCBmdW5jdGlvbiAobm9kZSkge1xuICAgIGlmIChcbiAgICAgICgndmFsdWUnIGluIG5vZGUgJiYgL1xccj9cXG58XFxyLy50ZXN0KG5vZGUudmFsdWUpKSB8fFxuICAgICAgbm9kZS50eXBlID09PSAnYnJlYWsnXG4gICAgKSB7XG4gICAgICBsaXRlcmFsV2l0aEJyZWFrID0gdHJ1ZVxuICAgICAgcmV0dXJuIEVYSVRcbiAgICB9XG4gIH0pXG5cbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgKCFub2RlLmRlcHRoIHx8IG5vZGUuZGVwdGggPCAzKSAmJlxuICAgICAgdG9TdHJpbmcobm9kZSkgJiZcbiAgICAgIChzdGF0ZS5vcHRpb25zLnNldGV4dCB8fCBsaXRlcmFsV2l0aEJyZWFrKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */\n\n\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatLinkAsAutolink(node, state) {\n  const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nfunction patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksdUJBQXVCO0FBQ25DOztBQUVBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakMsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsc0JBQXNCO0FBQ2pDLFdBQVcsdUJBQXVCO0FBQ2xDLFdBQVcsU0FBUztBQUNwQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvcGF0dGVybi1pbi1zY29wZS5qcz8wZDU2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7Q29uc3RydWN0TmFtZSwgVW5zYWZlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxDb25zdHJ1Y3ROYW1lPn0gc3RhY2tcbiAqIEBwYXJhbSB7VW5zYWZlfSBwYXR0ZXJuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhdHRlcm5JblNjb3BlKHN0YWNrLCBwYXR0ZXJuKSB7XG4gIHJldHVybiAoXG4gICAgbGlzdEluU2NvcGUoc3RhY2ssIHBhdHRlcm4uaW5Db25zdHJ1Y3QsIHRydWUpICYmXG4gICAgIWxpc3RJblNjb3BlKHN0YWNrLCBwYXR0ZXJuLm5vdEluQ29uc3RydWN0LCBmYWxzZSlcbiAgKVxufVxuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8Q29uc3RydWN0TmFtZT59IHN0YWNrXG4gKiBAcGFyYW0ge1Vuc2FmZVsnaW5Db25zdHJ1Y3QnXX0gbGlzdFxuICogQHBhcmFtIHtib29sZWFufSBub25lXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZnVuY3Rpb24gbGlzdEluU2NvcGUoc3RhY2ssIGxpc3QsIG5vbmUpIHtcbiAgaWYgKHR5cGVvZiBsaXN0ID09PSAnc3RyaW5nJykge1xuICAgIGxpc3QgPSBbbGlzdF1cbiAgfVxuXG4gIGlmICghbGlzdCB8fCBsaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBub25lXG4gIH1cblxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgbGlzdC5sZW5ndGgpIHtcbiAgICBpZiAoc3RhY2suaW5jbHVkZXMobGlzdFtpbmRleF0pKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ })

};
;