"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RefundNotifyHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundNotifyHandler = void 0;
const common_1 = require("@nestjs/common");
const create_payment_refund_dto_1 = require("../../util/database/mysql/payment_refund/dto/create-payment-refund.dto");
const alipay_strategy_1 = require("../strategies/alipay.strategy");
const wechat_pay_strategy_1 = require("../strategies/wechat-pay.strategy");
const payment_refund_service_1 = require("../../util/database/mysql/payment_refund/payment-refund.service");
const payment_log_service_1 = require("../../util/database/mysql/payment_log/payment-log.service");
const payment_log_dto_1 = require("../../util/database/mysql/payment_log/dto/payment-log.dto");
const lock_manager_1 = require("../lock/lock.manager");
const notify_service_1 = require("../notification/notify.service");
const create_payment_refund_dto_2 = require("../../util/database/mysql/payment_refund/dto/create-payment-refund.dto");
let RefundNotifyHandler = RefundNotifyHandler_1 = class RefundNotifyHandler {
    alipayStrategy;
    wechatPayStrategy;
    paymentRefundService;
    paymentLogService;
    lockManager;
    notifyService;
    logger = new common_1.Logger(RefundNotifyHandler_1.name);
    constructor(alipayStrategy, wechatPayStrategy, paymentRefundService, paymentLogService, lockManager, notifyService) {
        this.alipayStrategy = alipayStrategy;
        this.wechatPayStrategy = wechatPayStrategy;
        this.paymentRefundService = paymentRefundService;
        this.paymentLogService = paymentLogService;
        this.lockManager = lockManager;
        this.notifyService = notifyService;
    }
    async handleAlipayRefundNotify(data) {
        const startTime = Date.now();
        this.logger.log('收到支付宝退款回调通知');
        try {
            const verifyResult = await this.alipayStrategy.verifyNotify(data);
            if (!verifyResult.verified) {
                this.logger.warn('支付宝退款回调验证签名失败');
                return 'fail';
            }
            const outRefundNo = data.out_request_no;
            if (!outRefundNo) {
                this.logger.warn('支付宝退款回调缺少退款单号');
                return 'fail';
            }
            return this.lockManager.withDistributedLock(`refund:notify:alipay:${outRefundNo}`, async () => {
                try {
                    const refundRecord = await this.paymentRefundService.findByBusinessRefundId(outRefundNo);
                    if (!refundRecord) {
                        this.logger.warn(`找不到对应的退款记录: ${outRefundNo}`);
                        return 'fail';
                    }
                    if (refundRecord.status === create_payment_refund_dto_2.RefundStatus.SUCCESS || refundRecord.status === create_payment_refund_dto_2.RefundStatus.FAILED) {
                        this.logger.log(`退款记录已处理，状态: ${refundRecord.status}, 退款单号: ${outRefundNo}`);
                        return 'success';
                    }
                    const refundStatus = data.refund_status;
                    if (refundStatus === 'REFUND_SUCCESS') {
                        await this.paymentRefundService.updateStatus(refundRecord.id, create_payment_refund_dto_2.RefundStatus.SUCCESS, data);
                        await this.paymentLogService.create({
                            logType: payment_log_dto_1.LogType.REFUND,
                            operation: payment_log_dto_1.OperationType.NOTIFY,
                            orderNo: refundRecord.parameters?.originalOrderNo,
                            refundNo: outRefundNo,
                            paymentChannel: create_payment_refund_dto_1.PaymentChannel.ALIPAY,
                            requestData: data,
                            status: payment_log_dto_1.LogStatus.SUCCESS,
                            executionTime: Date.now() - startTime
                        });
                        await this.notifyService.handleRefundSuccess(outRefundNo, data.trade_no || '', refundRecord.amount, create_payment_refund_dto_1.PaymentChannel.ALIPAY, {
                            businessOrderId: refundRecord.parameters?.originalOrderNo,
                            userId: refundRecord.userId,
                        });
                        return 'success';
                    }
                    else {
                        await this.paymentRefundService.updateStatus(refundRecord.id, create_payment_refund_dto_2.RefundStatus.FAILED, data);
                        await this.paymentLogService.create({
                            logType: payment_log_dto_1.LogType.REFUND,
                            operation: payment_log_dto_1.OperationType.NOTIFY,
                            orderNo: refundRecord.parameters?.originalOrderNo,
                            refundNo: outRefundNo,
                            paymentChannel: create_payment_refund_dto_1.PaymentChannel.ALIPAY,
                            requestData: data,
                            status: payment_log_dto_1.LogStatus.FAIL,
                            errorMessage: `退款失败: ${refundStatus}`,
                            executionTime: Date.now() - startTime
                        });
                        await this.notifyService.handleRefundFail(outRefundNo, `退款失败: ${refundStatus}`, create_payment_refund_dto_1.PaymentChannel.ALIPAY, {
                            businessOrderId: refundRecord.parameters?.originalOrderNo,
                            userId: refundRecord.userId,
                        });
                        return 'success';
                    }
                }
                catch (error) {
                    this.logger.error(`处理支付宝退款回调异常: ${error.message}`, error.stack);
                    return 'fail';
                }
            }, 10000);
        }
        catch (error) {
            this.logger.error(`处理支付宝退款回调异常: ${error.message}`, error.stack);
            return 'fail';
        }
    }
    async handleWechatRefundNotify(data) {
        const startTime = Date.now();
        this.logger.log('收到微信支付退款回调通知');
        try {
            const verifyResult = await this.wechatPayStrategy.verifyNotify(data);
            if (!verifyResult.verified) {
                this.logger.warn('微信支付退款回调验证签名失败');
                return 'fail';
            }
            const decryptedData = verifyResult.rawNotify;
            if (!decryptedData || !decryptedData.out_refund_no) {
                this.logger.warn('微信支付退款回调数据异常');
                return 'fail';
            }
            const outRefundNo = decryptedData.out_refund_no;
            return this.lockManager.withDistributedLock(`refund:notify:wechat:${outRefundNo}`, async () => {
                try {
                    const refundRecord = await this.paymentRefundService.findByBusinessRefundId(outRefundNo);
                    if (!refundRecord) {
                        this.logger.warn(`找不到对应的退款记录: ${outRefundNo}`);
                        return 'fail';
                    }
                    if (refundRecord.status === create_payment_refund_dto_2.RefundStatus.SUCCESS || refundRecord.status === create_payment_refund_dto_2.RefundStatus.FAILED) {
                        this.logger.log(`退款记录已处理，状态: ${refundRecord.status}, 退款单号: ${outRefundNo}`);
                        return 'success';
                    }
                    const refundStatus = decryptedData.refund_status;
                    if (refundStatus === 'SUCCESS') {
                        await this.paymentRefundService.updateStatus(refundRecord.id, create_payment_refund_dto_2.RefundStatus.SUCCESS, decryptedData);
                        await this.paymentLogService.create({
                            logType: payment_log_dto_1.LogType.REFUND,
                            operation: payment_log_dto_1.OperationType.NOTIFY,
                            orderNo: refundRecord.parameters?.originalOrderNo,
                            refundNo: outRefundNo,
                            paymentChannel: create_payment_refund_dto_1.PaymentChannel.WECHAT,
                            requestData: decryptedData,
                            status: payment_log_dto_1.LogStatus.SUCCESS,
                            executionTime: Date.now() - startTime
                        });
                        await this.notifyService.handleRefundSuccess(outRefundNo, decryptedData.refund_id || '', refundRecord.amount, create_payment_refund_dto_1.PaymentChannel.WECHAT, {
                            businessOrderId: refundRecord.parameters?.originalOrderNo,
                            userId: refundRecord.userId,
                        });
                        return 'success';
                    }
                    else {
                        await this.paymentRefundService.updateStatus(refundRecord.id, create_payment_refund_dto_2.RefundStatus.FAILED, decryptedData);
                        await this.paymentLogService.create({
                            logType: payment_log_dto_1.LogType.REFUND,
                            operation: payment_log_dto_1.OperationType.NOTIFY,
                            orderNo: refundRecord.parameters?.originalOrderNo,
                            refundNo: outRefundNo,
                            paymentChannel: create_payment_refund_dto_1.PaymentChannel.WECHAT,
                            requestData: decryptedData,
                            status: payment_log_dto_1.LogStatus.FAIL,
                            errorMessage: `退款失败: ${refundStatus}`,
                            executionTime: Date.now() - startTime
                        });
                        await this.notifyService.handleRefundFail(outRefundNo, `退款失败: ${refundStatus}`, create_payment_refund_dto_1.PaymentChannel.WECHAT, {
                            businessOrderId: refundRecord.parameters?.originalOrderNo,
                            userId: refundRecord.userId,
                        });
                        return 'success';
                    }
                }
                catch (error) {
                    this.logger.error(`处理微信支付退款回调异常: ${error.message}`, error.stack);
                    return 'fail';
                }
            }, 10000);
        }
        catch (error) {
            this.logger.error(`处理微信支付退款回调异常: ${error.message}`, error.stack);
            return 'fail';
        }
    }
};
exports.RefundNotifyHandler = RefundNotifyHandler;
exports.RefundNotifyHandler = RefundNotifyHandler = RefundNotifyHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [alipay_strategy_1.AlipayStrategy,
        wechat_pay_strategy_1.WechatPayStrategy,
        payment_refund_service_1.PaymentRefundService,
        payment_log_service_1.PaymentLogService,
        lock_manager_1.LockManager,
        notify_service_1.NotifyService])
], RefundNotifyHandler);
//# sourceMappingURL=refund-notify.handler.js.map