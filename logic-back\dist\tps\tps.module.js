"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TPSModule = void 0;
const common_1 = require("@nestjs/common");
const user_info_module_1 = require("../util/database/mysql/user_info/user_info.module");
const user_student_module_1 = require("../util/database/mysql/user_student/user_student.module");
const user_school_relation_module_1 = require("../web/user_school_relation/user_school_relation.module");
const user_class_module_1 = require("../util/database/mysql/user_class/user_class.module");
const package_info_module_1 = require("../util/database/mysql/package_info/package_info.module");
const user_package_module_1 = require("../util/database/mysql/user_package/user_package.module");
const user_points_module_1 = require("../util/database/mysql/user_points/user_points.module");
const tps_controller_1 = require("./tps.controller");
const tps_service_1 = require("./tps.service");
const typeorm_1 = require("@nestjs/typeorm");
const user_info_entity_1 = require("../util/database/mysql/user_info/entities/user_info.entity");
let TPSModule = class TPSModule {
};
exports.TPSModule = TPSModule;
exports.TPSModule = TPSModule = __decorate([
    (0, common_1.Module)({
        imports: [
            user_info_module_1.UserInfoModule,
            user_student_module_1.UserStudentModule,
            user_school_relation_module_1.UserSchoolRelationModule,
            user_class_module_1.UserClassModule,
            package_info_module_1.PackageInfoModule,
            user_package_module_1.UserPackageModule,
            user_points_module_1.UserPointsModule,
            typeorm_1.TypeOrmModule.forFeature([user_info_entity_1.UserInfo])
        ],
        controllers: [tps_controller_1.TPSController],
        providers: [tps_service_1.TPSService],
        exports: [tps_service_1.TPSService],
    })
], TPSModule);
//# sourceMappingURL=tps.module.js.map