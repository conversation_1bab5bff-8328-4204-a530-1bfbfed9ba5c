import { Repository } from 'typeorm';
import { CreateActivitySubmitDto } from './dto/create-activity-submit.dto';
import { UpdateActivitySubmitDto } from './dto/update-activity-submit.dto';
import { ActivitySubmit } from './entities/activity_submit.entity';
import { Activity } from '../activity/entities/activity.entity';
export declare class ActivitySubmitService {
    private readonly activitySubmitRepository;
    private readonly activityRepository;
    constructor(activitySubmitRepository: Repository<ActivitySubmit>, activityRepository: Repository<Activity>);
    create(createActivitySubmitDto: CreateActivitySubmitDto): Promise<ActivitySubmit>;
    findAll(): Promise<ActivitySubmit[]>;
    findByActivity(activityId: number): Promise<ActivitySubmit[]>;
    findByUser(userId: number): Promise<ActivitySubmit[]>;
    findUserSubmitForActivity(activityId: number, userId: number): Promise<ActivitySubmit | null>;
    findUserSubmitForActivityAllStatus(activityId: number, userId: number): Promise<ActivitySubmit | null>;
    updateExistingSubmit(existingSubmit: ActivitySubmit, updateData: Partial<CreateActivitySubmitDto>): Promise<ActivitySubmit>;
    findOne(id: number): Promise<ActivitySubmit>;
    update(id: number, updateActivitySubmitDto: UpdateActivitySubmitDto): Promise<ActivitySubmit>;
    cancel(id: number): Promise<ActivitySubmit>;
    cancelByUser(activityId: number, userId: number): Promise<ActivitySubmit>;
    remove(id: number): Promise<void>;
    getSubmitStatistics(activityId: number): Promise<{
        total: number;
        submitted: number;
        cancelled: number;
        approved: number;
        rejected: number;
    }>;
}
