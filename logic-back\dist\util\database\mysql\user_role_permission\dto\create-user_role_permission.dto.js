"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserRolePermissionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateUserRolePermissionDto {
    roleId;
    permissionId;
}
exports.CreateUserRolePermissionDto = CreateUserRolePermissionDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '角色ID不能为空' }),
    (0, class_validator_1.IsNumber)(),
    (0, swagger_1.ApiProperty)({ description: '角色ID', example: 1 }),
    __metadata("design:type", Number)
], CreateUserRolePermissionDto.prototype, "roleId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '权限ID不能为空' }),
    (0, class_validator_1.IsNumber)(),
    (0, swagger_1.ApiProperty)({ description: '权限ID', example: 1 }),
    __metadata("design:type", Number)
], CreateUserRolePermissionDto.prototype, "permissionId", void 0);
//# sourceMappingURL=create-user_role_permission.dto.js.map