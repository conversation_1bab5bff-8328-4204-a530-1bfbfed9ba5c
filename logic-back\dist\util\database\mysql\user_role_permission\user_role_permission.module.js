"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRolePermissionModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_role_permission_service_1 = require("./user_role_permission.service");
const user_role_permission_controller_1 = require("./user_role_permission.controller");
const user_role_permission_entity_1 = require("./entities/user_role_permission.entity");
let UserRolePermissionModule = class UserRolePermissionModule {
};
exports.UserRolePermissionModule = UserRolePermissionModule;
exports.UserRolePermissionModule = UserRolePermissionModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([user_role_permission_entity_1.UserRolePermission])],
        controllers: [user_role_permission_controller_1.UserRolePermissionController],
        providers: [user_role_permission_service_1.UserRolePermissionService],
        exports: [user_role_permission_service_1.UserRolePermissionService],
    })
], UserRolePermissionModule);
//# sourceMappingURL=user_role_permission.module.js.map