import { ManagementService } from '../application/services/management/management.service';
import { CreateCourseSeriesDto, UpdateCourseSeriesDto } from '../application/dto/management/course-series.dto';
import { CreateCourseDto } from '../application/dto/management/course.dto';
import { CreateTaskTemplateDto } from '../application/dto/management/task-template.dto';
import { CourseSettingsDto } from '../application/dto/management/course-settings.dto';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
export declare class GetMyCourseSeriesQueryDto {
    page?: number;
    pageSize?: number;
    status?: number;
    keyword?: string;
}
export declare class GetSeriesCoursesQueryDto {
    page?: number;
    pageSize?: number;
    status?: number;
}
export declare class ManagementController {
    private readonly managementService;
    private readonly httpResponseResultService;
    constructor(managementService: ManagementService, httpResponseResultService: HttpResponseResultService);
    getMyCourseSeries(req: any, query: GetMyCourseSeriesQueryDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        list: {
            categoryLabel: string;
            statusLabel: string;
            contentSummary: any;
            id: number;
            title: string;
            description: string;
            coverImage: string;
            category: number;
            status: number;
            projectMembers: string;
            totalCourses: number;
            totalStudents: number;
            creatorId: number;
            createdAt: Date;
            updatedAt: Date;
            courses: import("../domain/entities/management/course.entity").Course[];
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>>;
    getSeriesCourses(req: any, seriesId: number, query: GetSeriesCoursesQueryDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        list: {
            statusLabel: string;
            videoDurationLabel: string;
            id: number;
            seriesId: number;
            title: string;
            description: string;
            coverImage: string;
            hasVideo: number;
            hasDocument: number;
            hasAudio: number;
            videoDuration: number;
            contentConfig: Record<string, any>;
            teachingInfo: Record<string, any>;
            additionalResources: Record<string, any>;
            orderIndex: number;
            status: number;
            creatorId: number;
            videoName: string;
            firstTeachingTitle: string;
            resourcesCount: number;
            createdAt: Date;
            updatedAt: Date;
            series: import("../domain/entities/management/course-series.entity").CourseSeries;
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>>;
    getSeriesDetail(id: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/management/course-series.entity").CourseSeries>>;
    createCourseSeries(req: any, courseSeriesData: CreateCourseSeriesDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/management/course-series.entity").CourseSeries>>;
    updateCourseSeries(req: any, id: number, updateData: UpdateCourseSeriesDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/management/course-series.entity").CourseSeries>>;
    deleteCourseSeries(req: any, id: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        success: boolean;
        message: string;
    }>>;
    publishCourseSeries(req: any, seriesId: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        id: number;
        title: string;
        status: number;
        statusLabel: string;
        publishedAt: string;
        totalCourses: number;
        publishedCourses: number;
        publishStats: {
            videoCourseCount: number;
            documentCourseCount: number;
            totalVideoDuration: number;
            totalResourcesCount: number;
        };
    }>>;
    createCourse(req: any, courseData: CreateCourseDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/management/course.entity").Course>>;
    setCourseSettings(req: any, courseId: number, settingsData: CourseSettingsDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        success: boolean;
        message: string;
        settings: import("../domain/entities/management/course-settings.entity").CourseSettings;
    }>>;
    addTaskTemplate(req: any, courseId: number, templateData: CreateTaskTemplateDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/teaching/task-template.entity").TaskTemplate>>;
    getCourseDetail(req: any, id: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/management/course.entity").Course>>;
    updateCourse(req: any, id: number, updateData: CreateCourseDto): Promise<import("../../http_response_result/http-response.interface").HttpResponse<import("../domain/entities/management/course.entity").Course>>;
    deleteCourse(req: any, id: number): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        success: boolean;
        message: string;
    }>>;
    updateCourseOrders(req: any, seriesId: number, body: {
        courseOrders: Array<{
            courseId: number;
            orderIndex: number;
        }>;
    }): Promise<import("../../http_response_result/http-response.interface").HttpResponse<{
        success: boolean;
        message: string;
    }>>;
}
