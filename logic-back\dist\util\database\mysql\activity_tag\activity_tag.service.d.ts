import { Repository } from 'typeorm';
import { CreateActivityTagDto } from './dto/create-activity_tag.dto';
import { UpdateActivityTagDto } from './dto/update-activity_tag.dto';
import { ActivityTag } from './entities/activity_tag.entity';
export declare class ActivityTagService {
    private readonly activityTagRepository;
    constructor(activityTagRepository: Repository<ActivityTag>);
    create(createActivityTagDto: CreateActivityTagDto): Promise<ActivityTag>;
    findAll(): Promise<ActivityTag[]>;
    findOne(id: number): Promise<ActivityTag>;
    update(id: number, updateActivityTagDto: UpdateActivityTagDto): Promise<ActivityTag>;
    remove(id: number): Promise<void>;
    hardRemove(id: number): Promise<void>;
    findByActivityId(activityId: number): Promise<ActivityTag[]>;
    findByTagId(tagId: number): Promise<ActivityTag[]>;
    removeByActivityId(activityId: number): Promise<void>;
    addActivityTags(activityId: number, tagIds: number[]): Promise<boolean>;
    updateActivityTags(activityId: number, tagIds: number[]): Promise<boolean>;
    deleteActivityTags(activityId: number): Promise<boolean>;
    getActivityTagIds(activityId: number): Promise<number[]>;
    getTagActivityIds(tagId: number): Promise<number[]>;
}
