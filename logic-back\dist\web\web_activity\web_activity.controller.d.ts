import { WebActivityService } from './web_activity.service';
import { Activity } from 'src/util/database/mysql/activity/entities/activity.entity';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class WebActivityController {
    private readonly webActivityService;
    private readonly httpResponseResultService;
    constructor(webActivityService: WebActivityService, httpResponseResultService: HttpResponseResultService);
    create(data: {
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage?: string;
        organizer: string;
        tagIds?: number[];
        activityType?: number;
        attachmentFiles?: string;
        promotionImage?: string;
        competitionGroups?: string;
        registrationForm?: string;
    }, currentUser: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<Activity>>;
    updateActivity(id: number, data: {
        name?: string;
        startTime?: Date;
        endTime?: Date;
        coverImage?: string;
        organizer?: string;
        tagIds?: number[];
        status?: number;
        activityType?: number;
        attachmentFiles?: string;
        promotionImage?: string;
        competitionGroups?: string;
        registrationForm?: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    deleteActivity(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    getActivityInfo(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<Activity>>;
    getActivityList(query: {
        page?: number;
        size?: number;
        status?: number;
        keyword?: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        list: Activity[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>>;
    getActivityWithWorks(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        works: import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[];
        id: number;
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage: string;
        organizer: string;
        creatorId: number;
        createTime: Date;
        updateTime: Date;
        status: number;
        activityType: number;
        isDelete: boolean;
        reviewReason: string;
        attachmentFiles: string;
        promotionImage: string;
        competitionGroups: string;
        registrationForm: string;
        activityTags: import("../../util/database/mysql/activity_tag/entities/activity_tag.entity").ActivityTag[];
        activityWorks: import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[];
    }>>;
    getActivityContentByType(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        contentType: number;
        id: number;
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage: string;
        organizer: string;
        creatorId: number;
        createTime: Date;
        updateTime: Date;
        status: number;
        activityType: number;
        isDelete: boolean;
        reviewReason: string;
        attachmentFiles: string;
        promotionImage: string;
        competitionGroups: string;
        registrationForm: string;
        activityTags: import("../../util/database/mysql/activity_tag/entities/activity_tag.entity").ActivityTag[];
        activityWorks: import("../../util/database/mysql/activity_work/entities/activity_work.entity").ActivityWork[];
    }>>;
    addWorksToActivity(activityId: number, data: {
        works: {
            workId: number;
            isAwarded?: boolean;
            category?: string;
            sort?: number;
        }[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    setAwardedWorks(activityId: number, data: {
        workIds: number[];
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    uploadSignature(data: {
        signatureData: string;
        activityId: number;
    }, currentUser: any, req: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        filePath: string;
        fileName: string;
        uploadTime: Date;
        userIp: string;
    }>>;
    submitRegistration(data: {
        activityId: number;
        agreementAccepted: boolean;
        parentConsentAccepted: boolean;
        parentSignaturePath: string;
        signatureTime?: string;
        signatureIp?: string;
        remark?: string;
    }, currentUser: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit>>;
    checkUserRegistration(activityId: number, currentUser: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        submitted: boolean | null;
        submit: import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit | undefined;
    }>>;
    cancelRegistration(activityId: number, currentUser: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit>>;
    getUserRegistrations(query: {
        page?: number;
        size?: number;
    }, currentUser: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        list: import("../../util/database/mysql/activity_submit/entities/activity_submit.entity").ActivitySubmit[];
        total: number;
        page: number;
        size: number;
        totalPages: number;
    }>>;
    getRegistrationStatistics(activityId: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        total: number;
        submitted: number;
        cancelled: number;
        approved: number;
        rejected: number;
    }>>;
}
