import { Repository, DataSource } from 'typeorm';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { TaskTemplate } from '../../../domain/entities/teaching/task-template.entity';
import { CourseSettings } from '../../../domain/entities/management/course-settings.entity';
import { CourseSeriesTag } from '../../../domain/entities/marketplace/course-series-tag.entity';
import { CourseTag } from '../../../domain/entities/marketplace/course-tag.entity';
export declare class ManagementService {
    private readonly courseSeriesRepository;
    private readonly courseRepository;
    private readonly taskTemplateRepository;
    private readonly courseSettingsRepository;
    private readonly courseSeriesTagRepository;
    private readonly courseTagRepository;
    private readonly dataSource;
    constructor(courseSeriesRepository: Repository<CourseSeries>, courseRepository: Repository<Course>, taskTemplateRepository: Repository<TaskTemplate>, courseSettingsRepository: Repository<CourseSettings>, courseSeriesTagRepository: Repository<CourseSeriesTag>, courseTagRepository: Repository<CourseTag>, dataSource: DataSource);
    getMyCourseSeries(userId: number, page: number, pageSize: number, status?: number, keyword?: string): Promise<{
        items: CourseSeries[];
        page: number;
        pageSize: number;
        total: number;
        totalPages: number;
    }>;
    findCourseSeriesById(id: number): Promise<CourseSeries>;
    createCourseSeries(courseSeriesData: any): Promise<CourseSeries>;
    updateCourseSeries(id: number, updateData: any, userId?: number): Promise<CourseSeries>;
    removeCourseSeries(id: number, userId?: number): Promise<{
        success: boolean;
        message: string;
    }>;
    publishCourseSeries(seriesId: number, userId: number): Promise<{
        success: boolean;
        message: string;
        data: CourseSeries;
        publishStats: {
            videoCourseCount: number;
            documentCourseCount: number;
            totalVideoDuration: number;
            totalResourcesCount: number;
            publishedCourses: number;
            totalCourses: number;
        };
    }>;
    private getCourseSeriesPublishStats;
    getSeriesCourses(seriesId: number, status?: number, page?: number, pageSize?: number, userId?: number): Promise<{
        seriesId: number;
        courses: Course[];
        total: number;
        page: number;
        pageSize: number;
    }>;
    createCourse(courseData: any): Promise<Course>;
    private updateSeriesCourseCount;
    private updateSeriesCourseCountWithManager;
    setCourseSettings(courseId: number, settingsData: any, userId: number): Promise<{
        success: boolean;
        message: string;
        settings: CourseSettings;
    }>;
    addTaskTemplate(courseId: number, templateData: any, userId: number): Promise<TaskTemplate>;
    findCourseById(id: number, userId?: number): Promise<Course>;
    updateCourse(id: number, updateData: any, userId: number): Promise<Course>;
    removeCourse(id: number, userId: number): Promise<{
        success: boolean;
        message: string;
    }>;
    updateCourseOrders(seriesId: number, courseOrders: Array<{
        courseId: number;
        orderIndex: number;
    }>, userId: number): Promise<{
        success: boolean;
        message: string;
    }>;
}
