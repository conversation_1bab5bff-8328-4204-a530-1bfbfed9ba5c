"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const teaching_service_1 = require("../application/services/teaching/teaching.service");
const teaching_exceptions_1 = require("../domain/exceptions/teaching/teaching.exceptions");
const one_click_start_dto_1 = require("../application/dto/teaching/one-click-start.dto");
const course_settings_dto_1 = require("../application/dto/teaching/course-settings.dto");
const teaching_records_dto_1 = require("../application/dto/teaching/teaching-records.dto");
const current_user_decorator_1 = require("../../../common/decorators/current-user.decorator");
const http_response_result_service_1 = require("../../http_response_result/http_response_result.service");
let TeachingController = class TeachingController {
    teachingService;
    httpResponseResultService;
    constructor(teachingService, httpResponseResultService) {
        this.teachingService = teachingService;
        this.httpResponseResultService = httpResponseResultService;
    }
    handleTeachingException(error) {
        if (error instanceof teaching_exceptions_1.ConcurrencyConflictException ||
            error instanceof teaching_exceptions_1.DuplicateOperationException ||
            error instanceof teaching_exceptions_1.CourseNotFoundOrNotPublishedException ||
            error instanceof teaching_exceptions_1.InsufficientTeacherPermissionException ||
            error instanceof teaching_exceptions_1.EmptyClassException ||
            error instanceof teaching_exceptions_1.IncompleteSettingsException ||
            error instanceof teaching_exceptions_1.PartialFailureException ||
            error instanceof teaching_exceptions_1.InsufficientStudentPointsException) {
            const response = error.getResponse();
            return {
                code: response.code,
                msg: response.message,
                data: response.data
            };
        }
        console.error('未处理的异常:', error);
        return this.httpResponseResultService.custom(500, '服务器内部错误，请稍后重试', null);
    }
    async oneClickStart(dto, currentUser) {
        try {
            console.log("currentUser", currentUser);
            const teacherId = currentUser?.id;
            if (!teacherId) {
                throw new common_1.BadRequestException('无法获取当前用户信息，请重新登录');
            }
            console.log("进入一键上课接口调用");
            const result = await this.teachingService.oneClickStart(dto, teacherId);
            return this.httpResponseResultService.success(result, '课程已成功开启！');
        }
        catch (error) {
            return this.handleTeachingException(error);
        }
    }
    async getCourseSettings(courseId) {
        try {
            const courseIdNum = parseInt(courseId, 10);
            if (isNaN(courseIdNum) || courseIdNum <= 0) {
                throw new common_1.BadRequestException('课程ID必须是有效的正整数');
            }
            const result = await this.teachingService.getCourseSettings(courseIdNum);
            return this.httpResponseResultService.success(result, 'success');
        }
        catch (error) {
            throw error;
        }
    }
    async getTeachingRecords(query) {
        try {
            const result = await this.teachingService.getTeachingRecords(query);
            return this.httpResponseResultService.success(result, 'success');
        }
        catch (error) {
            throw error;
        }
    }
};
exports.TeachingController = TeachingController;
__decorate([
    (0, common_1.Post)('one-click-start'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '一键上课',
        description: '为指定班级开启课程，自动分配积分、应用模板、创建任务'
    }),
    (0, swagger_1.ApiBody)({
        type: one_click_start_dto_1.OneClickStartDto,
        description: '一键上课请求参数',
        examples: {
            example1: {
                summary: '基本示例',
                description: '为班级10开启课程3216的教学',
                value: {
                    courseId: 3216,
                    classId: 63
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '课程开启成功',
        type: one_click_start_dto_1.OneClickStartResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: '课程已成功开启！',
                    data: {
                        success: true,
                        teachingRecordId: 15,
                        pointsAllocated: 3000,
                        tasksCreated: 2,
                        templateApplied: true,
                        executionTime: '2024-01-26T15:30:00Z',
                        lockAcquireTime: 45,
                        totalExecutionTime: 2350,
                        details: {
                            courseName: '第一课：Node.js基础入门',
                            seriesName: 'Node.js后端开发系列',
                            className: '软件工程2024-1班',
                            studentCount: 30,
                            pointsPerStudent: 100,
                            templateName: 'Node.js开发环境模板',
                            createdTasks: [
                                {
                                    taskId: 101,
                                    taskName: 'Node.js基础练习',
                                    startDate: '2024-01-26T15:30:00Z',
                                    endDate: '2024-02-02T23:59:59Z',
                                    assignedStudents: 30
                                },
                                {
                                    taskId: 102,
                                    taskName: 'HTTP服务器实践',
                                    startDate: '2024-01-26T15:30:00Z',
                                    endDate: '2024-01-31T23:59:59Z',
                                    assignedStudents: 30
                                }
                            ],
                            failedOperations: [],
                            warningMessages: []
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        examples: {
            badRequest: {
                summary: '参数错误',
                value: {
                    code: 400,
                    message: '课程ID不能为空',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '权限不足',
        examples: {
            forbidden: {
                summary: '教师权限不足',
                value: {
                    code: 403,
                    message: '权限不足',
                    data: {
                        reason: '您没有权限操作此班级',
                        teacherId: 100,
                        classId: 10
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 405,
        description: '重复操作',
        examples: {
            duplicate: {
                summary: '重复操作',
                value: {
                    code: 405,
                    message: '该课程已在此班级开启，请勿重复操作',
                    data: {
                        reason: '检测到重复的一键上课操作',
                        courseId: 25,
                        classId: 10,
                        teacherId: 100,
                        existingRecordId: 12,
                        lastExecutionTime: '2024-01-26T15:25:00Z'
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '并发冲突',
        examples: {
            conflict: {
                summary: '并发冲突',
                value: {
                    code: 409,
                    message: '系统繁忙，请稍后重试',
                    data: {
                        reason: '无法获取操作锁，可能有其他用户正在为此班级开启课程',
                        courseId: 25,
                        classId: 10,
                        lockKey: 'course-teaching:25:10',
                        retryAfter: 30
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 422,
        description: '业务逻辑错误',
        examples: {
            emptyClass: {
                summary: '班级无学生',
                value: {
                    code: 422,
                    message: '业务逻辑错误',
                    data: {
                        reason: '班级中没有学生，无法开启课程',
                        classId: 10,
                        studentCount: 0
                    }
                }
            },
            incompleteSettings: {
                summary: '课程设置不完整',
                value: {
                    code: 422,
                    message: '业务逻辑错误',
                    data: {
                        reason: '课程设置不完整，请先完成课程配置',
                        courseId: 25,
                        missingSettings: ['templateId', 'taskTemplates']
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 206,
        description: '部分操作失败',
        examples: {
            partialFailure: {
                summary: '部分操作失败',
                value: {
                    code: 206,
                    message: '课程开启完成，但部分操作失败',
                    data: {
                        success: true,
                        teachingRecordId: 16,
                        pointsAllocated: 2700,
                        tasksCreated: 2,
                        templateApplied: false,
                        executionTime: '2024-01-26T15:35:00Z',
                        lockAcquireTime: 32,
                        totalExecutionTime: 1850,
                        details: {
                            courseName: '第一课：Node.js基础入门',
                            seriesName: 'Node.js后端开发系列',
                            className: '软件工程2024-1班',
                            studentCount: 30,
                            pointsPerStudent: 100,
                            templateName: 'Node.js开发环境模板',
                            createdTasks: [],
                            failedOperations: [
                                {
                                    operation: 'applyTemplate',
                                    error: '模板服务暂时不可用',
                                    affectedStudents: 3
                                }
                            ],
                            warningMessages: [
                                '3名学生的模板应用失败，请稍后手动处理'
                            ]
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '资源不存在',
        examples: {
            notFound: {
                summary: '课程不存在',
                value: {
                    code: 404,
                    message: '课程不存在',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [one_click_start_dto_1.OneClickStartDto, Object]),
    __metadata("design:returntype", Promise)
], TeachingController.prototype, "oneClickStart", null);
__decorate([
    (0, common_1.Get)('course-settings/:courseId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '获取课程设置信息',
        description: '获取课程的设置信息，用于一键上课前的预览'
    }),
    (0, swagger_1.ApiParam)({
        name: 'courseId',
        description: '课程ID',
        type: 'number',
        example: 25
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: course_settings_dto_1.CourseSettingsResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: 'success',
                    data: {
                        courseId: 25,
                        courseName: '第一课：Node.js基础入门',
                        seriesName: 'Node.js后端开发系列',
                        contentInfo: {
                            hasVideo: 1,
                            hasDocument: 1,
                            hasAudio: 0,
                            videoDuration: 3600,
                            videoDurationLabel: '60分钟',
                            videoName: 'Node.js基础入门讲解.mp4',
                            resourcesCount: 3
                        },
                        settings: {
                            templateId: 5,
                            templateName: 'Node.js开发环境模板',
                            requiredPoints: 100,
                            autoCreateTasks: 1,
                            autoCreateTasksLabel: '是'
                        },
                        taskTemplates: [
                            {
                                id: 8,
                                taskName: 'Node.js基础练习',
                                taskDescription: '创建一个简单的Node.js应用，实现文件读写和HTTP服务器功能',
                                durationDays: 7,
                                status: 1,
                                statusLabel: '启用',
                                attachmentsCount: 2,
                                assessmentItemsCount: 3,
                                firstAttachmentType: 'document'
                            },
                            {
                                id: 9,
                                taskName: 'HTTP服务器实践',
                                taskDescription: '实现一个基础的HTTP服务器，支持GET和POST请求',
                                durationDays: 5,
                                status: 1,
                                statusLabel: '启用',
                                attachmentsCount: 1,
                                assessmentItemsCount: 2,
                                firstAttachmentType: 'file'
                            }
                        ],
                        preview: {
                            willAllocatePoints: true,
                            pointsPerStudent: 100,
                            willApplyTemplate: true,
                            willCreateTasks: true,
                            tasksCount: 2
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        examples: {
            badRequest: {
                summary: '课程不存在',
                value: {
                    code: 400,
                    message: '课程不存在',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    __param(0, (0, common_1.Param)('courseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeachingController.prototype, "getCourseSettings", null);
__decorate([
    (0, common_1.Get)('records'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '查询教学记录',
        description: '查询课程教学记录，支持按教师、课程、班级等条件筛选'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        description: '页码，默认1',
        type: 'number',
        required: false,
        example: 1
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        description: '每页数量，默认10',
        type: 'number',
        required: false,
        example: 10
    }),
    (0, swagger_1.ApiQuery)({
        name: 'teacherId',
        description: '教师ID筛选',
        type: 'number',
        required: false,
        example: 100
    }),
    (0, swagger_1.ApiQuery)({
        name: 'courseId',
        description: '课程ID筛选',
        type: 'number',
        required: false,
        example: 25
    }),
    (0, swagger_1.ApiQuery)({
        name: 'classId',
        description: '班级ID筛选',
        type: 'number',
        required: false,
        example: 10
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        description: '状态筛选：0=进行中，1=成功，2=失败',
        type: 'number',
        required: false,
        example: 1,
        enum: [0, 1, 2]
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: '开始日期筛选',
        type: 'string',
        required: false,
        example: '2024-01-01'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: '结束日期筛选',
        type: 'string',
        required: false,
        example: '2024-01-31'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: teaching_records_dto_1.TeachingRecordsResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: 'success',
                    data: {
                        list: [
                            {
                                id: 15,
                                courseId: 25,
                                courseName: '第一课：Node.js基础入门',
                                seriesName: 'Node.js后端开发系列',
                                classId: 10,
                                className: '软件工程2024-1班',
                                teacherId: 100,
                                teacherName: '张老师',
                                status: 1,
                                statusLabel: '成功',
                                pointsAllocated: 3000,
                                tasksCreated: 2,
                                templateApplied: 1,
                                lockAcquireTime: 45,
                                totalExecutionTime: 2350,
                                errorMessage: null,
                                createdAt: '2024-01-26T15:30:00Z',
                                updatedAt: '2024-01-26T15:32:00Z'
                            }
                        ],
                        pagination: {
                            page: 1,
                            pageSize: 10,
                            total: 1,
                            totalPages: 1,
                            hasNext: false,
                            hasPrev: false
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        examples: {
            badRequest: {
                summary: '参数错误',
                value: {
                    code: 400,
                    message: '页码必须大于0',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [teaching_records_dto_1.GetTeachingRecordsQueryDto]),
    __metadata("design:returntype", Promise)
], TeachingController.prototype, "getTeachingRecords", null);
exports.TeachingController = TeachingController = __decorate([
    (0, swagger_1.ApiTags)('课程教学'),
    (0, common_1.Controller)('api/v1/course-teaching'),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    __metadata("design:paramtypes", [teaching_service_1.TeachingService,
        http_response_result_service_1.HttpResponseResultService])
], TeachingController);
//# sourceMappingURL=teaching.controller.js.map