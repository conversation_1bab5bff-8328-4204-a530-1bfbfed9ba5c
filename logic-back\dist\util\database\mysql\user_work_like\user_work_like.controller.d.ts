import { UserWorkLikeService } from './user_work_like.service';
import { CreateUserWorkLikeDto } from './dto/create-user_work_like.dto';
import { UpdateUserWorkLikeDto } from './dto/update-user_work_like.dto';
import { UserWorkLike } from './entities/user_work_like.entity';
export declare class UserWorkLikeController {
    private readonly userWorkLikeService;
    constructor(userWorkLikeService: UserWorkLikeService);
    create(createUserWorkLikeDto: CreateUserWorkLikeDto): Promise<UserWorkLike>;
    findAll(): Promise<UserWorkLike[]>;
    findByUserId(userId: string): Promise<UserWorkLike[]>;
    findByTargetId(targetId: string, targetType: string): Promise<UserWorkLike[]>;
    checkLike(userId: string, targetId: string, targetType: string): Promise<UserWorkLike>;
    toggleLike(userId: string, targetId: string, targetType: string): Promise<UserWorkLike>;
    findOne(id: string): Promise<UserWorkLike>;
    update(id: string, updateUserWorkLikeDto: UpdateUserWorkLikeDto): Promise<UserWorkLike>;
    remove(id: string): Promise<void>;
}
