"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchoolRelationModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_school_relation_service_1 = require("./user_school_relation.service");
const user_school_relation_controller_1 = require("./user_school_relation.controller");
const user_school_relation_entity_1 = require("./entities/user_school_relation.entity");
const user_info_entity_1 = require("../user_info/entities/user_info.entity");
let UserSchoolRelationModule = class UserSchoolRelationModule {
};
exports.UserSchoolRelationModule = UserSchoolRelationModule;
exports.UserSchoolRelationModule = UserSchoolRelationModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([user_school_relation_entity_1.UserSchoolRelation, user_info_entity_1.UserInfo])],
        controllers: [user_school_relation_controller_1.UserSchoolRelationController],
        providers: [user_school_relation_service_1.UserSchoolRelationService],
        exports: [user_school_relation_service_1.UserSchoolRelationService],
    })
], UserSchoolRelationModule);
//# sourceMappingURL=user_school_relation.module.js.map