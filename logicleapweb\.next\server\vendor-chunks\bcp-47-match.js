"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcp-47-match";
exports.ids = ["vendor-chunks/bcp-47-match"];
exports.modules = {

/***/ "(ssr)/./node_modules/bcp-47-match/index.js":
/*!********************************************!*\
  !*** ./node_modules/bcp-47-match/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicFilter: () => (/* binding */ basicFilter),\n/* harmony export */   extendedFilter: () => (/* binding */ extendedFilter),\n/* harmony export */   lookup: () => (/* binding */ lookup)\n/* harmony export */ });\n/**\n * See <https://tools.ietf.org/html/rfc4647#section-3.1>\n * for more info on the algorithms.\n */\n\n/**\n * @typedef {string} Tag\n *   BCP-47 tag.\n * @typedef {Array<Tag>} Tags\n *   List of BCP-47 tags.\n * @typedef {string} Range\n *   RFC 4647 range.\n * @typedef {Array<Range>} Ranges\n *   List of RFC 4647 range.\n *\n * @callback Check\n *   An internal check.\n * @param {Tag} tag\n *   BCP-47 tag.\n * @param {Range} range\n *   RFC 4647 range.\n * @returns {boolean}\n *   Whether the range matches the tag.\n *\n * @typedef {FilterOrLookup<true>} Filter\n *   Filter: yields all tags that match a range.\n * @typedef {FilterOrLookup<false>} Lookup\n *   Lookup: yields the best tag that matches a range.\n */\n\n/**\n * @template {boolean} IsFilter\n *   Whether to filter or perform a lookup.\n * @callback FilterOrLookup\n *   A check.\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {IsFilter extends true ? Tags : Tag|undefined}\n *   Result.\n */\n\n/**\n * Factory to perform a filter or a lookup.\n *\n * This factory creates a function that accepts a list of tags and a list of\n * ranges, and contains logic to exit early for lookups.\n * `check` just has to deal with one tag and one range.\n * This match function iterates over ranges, and for each range,\n * iterates over tags.\n * That way, earlier ranges matching any tag have precedence over later ranges.\n *\n * @template {boolean} IsFilter\n * @param {Check} check\n *   A check.\n * @param {IsFilter} filter\n *   Whether to filter or perform a lookup.\n * @returns {FilterOrLookup<IsFilter>}\n *   Filter or lookup.\n */\nfunction factory(check, filter) {\n  /**\n   * @param {Tag|Tags} tags\n   *   One or more BCP-47 tags.\n   * @param {Range|Ranges|undefined} [ranges='*']\n   *   One or more RFC 4647 ranges.\n   * @returns {IsFilter extends true ? Tags : Tag|undefined}\n   *   Result.\n   */\n  return function (tags, ranges) {\n    let left = cast(tags, 'tag')\n    const right = cast(\n      ranges === null || ranges === undefined ? '*' : ranges,\n      'range'\n    )\n    /** @type {Tags} */\n    const matches = []\n    let rightIndex = -1\n\n    while (++rightIndex < right.length) {\n      const range = right[rightIndex].toLowerCase()\n\n      // Ignore wildcards in lookup mode.\n      if (!filter && range === '*') continue\n\n      let leftIndex = -1\n      /** @type {Tags} */\n      const next = []\n\n      while (++leftIndex < left.length) {\n        if (check(left[leftIndex].toLowerCase(), range)) {\n          // Exit if this is a lookup and we have a match.\n          if (!filter) {\n            return /** @type {IsFilter extends true ? Tags : Tag|undefined} */ (\n              left[leftIndex]\n            )\n          }\n\n          matches.push(left[leftIndex])\n        } else {\n          next.push(left[leftIndex])\n        }\n      }\n\n      left = next\n    }\n\n    // If this is a filter, return the list.  If it’s a lookup, we didn’t find\n    // a match, so return `undefined`.\n    return /** @type {IsFilter extends true ? Tags : Tag|undefined} */ (\n      filter ? matches : undefined\n    )\n  }\n}\n\n/**\n * Basic Filtering (Section 3.3.1) matches a language priority list consisting\n * of basic language ranges (Section 2.1) to sets of language tags.\n *\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {Tags}\n *   List of BCP-47 tags.\n */\nconst basicFilter = factory(function (tag, range) {\n  return range === '*' || tag === range || tag.includes(range + '-')\n}, true)\n\n/**\n * Extended Filtering (Section 3.3.2) matches a language priority list\n * consisting of extended language ranges (Section 2.2) to sets of language\n * tags.\n *\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {Tags}\n *   List of BCP-47 tags.\n */\nconst extendedFilter = factory(function (tag, range) {\n  // 3.3.2.1\n  const left = tag.split('-')\n  const right = range.split('-')\n  let leftIndex = 0\n  let rightIndex = 0\n\n  // 3.3.2.2\n  if (right[rightIndex] !== '*' && left[leftIndex] !== right[rightIndex]) {\n    return false\n  }\n\n  leftIndex++\n  rightIndex++\n\n  // 3.3.2.3\n  while (rightIndex < right.length) {\n    // 3.3.2.3.A\n    if (right[rightIndex] === '*') {\n      rightIndex++\n      continue\n    }\n\n    // 3.3.2.3.B\n    if (!left[leftIndex]) return false\n\n    // 3.3.2.3.C\n    if (left[leftIndex] === right[rightIndex]) {\n      leftIndex++\n      rightIndex++\n      continue\n    }\n\n    // 3.3.2.3.D\n    if (left[leftIndex].length === 1) return false\n\n    // 3.3.2.3.E\n    leftIndex++\n  }\n\n  // 3.3.2.4\n  return true\n}, true)\n\n/**\n * Lookup (Section 3.4) matches a language priority list consisting of basic\n * language ranges to sets of language tags to find the one exact language tag\n * that best matches the range.\n *\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {Tag|undefined}\n *   BCP-47 tag.\n */\nconst lookup = factory(function (tag, range) {\n  let right = range\n\n  /* eslint-disable-next-line no-constant-condition */\n  while (true) {\n    if (right === '*' || tag === right) return true\n\n    let index = right.lastIndexOf('-')\n\n    if (index < 0) return false\n\n    if (right.charAt(index - 2) === '-') index -= 2\n\n    right = right.slice(0, index)\n  }\n}, false)\n\n/**\n * Validate tags or ranges, and cast them to arrays.\n *\n * @param {string|Array<string>} values\n * @param {string} name\n * @returns {Array<string>}\n */\nfunction cast(values, name) {\n  const value = values && typeof values === 'string' ? [values] : values\n\n  if (!value || typeof value !== 'object' || !('length' in value)) {\n    throw new Error(\n      'Invalid ' + name + ' `' + value + '`, expected non-empty string'\n    )\n  }\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bcp-47-match/index.js\n");

/***/ })

};
;