import { ParticipationAuditService } from './participation_audit.service';
import { CreateParticipationAuditDto } from './dto/create-participation_audit.dto';
import { UpdateParticipationAuditDto } from './dto/update-participation_audit.dto';
import { ParticipationAudit } from './entities/participation_audit.entity';
export declare class ParticipationAuditController {
    private readonly participationAuditService;
    constructor(participationAuditService: ParticipationAuditService);
    create(createParticipationAuditDto: CreateParticipationAuditDto): Promise<ParticipationAudit>;
    findAll(): Promise<ParticipationAudit[]>;
    findOne(id: string): Promise<ParticipationAudit>;
    update(id: string, updateParticipationAuditDto: UpdateParticipationAuditDto): Promise<ParticipationAudit>;
    remove(id: string): Promise<void>;
}
