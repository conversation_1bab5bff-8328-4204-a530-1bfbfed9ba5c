"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiVoiceprintRecognitionModule = void 0;
const common_1 = require("@nestjs/common");
const ai_voiceprint_recognition_service_1 = require("./ai_voiceprint_recognition.service");
const ai_voiceprint_recognition_controller_1 = require("./ai_voiceprint_recognition.controller");
const queue_module_1 = require("../../util/queue/queue.module");
const scratch_config_module_1 = require("../config/scratch.config.module");
const user_point_module_1 = require("../../web/user_point/user_point.module");
const web_socket_module_1 = require("../../util/web_socket/web_socket.module");
const web_point_module_1 = require("../../web/web_point/web_point.module");
const xunfei_voiceprint_recognition_module_1 = require("../../util/ai_providers/xunfei_voiceprint_recognition/xunfei_voiceprint_recognition.module");
const http_response_result_module_1 = require("../../web/http_response_result/http_response_result.module");
const voiceprint_group_module_1 = require("../../util/database/mysql/voiceprint_group/voiceprint_group.module");
const voiceprint_feature_module_1 = require("../../util/database/mysql/voiceprint_feature/voiceprint_feature.module");
let AiVoiceprintRecognitionModule = class AiVoiceprintRecognitionModule {
};
exports.AiVoiceprintRecognitionModule = AiVoiceprintRecognitionModule;
exports.AiVoiceprintRecognitionModule = AiVoiceprintRecognitionModule = __decorate([
    (0, common_1.Module)({
        imports: [
            queue_module_1.QueueModule,
            web_socket_module_1.WebSocketModule,
            user_point_module_1.UserPointModule,
            web_point_module_1.WebPointModule,
            scratch_config_module_1.ScratchConfigModule,
            xunfei_voiceprint_recognition_module_1.XunfeiVoiceprintRecognitionModule,
            http_response_result_module_1.HttpResponseResultModule,
            voiceprint_group_module_1.VoiceprintGroupModule,
            voiceprint_feature_module_1.VoiceprintFeatureModule
        ],
        controllers: [ai_voiceprint_recognition_controller_1.AiVoiceprintRecognitionController],
        providers: [ai_voiceprint_recognition_service_1.AiVoiceprintRecognitionService],
        exports: [ai_voiceprint_recognition_service_1.AiVoiceprintRecognitionService]
    })
], AiVoiceprintRecognitionModule);
//# sourceMappingURL=ai_voiceprint_recognition.module.js.map