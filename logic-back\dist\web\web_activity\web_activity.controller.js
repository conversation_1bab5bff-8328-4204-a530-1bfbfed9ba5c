"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const web_activity_service_1 = require("./web_activity.service");
const activity_entity_1 = require("../../util/database/mysql/activity/entities/activity.entity");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const not_login_decorator_1 = require("../router_guard/not-login.decorator");
let WebActivityController = class WebActivityController {
    webActivityService;
    httpResponseResultService;
    constructor(webActivityService, httpResponseResultService) {
        this.webActivityService = webActivityService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async create(data, currentUser) {
        try {
            if (!currentUser || !currentUser.id) {
                return this.httpResponseResultService.error('用户未登录或登录已过期', null, 401);
            }
            const result = await this.webActivityService.create(data, currentUser.id);
            return this.httpResponseResultService.success(result, '创建成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '创建失败', null, 400);
        }
    }
    async updateActivity(id, data) {
        try {
            const result = await this.webActivityService.updateActivity(id, data);
            return this.httpResponseResultService.success(result, '更新成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '更新失败', null, 400);
        }
    }
    async deleteActivity(id) {
        try {
            const result = await this.webActivityService.deleteActivity(id);
            return this.httpResponseResultService.success(result, '删除成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '删除失败', null, 400);
        }
    }
    async getActivityInfo(id) {
        try {
            const result = await this.webActivityService.getById(id);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async getActivityList(query) {
        try {
            const result = await this.webActivityService.list(query);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async getActivityWithWorks(id) {
        try {
            const result = await this.webActivityService.getActivityWithWorks(id);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async getActivityContentByType(id) {
        try {
            const result = await this.webActivityService.getActivityContentByType(id);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async addWorksToActivity(activityId, data) {
        try {
            const result = await this.webActivityService.addWorksToActivity(activityId, data.works);
            return this.httpResponseResultService.success(result, '添加成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '添加失败', null, 400);
        }
    }
    async setAwardedWorks(activityId, data) {
        try {
            const result = await this.webActivityService.setAwardedWorks(activityId, data.workIds);
            return this.httpResponseResultService.success(result, '设置成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '设置失败', null, 400);
        }
    }
    async uploadSignature(data, currentUser, req) {
        try {
            if (!currentUser || !currentUser.id) {
                return this.httpResponseResultService.error('用户未登录或登录已过期', null, 401);
            }
            const result = await this.webActivityService.uploadSignature({
                signatureData: data.signatureData,
                activityId: data.activityId,
                userId: currentUser.id,
                userIp: req.ip || req.connection?.remoteAddress || 'unknown'
            });
            return this.httpResponseResultService.success(result, '签名上传成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '签名上传失败', null, 400);
        }
    }
    async submitRegistration(data, currentUser) {
        try {
            if (!currentUser || !currentUser.id) {
                return this.httpResponseResultService.error('用户未登录或登录已过期', null, 401);
            }
            const result = await this.webActivityService.submitRegistration({
                ...data,
                userId: currentUser.id,
                userName: currentUser.nickName || currentUser.username || '未知用户',
                userPhone: currentUser.phone || '',
                signatureTime: data.signatureTime ? new Date(data.signatureTime) : new Date()
            });
            return this.httpResponseResultService.success(result, '报名成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '报名失败', null, 400);
        }
    }
    async checkUserRegistration(activityId, currentUser) {
        try {
            if (!currentUser || !currentUser.id) {
                return this.httpResponseResultService.error('用户未登录或登录已过期', null, 401);
            }
            const result = await this.webActivityService.checkUserRegistration(activityId, currentUser.id);
            return this.httpResponseResultService.success(result, '检查成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '检查失败', null, 400);
        }
    }
    async cancelRegistration(activityId, currentUser) {
        try {
            if (!currentUser || !currentUser.id) {
                return this.httpResponseResultService.error('用户未登录或登录已过期', null, 401);
            }
            const result = await this.webActivityService.cancelRegistration(activityId, currentUser.id);
            return this.httpResponseResultService.success(result, '取消成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '取消失败', null, 400);
        }
    }
    async getUserRegistrations(query, currentUser) {
        try {
            if (!currentUser || !currentUser.id) {
                return this.httpResponseResultService.error('用户未登录或登录已过期', null, 401);
            }
            const result = await this.webActivityService.getUserRegistrations(currentUser.id, query);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async getRegistrationStatistics(activityId) {
        try {
            const result = await this.webActivityService.getRegistrationStatistics(activityId);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
};
exports.WebActivityController = WebActivityController;
__decorate([
    (0, common_1.Post)('/createActivity'),
    (0, swagger_1.ApiOperation)({ summary: '创建活动' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['name', 'startTime', 'endTime', 'organizer'],
            properties: {
                name: { type: 'string', description: '活动名称' },
                startTime: { type: 'string', format: 'date-time', description: '活动开始时间' },
                endTime: { type: 'string', format: 'date-time', description: '活动结束时间' },
                coverImage: { type: 'string', description: '活动封面' },
                organizer: { type: 'string', description: '主办单位' },
                tagIds: { type: 'array', items: { type: 'number' }, description: '标签ID数组' },
                activityType: { type: 'number', description: '活动类型：1-作品活动 2-图片活动 3-其他' },
                attachmentFiles: { type: 'string', description: '活动附件文件URL列表，多个文件用逗号分隔' },
                promotionImage: { type: 'string', description: '活动宣传图片URL' },
                competitionGroups: { type: 'string', description: '参赛组别列表，多个组别用逗号分隔' },
                registrationForm: { type: 'string', description: '报名表信息，格式：报名表URL,示例图URL' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: activity_entity_1.Activity }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('/updateActivity/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '活动ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', description: '活动名称' },
                startTime: { type: 'string', format: 'date-time', description: '活动开始时间' },
                endTime: { type: 'string', format: 'date-time', description: '活动结束时间' },
                coverImage: { type: 'string', description: '活动封面' },
                organizer: { type: 'string', description: '主办单位' },
                tagIds: { type: 'array', items: { type: 'number' }, description: '标签ID数组' },
                status: { type: 'number', description: '活动状态' },
                activityType: { type: 'number', description: '活动类型' },
                attachmentFiles: { type: 'string', description: '活动附件文件URL列表，多个文件用逗号分隔' },
                promotionImage: { type: 'string', description: '活动宣传图片URL' },
                competitionGroups: { type: 'string', description: '参赛组别列表，多个组别用逗号分隔' },
                registrationForm: { type: 'string', description: '报名表信息，格式：报名表URL,示例图URL' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '活动不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "updateActivity", null);
__decorate([
    (0, common_1.Delete)('/deleteActivity/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除活动' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '活动不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "deleteActivity", null);
__decorate([
    (0, common_1.Get)('/infoActivity/:id'),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiOperation)({ summary: '获取活动详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: activity_entity_1.Activity }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '活动不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "getActivityInfo", null);
__decorate([
    (0, common_1.Get)('/listActivity'),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiOperation)({ summary: '获取活动列表' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: '页码', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'size', required: false, type: Number, description: '每页数量', example: 10 }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, type: Number, description: '活动状态' }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: false, type: String, description: '搜索关键词' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "getActivityList", null);
__decorate([
    (0, common_1.Get)('/infoActivityWithWorks/:id'),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiOperation)({ summary: '获取活动详情（包含作品信息）' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '活动不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "getActivityWithWorks", null);
__decorate([
    (0, common_1.Get)('/infoActivityContent/:id'),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiOperation)({ summary: '根据活动类型获取活动内容' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '活动不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "getActivityContentByType", null);
__decorate([
    (0, common_1.Post)('/addWorks/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '向活动添加作品' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['works'],
            properties: {
                works: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            workId: { type: 'number', description: '作品ID' },
                            isAwarded: { type: 'boolean', description: '是否获奖' },
                            category: { type: 'string', description: '作品分类' },
                            sort: { type: 'number', description: '排序' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "addWorksToActivity", null);
__decorate([
    (0, common_1.Post)('/setAwardedWorks/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '设置活动中的获奖作品' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['workIds'],
            properties: {
                workIds: { type: 'array', items: { type: 'number' }, description: '作品ID数组' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "setAwardedWorks", null);
__decorate([
    (0, common_1.Post)('/uploadSignature'),
    (0, swagger_1.ApiOperation)({ summary: '上传签名图片' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['signatureData'],
            properties: {
                signatureData: { type: 'string', description: '签名图片的Base64数据' },
                activityId: { type: 'number', description: '活动ID' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '上传成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '上传失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "uploadSignature", null);
__decorate([
    (0, common_1.Post)('/submitRegistration'),
    (0, swagger_1.ApiOperation)({ summary: '活动报名' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['activityId', 'agreementAccepted', 'parentConsentAccepted', 'parentSignaturePath'],
            properties: {
                activityId: { type: 'number', description: '活动ID' },
                agreementAccepted: { type: 'boolean', description: '是否同意参赛协议' },
                parentConsentAccepted: { type: 'boolean', description: '是否同意家长知情同意书' },
                parentSignaturePath: { type: 'string', description: '家长签名文件路径' },
                signatureTime: { type: 'string', description: '签名时间' },
                signatureIp: { type: 'string', description: '签名IP地址' },
                remark: { type: 'string', description: '备注信息' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '报名成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '报名失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "submitRegistration", null);
__decorate([
    (0, common_1.Get)('/checkRegistration/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户是否已报名某活动' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检查成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "checkUserRegistration", null);
__decorate([
    (0, common_1.Patch)('/cancelRegistration/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '取消活动报名' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "cancelRegistration", null);
__decorate([
    (0, common_1.Get)('/myRegistrations'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户的报名记录' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: '页码', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'size', required: false, type: Number, description: '每页数量', example: 10 }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "getUserRegistrations", null);
__decorate([
    (0, common_1.Get)('/registrationStatistics/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '获取活动的报名统计信息' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityController.prototype, "getRegistrationStatistics", null);
exports.WebActivityController = WebActivityController = __decorate([
    (0, swagger_1.ApiTags)('web/活动管理(web_activity)'),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)('api/v1/activity'),
    __metadata("design:paramtypes", [web_activity_service_1.WebActivityService,
        http_response_result_service_1.HttpResponseResultService])
], WebActivityController);
//# sourceMappingURL=web_activity.controller.js.map