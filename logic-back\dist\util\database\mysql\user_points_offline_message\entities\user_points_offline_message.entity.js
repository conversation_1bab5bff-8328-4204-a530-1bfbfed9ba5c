"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPointsOfflineMessage = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let UserPointsOfflineMessage = class UserPointsOfflineMessage {
    id;
    userId;
    pointsChange;
    totalPoints;
    status;
    createTime;
    updateTime;
};
exports.UserPointsOfflineMessage = UserPointsOfflineMessage;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], UserPointsOfflineMessage.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], UserPointsOfflineMessage.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        comment: '积分变动值',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0
    }),
    (0, swagger_1.ApiProperty)({ description: '积分变动值' }),
    __metadata("design:type", Number)
], UserPointsOfflineMessage.prototype, "pointsChange", void 0);
__decorate([
    (0, typeorm_1.Column)({
        comment: '总积分',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0
    }),
    (0, swagger_1.ApiProperty)({ description: '总积分' }),
    __metadata("design:type", Number)
], UserPointsOfflineMessage.prototype, "totalPoints", void 0);
__decorate([
    (0, typeorm_1.Column)({
        comment: '消息状态 0:未发送 1:已发送',
        default: 0
    }),
    (0, swagger_1.ApiProperty)({ description: '消息状态 0:未发送 1:已发送', default: 0 }),
    __metadata("design:type", Number)
], UserPointsOfflineMessage.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        precision: 6,
        default: () => 'CURRENT_TIMESTAMP(6)',
        comment: '创建时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], UserPointsOfflineMessage.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        precision: 6,
        default: () => 'CURRENT_TIMESTAMP(6)',
        onUpdate: 'CURRENT_TIMESTAMP(6)',
        comment: '更新时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], UserPointsOfflineMessage.prototype, "updateTime", void 0);
exports.UserPointsOfflineMessage = UserPointsOfflineMessage = __decorate([
    (0, typeorm_1.Entity)('user_points_offline_message')
], UserPointsOfflineMessage);
//# sourceMappingURL=user_points_offline_message.entity.js.map