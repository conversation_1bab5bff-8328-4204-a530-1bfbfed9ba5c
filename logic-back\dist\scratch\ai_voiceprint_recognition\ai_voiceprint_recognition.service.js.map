{"version": 3, "file": "ai_voiceprint_recognition.service.js", "sourceRoot": "", "sources": ["../../../src/scratch/ai_voiceprint_recognition/ai_voiceprint_recognition.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,+DAA2D;AAE3D,kEAA4D;AAC5D,6EAAwE;AACxE,iFAA0E;AAC1E,6EAAsE;AACtE,uJAA+I;AAGxI,IAAM,8BAA8B,GAApC,MAAM,8BAA+B,SAAQ,kCAAe;IAExC;IACF;IACA;IACA;IACA;IALrB,YACuB,YAA0B,EAC5B,oBAA0C,EAC1C,gBAAkC,EAClC,eAAgC,EAChC,iBAAqD;QAEtE,KAAK,CAAC,YAAY,CAAC,CAAC;QAND,iBAAY,GAAZ,YAAY,CAAc;QAC5B,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,oBAAe,GAAf,eAAe,CAAiB;QAChC,sBAAiB,GAAjB,iBAAiB,CAAoC;QAGtE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;IACrF,CAAC;IAUD,KAAK,CAAC,qBAAqB,CAAC,IAA8F;QACtH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;IAC9D,CAAC;IAMD,KAAK,CAAC,uBAAuB,CAAC,IAO7B;QACG,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAMD,KAAK,CAAC,iBAAiB,CAAC,IAMvB;QACG,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAMD,KAAK,CAAC,gBAAgB,CAAC,IAMtB;QACG,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAMD,KAAK,CAAC,0BAA0B,CAAC,IAIhC;QACG,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC;IAMD,KAAK,CAAC,uBAAuB,CAAC,IAK7B;QACG,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAMD,KAAK,CAAC,uBAAuB,CAAC,IAO7B;QACG,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,GAAQ;QACtB,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,0BAA0B,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;YACzB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;YAGlC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,+BAA+B,EAAE;gBAC/D,MAAM,EAAE,YAAY;gBACpB,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,GAAG,CAAC,EAAE;aAChB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAEtB,IAAI,MAAM,CAAC;YAGX,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;gBACf,KAAK,yBAAyB;oBAC1B,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;oBAClD,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,CAAC,CAAC;oBACvF,MAAM;gBAEV,KAAK,2BAA2B;oBAC5B,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;oBACjF,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,cAAc,EAAE,SAAS,EAAE,WAAW,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC;oBAC/G,MAAM;gBAEV,KAAK,oBAAoB;oBACrB,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;oBACrF,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;oBACjG,MAAM;gBAEV,KAAK,mBAAmB;oBACpB,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;oBAC3E,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;oBAC3F,MAAM;gBAEV,KAAK,+BAA+B;oBAChC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;oBAC1C,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;oBACrE,MAAM;gBAEV,KAAK,2BAA2B;oBAC5B,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;oBACvE,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;oBACpF,MAAM;gBAEV,KAAK,2BAA2B;oBAC5B,MAAM,EACF,OAAO,EAAE,aAAa,EACtB,SAAS,EAAE,eAAe,EAC1B,WAAW,EAAE,iBAAiB,EAC9B,WAAW,EAAE,WAAW,EAC3B,GAAG,OAAO,CAAC;oBACZ,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAC/C,aAAa,EACb,eAAe,EACf,iBAAiB,IAAI,EAAE,EACvB,WAAW,CACd,CAAC;oBACF,MAAM;gBAEV;oBACI,MAAM,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAChD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,uBAAuB,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAE7C,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAGvD,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACpC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAC7B,+BAA+B,EAC/B;oBACI,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,SAAS,KAAK,CAAC,OAAO,IAAI,MAAM,EAAE;oBAC3C,KAAK,EAAE,GAAG,CAAC,EAAE;oBACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB,EACD,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CACrD,CAAC;YACN,CAAC;YAED,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAGD,KAAK,CAAC,eAAe,CAAC,GAAQ;QAC1B,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QACnC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;QAGzB,MAAM,cAAc,GAAG;YACnB,yBAAyB,EAAE,SAAS;YACpC,oBAAoB,EAAE,MAAM;YAC5B,mBAAmB,EAAE,MAAM;SAC9B,CAAC;QAGF,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACD,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,IAAG,OAAO,KAAK,yBAAyB,EAAC,CAAC;oBACtC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC;gBACtF,CAAC;qBAAK,IAAG,OAAO,KAAK,oBAAoB,EAAC,CAAC;oBACvC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC;gBACvF,CAAC;qBAAK,IAAG,OAAO,KAAK,mBAAmB,EAAC,CAAC;oBACtC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC;gBACtF,CAAC;gBACD,MAAM,MAAM,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;gBACvC,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;gBAChE,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,KAAK,MAAM,UAAU,EAAE,MAAM,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAM,GAAG,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,SAAS,EAAE,GAAG,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;QAGD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,+BAA+B,EAAE;YAC/D,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,MAAM,EAAE,GAAG,CAAC,WAAW;YACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAGtB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAGD,kBAAkB,CAAC,GAAQ,EAAE,GAAU;IAQvC,CAAC;IAGD,oBAAoB,CAAC,GAAQ,EAAE,GAAU;QAE3C,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAE/C,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,+BAA+B,EAAE;YACjE,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,YAAY;YACrB,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAEvD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAGD,kBAAkB,CAAC,IAAS,EAAE,GAAQ;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,+BAA+B,EAAE;YAC/D,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,KAAa;QAC5B,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC9B,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3C,IAAI,CAAC,GAAG,EAAE,CAAC;gBACP,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,OAAO;gBACH,GAAG,GAAG,CAAC,MAAM,EAAE;gBACf,QAAQ;aACX,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5C,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;CACJ,CAAA;AApTY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;qCAG4B,4BAAY;QACN,6CAAoB;QACxB,qCAAgB;QACjB,mCAAe;QACb,0EAAkC;GANjE,8BAA8B,CAoT1C"}