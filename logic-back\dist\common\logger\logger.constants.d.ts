export declare const LOG_LEVELS: {
    readonly ERROR: "error";
    readonly WARN: "warn";
    readonly INFO: "info";
    readonly HTTP: "http";
    readonly VERBOSE: "verbose";
    readonly DEBUG: "debug";
    readonly SILLY: "silly";
};
export declare const LOG_CONTEXTS: {
    readonly HTTP: "HTTP";
    readonly DATABASE: "Database";
    readonly AUTH: "Auth";
    readonly PAYMENT: "Payment";
    readonly BUSINESS: "Business";
    readonly STARTUP: "Startup";
    readonly PERFORMANCE: "Performance";
    readonly SECURITY: "Security";
    readonly WEBSOCKET: "WebSocket";
    readonly SCHEDULER: "Scheduler";
    readonly CACHE: "Cache";
    readonly FILE: "File";
    readonly EMAIL: "Email";
    readonly SMS: "SMS";
};
export declare const LOG_FILES: {
    readonly APPLICATION: "application";
    readonly ERROR: "error";
    readonly HTTP: "http";
    readonly DATABASE: "database";
    readonly SECURITY: "security";
    readonly PERFORMANCE: "performance";
    readonly EXCEPTIONS: "exceptions";
    readonly REJECTIONS: "rejections";
};
export declare const LOG_CONFIG: {
    readonly development: {
        readonly level: "debug";
        readonly console: true;
        readonly file: true;
    };
    readonly dev: {
        readonly level: "debug";
        readonly console: true;
        readonly file: true;
    };
    readonly production: {
        readonly level: "info";
        readonly console: false;
        readonly file: true;
    };
    readonly prod: {
        readonly level: "info";
        readonly console: false;
        readonly file: true;
    };
    readonly test: {
        readonly level: "error";
        readonly console: false;
        readonly file: false;
    };
};
