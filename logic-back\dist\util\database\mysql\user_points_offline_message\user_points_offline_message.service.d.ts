import { Repository } from 'typeorm';
import { CreateUserPointsOfflineMessageDto } from './dto/create-user_points_offline_message.dto';
import { UpdateUserPointsOfflineMessageDto } from './dto/update-user_points_offline_message.dto';
import { UserPointsOfflineMessage } from './entities/user_points_offline_message.entity';
export declare class UserPointsOfflineMessageService {
    private userPointsOfflineMessageRepository;
    constructor(userPointsOfflineMessageRepository: Repository<UserPointsOfflineMessage>);
    create(createUserPointsOfflineMessageDto: CreateUserPointsOfflineMessageDto): Promise<UserPointsOfflineMessage>;
    findAll(): Promise<UserPointsOfflineMessage[]>;
    findByUserId(userId: number): Promise<UserPointsOfflineMessage[]>;
    findOne(id: number): Promise<UserPointsOfflineMessage>;
    update(id: number, updateUserPointsOfflineMessageDto: UpdateUserPointsOfflineMessageDto): Promise<UserPointsOfflineMessage>;
    remove(id: number): Promise<void>;
    markAsSent(id: number): Promise<UserPointsOfflineMessage>;
    findPendingMessages(): Promise<UserPointsOfflineMessage[]>;
    getPendingMessage(userId: number): Promise<UserPointsOfflineMessage | null>;
}
