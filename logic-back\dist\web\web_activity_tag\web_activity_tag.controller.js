"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebActivityTagController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const web_activity_tag_service_1 = require("./web_activity_tag.service");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
let WebActivityTagController = class WebActivityTagController {
    webActivityTagService;
    httpResponseResultService;
    constructor(webActivityTagService, httpResponseResultService) {
        this.webActivityTagService = webActivityTagService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async addActivityTags(data) {
        try {
            const result = await this.webActivityTagService.addActivityTags(data.activityId, data.tagIds);
            return this.httpResponseResultService.success(result, '添加成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '添加失败', null, 400);
        }
    }
    async updateActivityTags(activityId, data) {
        try {
            const result = await this.webActivityTagService.updateActivityTags(activityId, data.tagIds);
            return this.httpResponseResultService.success(result, '更新成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '更新失败', null, 400);
        }
    }
    async deleteActivityTags(activityId) {
        try {
            const result = await this.webActivityTagService.deleteActivityTags(activityId);
            return this.httpResponseResultService.success(result, '删除成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '删除失败', null, 400);
        }
    }
    async getActivityTagIds(activityId) {
        try {
            const result = await this.webActivityTagService.getActivityTagIds(activityId);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async getTagActivityIds(tagId) {
        try {
            const result = await this.webActivityTagService.getTagActivityIds(tagId);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
};
exports.WebActivityTagController = WebActivityTagController;
__decorate([
    (0, common_1.Post)('/add-tags'),
    (0, swagger_1.ApiOperation)({ summary: '添加活动标签关联' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['activityId', 'tagIds'],
            properties: {
                activityId: { type: 'number', description: '活动ID' },
                tagIds: { type: 'array', items: { type: 'number' }, description: '标签ID数组' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebActivityTagController.prototype, "addActivityTags", null);
__decorate([
    (0, common_1.Put)('/edit-tags/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '更新活动标签关联' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['tagIds'],
            properties: {
                tagIds: { type: 'array', items: { type: 'number' }, description: '标签ID数组' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WebActivityTagController.prototype, "updateActivityTags", null);
__decorate([
    (0, common_1.Delete)('/remove-tags/:activityId'),
    (0, swagger_1.ApiOperation)({ summary: '删除活动标签关联' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityTagController.prototype, "deleteActivityTags", null);
__decorate([
    (0, common_1.Get)('/get-activity/:activityId/tags'),
    (0, swagger_1.ApiOperation)({ summary: '获取活动的标签ID列表' }),
    (0, swagger_1.ApiParam)({ name: 'activityId', description: '活动ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('activityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityTagController.prototype, "getActivityTagIds", null);
__decorate([
    (0, common_1.Get)('/get-tag/:tagId/activities'),
    (0, swagger_1.ApiOperation)({ summary: '获取标签关联的活动ID列表' }),
    (0, swagger_1.ApiParam)({ name: 'tagId', description: '标签ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('tagId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebActivityTagController.prototype, "getTagActivityIds", null);
exports.WebActivityTagController = WebActivityTagController = __decorate([
    (0, swagger_1.ApiTags)('web/活动标签关联管理(web_activity_tag)'),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)('api/v1/activity_tag'),
    __metadata("design:paramtypes", [web_activity_tag_service_1.WebActivityTagService,
        http_response_result_service_1.HttpResponseResultService])
], WebActivityTagController);
//# sourceMappingURL=web_activity_tag.controller.js.map