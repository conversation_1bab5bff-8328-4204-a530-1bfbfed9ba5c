import { RedisService } from '../../database/redis/redis.service';
import { SessionType } from '../encryption.service';
export declare class RedisSessionService {
    private readonly redisService;
    private readonly logger;
    private readonly SESSION_KEY_PREFIX;
    private readonly STANDARD_SESSION_TTL;
    private readonly SECURE_SESSION_TTL;
    constructor(redisService: RedisService);
    private getSessionKey;
    storeSession(sessionId: string, sessionData: any, sessionType?: SessionType): Promise<boolean>;
    getSession(sessionId: string): Promise<any>;
    refreshSession(sessionId: string, sessionType?: SessionType): Promise<boolean>;
    removeSession(sessionId: string): Promise<boolean>;
    hasSession(sessionId: string): Promise<boolean>;
    getSessionCount(): Promise<number>;
}
