"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseHealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const connection_monitor_service_1 = require("./connection-monitor.service");
let DatabaseHealthController = class DatabaseHealthController {
    connectionMonitorService;
    constructor(connectionMonitorService) {
        this.connectionMonitorService = connectionMonitorService;
    }
    async getConnectionStatus() {
        try {
            const status = await this.connectionMonitorService.getConnectionPoolStatus();
            return {
                code: 200,
                message: 'success',
                data: status,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            return {
                code: 500,
                message: error.message,
                data: null,
                timestamp: new Date().toISOString()
            };
        }
    }
    async releaseIdleConnections() {
        try {
            const result = await this.connectionMonitorService.forceReleaseIdleConnections();
            return {
                code: 200,
                message: `成功释放 ${result.releasedConnections} 个空闲连接`,
                data: result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            return {
                code: 500,
                message: error.message,
                data: null,
                timestamp: new Date().toISOString()
            };
        }
    }
    async healthCheck() {
        try {
            const status = await this.connectionMonitorService.getConnectionPoolStatus();
            const isHealthy = status.isConnected && status.totalConnections < status.connectionLimit * 0.9;
            return {
                code: isHealthy ? 200 : 500,
                message: isHealthy ? '数据库连接正常' : '数据库连接异常',
                data: {
                    healthy: isHealthy,
                    connectionUsage: `${status.totalConnections}/${status.connectionLimit}`,
                    usagePercentage: Math.round((status.totalConnections / status.connectionLimit) * 100),
                    ...status
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            return {
                code: 500,
                message: '健康检查失败: ' + error.message,
                data: { healthy: false },
                timestamp: new Date().toISOString()
            };
        }
    }
};
exports.DatabaseHealthController = DatabaseHealthController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取数据库连接池状态',
        description: '查看当前数据库连接池的使用情况'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取连接池状态',
        schema: {
            type: 'object',
            properties: {
                totalConnections: { type: 'number', description: '总连接数' },
                freeConnections: { type: 'number', description: '空闲连接数' },
                acquiringConnections: { type: 'number', description: '正在获取的连接数' },
                connectionLimit: { type: 'number', description: '连接池限制' },
                queueLimit: { type: 'number', description: '队列限制' },
                isConnected: { type: 'boolean', description: '是否已连接' },
            }
        }
    }),
    (0, common_1.Get)('connection-status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseHealthController.prototype, "getConnectionStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '强制释放空闲连接',
        description: '手动释放所有空闲的数据库连接，用于解决连接池耗尽问题'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功释放空闲连接',
        schema: {
            type: 'object',
            properties: {
                releasedConnections: { type: 'number', description: '释放的连接数' },
            }
        }
    }),
    (0, common_1.Post)('release-idle-connections'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseHealthController.prototype, "releaseIdleConnections", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '数据库健康检查',
        description: '检查数据库连接是否正常'
    }),
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseHealthController.prototype, "healthCheck", null);
exports.DatabaseHealthController = DatabaseHealthController = __decorate([
    (0, swagger_1.ApiTags)('数据库健康检查'),
    (0, common_1.Controller)('api/v1/database-health'),
    __metadata("design:paramtypes", [connection_monitor_service_1.ConnectionMonitorService])
], DatabaseHealthController);
//# sourceMappingURL=database-health.controller.js.map