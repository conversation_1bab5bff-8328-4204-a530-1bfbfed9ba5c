"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateActivitySubmitDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateActivitySubmitDto {
    activityId;
    userId;
    userName;
    userPhone;
    agreementAccepted;
    parentConsentAccepted;
    parentSignaturePath;
    signatureTime;
    signatureIp;
    reviewReason;
    remark;
}
exports.CreateActivitySubmitDto = CreateActivitySubmitDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '活动ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '活动ID必须是数字' }),
    __metadata("design:type", Number)
], CreateActivitySubmitDto.prototype, "activityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    __metadata("design:type", Number)
], CreateActivitySubmitDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户姓名' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户姓名不能为空' }),
    (0, class_validator_1.IsString)({ message: '用户姓名必须是字符串' }),
    __metadata("design:type", String)
], CreateActivitySubmitDto.prototype, "userName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户手机号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户手机号必须是字符串' }),
    __metadata("design:type", String)
], CreateActivitySubmitDto.prototype, "userPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否同意参赛协议' }),
    (0, class_validator_1.IsNotEmpty)({ message: '是否同意参赛协议不能为空' }),
    (0, class_validator_1.IsBoolean)({ message: '是否同意参赛协议必须是布尔值' }),
    __metadata("design:type", Boolean)
], CreateActivitySubmitDto.prototype, "agreementAccepted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否同意家长知情同意书' }),
    (0, class_validator_1.IsNotEmpty)({ message: '是否同意家长知情同意书不能为空' }),
    (0, class_validator_1.IsBoolean)({ message: '是否同意家长知情同意书必须是布尔值' }),
    __metadata("design:type", Boolean)
], CreateActivitySubmitDto.prototype, "parentConsentAccepted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '家长签名文件路径', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '家长签名文件路径必须是字符串' }),
    __metadata("design:type", String)
], CreateActivitySubmitDto.prototype, "parentSignaturePath", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '签名时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], CreateActivitySubmitDto.prototype, "signatureTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '签名IP地址', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '签名IP地址必须是字符串' }),
    __metadata("design:type", String)
], CreateActivitySubmitDto.prototype, "signatureIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核原因/备注', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '审核原因必须是字符串' }),
    __metadata("design:type", String)
], CreateActivitySubmitDto.prototype, "reviewReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注信息必须是字符串' }),
    __metadata("design:type", String)
], CreateActivitySubmitDto.prototype, "remark", void 0);
//# sourceMappingURL=create-activity-submit.dto.js.map