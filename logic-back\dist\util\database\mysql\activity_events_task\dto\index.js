"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmitEventsTaskDto = exports.UpdateTaskStatusDto = exports.BatchUpdateStatusDto = exports.UpdateActivityEventsTaskDto = exports.CreateActivityEventsTaskDto = void 0;
var create_activity_events_task_dto_1 = require("./create-activity-events-task.dto");
Object.defineProperty(exports, "CreateActivityEventsTaskDto", { enumerable: true, get: function () { return create_activity_events_task_dto_1.CreateActivityEventsTaskDto; } });
var update_activity_events_task_dto_1 = require("./update-activity-events-task.dto");
Object.defineProperty(exports, "UpdateActivityEventsTaskDto", { enumerable: true, get: function () { return update_activity_events_task_dto_1.UpdateActivityEventsTaskDto; } });
var batch_update_status_dto_1 = require("./batch-update-status.dto");
Object.defineProperty(exports, "BatchUpdateStatusDto", { enumerable: true, get: function () { return batch_update_status_dto_1.BatchUpdateStatusDto; } });
var update_task_status_dto_1 = require("./update-task-status.dto");
Object.defineProperty(exports, "UpdateTaskStatusDto", { enumerable: true, get: function () { return update_task_status_dto_1.UpdateTaskStatusDto; } });
var submit_events_task_dto_1 = require("./submit-events-task.dto");
Object.defineProperty(exports, "SubmitEventsTaskDto", { enumerable: true, get: function () { return submit_events_task_dto_1.SubmitEventsTaskDto; } });
//# sourceMappingURL=index.js.map