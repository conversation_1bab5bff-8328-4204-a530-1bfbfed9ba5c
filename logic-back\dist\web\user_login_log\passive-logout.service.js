"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassiveLogoutUtil = exports.PassiveLogoutReason = exports.PassiveLogoutService = void 0;
const common_1 = require("@nestjs/common");
const login_logger_util_1 = require("./login-logger.util");
let PassiveLogoutService = class PassiveLogoutService {
    async recordPassiveLogout(userId, reason, clientInfo) {
        try {
            await login_logger_util_1.LoginLoggerUtil.logLogout({
                userId,
                clientIp: clientInfo?.clientIp || '未知IP',
                sessionId: undefined,
                reason: reason,
                logoutType: 'passive'
            });
            console.log('✅ 被动登出日志记录成功:', { userId, reason });
        }
        catch (error) {
            console.error('❌ 记录被动登出日志失败:', error);
        }
    }
    async recordDeviceKickOut(userId, newDeviceInfo, clientInfo) {
        const reason = `新设备登录，旧会话被挤出 - 新设备: ${newDeviceInfo}`;
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordTokenExpired(userId, clientInfo) {
        const reason = 'Token过期，会话自动结束';
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordAdminForceLogout(userId, adminId, clientInfo) {
        const reason = `管理员强制登出 - 操作员ID: ${adminId}`;
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordSecurityLogout(userId, securityReason, clientInfo) {
        const reason = `安全策略触发登出 - 原因: ${securityReason}`;
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordMaintenanceLogout(userId, maintenanceReason, clientInfo) {
        const reason = `系统维护登出 - 原因: ${maintenanceReason}`;
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordAccountStatusLogout(userId, statusChange, clientInfo) {
        const reason = `账户状态变更登出 - 变更: ${statusChange}`;
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordPermissionChangeLogout(userId, permissionChange, clientInfo) {
        const reason = `权限变更登出 - 变更: ${permissionChange}`;
        await this.recordPassiveLogout(userId, reason, clientInfo);
    }
    async recordBatchPassiveLogout(userIds, reason, clientInfo) {
        console.log(`🔄 开始批量记录被动登出，用户数量: ${userIds.length}`);
        const promises = userIds.map(userId => this.recordPassiveLogout(userId, reason, clientInfo));
        try {
            await Promise.allSettled(promises);
            console.log(`✅ 批量被动登出日志记录完成，用户数量: ${userIds.length}`);
        }
        catch (error) {
            console.error('❌ 批量记录被动登出日志失败:', error);
        }
    }
};
exports.PassiveLogoutService = PassiveLogoutService;
exports.PassiveLogoutService = PassiveLogoutService = __decorate([
    (0, common_1.Injectable)()
], PassiveLogoutService);
var PassiveLogoutReason;
(function (PassiveLogoutReason) {
    PassiveLogoutReason["DEVICE_KICK_OUT"] = "\u65B0\u8BBE\u5907\u767B\u5F55\u6324\u51FA";
    PassiveLogoutReason["TOKEN_EXPIRED"] = "Token\u8FC7\u671F";
    PassiveLogoutReason["ADMIN_FORCE"] = "\u7BA1\u7406\u5458\u5F3A\u5236\u767B\u51FA";
    PassiveLogoutReason["SECURITY_POLICY"] = "\u5B89\u5168\u7B56\u7565\u89E6\u53D1";
    PassiveLogoutReason["SYSTEM_MAINTENANCE"] = "\u7CFB\u7EDF\u7EF4\u62A4";
    PassiveLogoutReason["ACCOUNT_STATUS_CHANGE"] = "\u8D26\u6237\u72B6\u6001\u53D8\u66F4";
    PassiveLogoutReason["PERMISSION_CHANGE"] = "\u6743\u9650\u53D8\u66F4";
    PassiveLogoutReason["SESSION_TIMEOUT"] = "\u4F1A\u8BDD\u8D85\u65F6";
    PassiveLogoutReason["CONCURRENT_LOGIN_LIMIT"] = "\u5E76\u53D1\u767B\u5F55\u9650\u5236";
    PassiveLogoutReason["IP_RESTRICTION"] = "IP\u9650\u5236";
    PassiveLogoutReason["SUSPICIOUS_ACTIVITY"] = "\u53EF\u7591\u6D3B\u52A8\u68C0\u6D4B";
})(PassiveLogoutReason || (exports.PassiveLogoutReason = PassiveLogoutReason = {}));
class PassiveLogoutUtil {
    static service;
    static setService(service) {
        PassiveLogoutUtil.service = service;
    }
    static async recordDeviceKickOut(userId, newDeviceInfo, clientInfo) {
        if (PassiveLogoutUtil.service) {
            await PassiveLogoutUtil.service.recordDeviceKickOut(userId, newDeviceInfo, clientInfo);
        }
        else {
            console.warn('⚠️ PassiveLogoutService 未初始化');
        }
    }
    static async recordTokenExpired(userId, clientInfo) {
        if (PassiveLogoutUtil.service) {
            await PassiveLogoutUtil.service.recordTokenExpired(userId, clientInfo);
        }
        else {
            console.warn('⚠️ PassiveLogoutService 未初始化');
        }
    }
    static async recordAdminForceLogout(userId, adminId, clientInfo) {
        if (PassiveLogoutUtil.service) {
            await PassiveLogoutUtil.service.recordAdminForceLogout(userId, adminId, clientInfo);
        }
        else {
            console.warn('⚠️ PassiveLogoutService 未初始化');
        }
    }
    static async recordSecurityLogout(userId, securityReason, clientInfo) {
        if (PassiveLogoutUtil.service) {
            await PassiveLogoutUtil.service.recordSecurityLogout(userId, securityReason, clientInfo);
        }
        else {
            console.warn('⚠️ PassiveLogoutService 未初始化');
        }
    }
}
exports.PassiveLogoutUtil = PassiveLogoutUtil;
//# sourceMappingURL=passive-logout.service.js.map