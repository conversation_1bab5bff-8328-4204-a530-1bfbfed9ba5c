export declare class CourseDetailDataDto {
    id: number;
    title: string;
    description: string;
    coverImage: string;
    hasVideo: number;
    hasDocument: number;
    hasAudio: number;
    videoDuration: number;
    videoDurationLabel: string;
    videoName: string;
    resourcesCount: number;
    contentConfig: Record<string, any>;
    teachingInfo: any[];
    additionalResources: any[];
    orderIndex: number;
    status: number;
    statusLabel: string;
    currentCourseId: number;
}
export declare class CourseDetailResponseDto {
    code: number;
    message: string;
    data: CourseDetailDataDto;
}
