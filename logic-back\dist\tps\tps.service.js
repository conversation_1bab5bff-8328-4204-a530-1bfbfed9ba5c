"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TPSService = void 0;
const common_1 = require("@nestjs/common");
const user_info_service_1 = require("../util/database/mysql/user_info/user_info.service");
const user_student_service_1 = require("../util/database/mysql/user_student/user_student.service");
const user_school_relation_service_1 = require("../web/user_school_relation/user_school_relation.service");
const user_class_service_1 = require("../util/database/mysql/user_class/user_class.service");
const package_info_service_1 = require("../util/database/mysql/package_info/package_info.service");
const user_package_service_1 = require("../util/database/mysql/user_package/user_package.service");
const user_points_service_1 = require("../util/database/mysql/user_points/user_points.service");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_info_entity_1 = require("../util/database/mysql/user_info/entities/user_info.entity");
let testUserIds = [];
let TPSService = class TPSService {
    userStudentService;
    userInfoService;
    userSchoolRelationService;
    baseUserClassService;
    packageInfoService;
    userPackageService;
    userPointsService;
    userInfoRepository;
    constructor(userStudentService, userInfoService, userSchoolRelationService, baseUserClassService, packageInfoService, userPackageService, userPointsService, userInfoRepository) {
        this.userStudentService = userStudentService;
        this.userInfoService = userInfoService;
        this.userSchoolRelationService = userSchoolRelationService;
        this.baseUserClassService = baseUserClassService;
        this.packageInfoService = packageInfoService;
        this.userPackageService = userPackageService;
        this.userPointsService = userPointsService;
        this.userInfoRepository = userInfoRepository;
    }
    async createTestStudents(classId, count = 50, schoolId = 2) {
        const createdUsers = [];
        const createdStudents = [];
        const errors = [];
        testUserIds = [];
        try {
            const timestamp = Date.now().toString().slice(-8);
            for (let i = 0; i < count; i++) {
                try {
                    const nickName = `测试学生${i}_1${timestamp}`;
                    const userInfo = await this.userInfoService.create({
                        nickName: nickName,
                        roleId: 1,
                        avatarUrl: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png',
                        phone: `1${timestamp}${String(i).padStart(3, '0')}`,
                    });
                    testUserIds.push(userInfo.id);
                    createdUsers.push(userInfo);
                    const student = await this.userStudentService.create({
                        userId: userInfo.id,
                        studentNumber: `${i}_1${timestamp}`,
                        classId: classId,
                        schoolId: schoolId
                    });
                    createdStudents.push(student);
                }
                catch (error) {
                    console.error(`创建第${i + 1}个测试学生失败:`, error.message);
                    errors.push({ index: i, error: error.message });
                }
            }
            return {
                success: true,
                message: `批量学生创建完成，创建了${createdStudents.length}/${count}个学生`,
                usersCreated: createdUsers.length,
                studentsCreated: createdStudents.length,
                userIds: testUserIds,
                errors: errors.length > 0 ? errors : undefined
            };
        }
        catch (error) {
            return {
                success: false,
                message: '批量创建学生失败',
                error: error.message,
                usersCreated: createdUsers.length,
                studentsCreated: createdStudents.length,
                errors
            };
        }
    }
    async createCompleteTestData(schoolId, teacherCount = 5, studentCount = 30) {
        const results = {
            teachers: [],
            classes: [],
            students: [],
            errors: []
        };
        try {
            const timestamp = Date.now().toString().slice(-8);
            for (let i = 0; i < teacherCount; i++) {
                try {
                    const nickName = `测试教师${i}_2${timestamp}`;
                    const teacherUser = await this.userInfoService.create({
                        nickName: nickName,
                        roleId: 2,
                        avatarUrl: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png',
                        phone: `2${timestamp}${String(i).padStart(3, '0')}`,
                    });
                    await this.userSchoolRelationService.create({
                        userId: teacherUser.id,
                        schoolId: schoolId,
                        roleType: 2
                    });
                    const className = `测试班级${i}_${timestamp}`;
                    const classData = await this.baseUserClassService.create({
                        schoolId: schoolId,
                        className: className,
                        teacherId: teacherUser.id,
                        grade: `测试年级${i}`,
                        assistantTeacherId: 0
                    });
                    const classStudents = [];
                    for (let j = 0; j < studentCount; j++) {
                        try {
                            const studentNickName = `测试学生${j}_1${timestamp}_班级${i}`;
                            const studentUser = await this.userInfoService.create({
                                nickName: studentNickName,
                                roleId: 1,
                                avatarUrl: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png',
                                phone: `1${timestamp}${String(i).padStart(2, '0')}${String(j).padStart(3, '0')}`,
                            });
                            const student = await this.userStudentService.create({
                                userId: studentUser.id,
                                studentNumber: `${i}_${j}_1${timestamp}`,
                                classId: classData.id,
                                schoolId: schoolId
                            });
                            classStudents.push({
                                user: studentUser,
                                student: student
                            });
                            testUserIds.push(studentUser.id);
                        }
                        catch (error) {
                            console.error(`创建班级${i}的第${j + 1}个学生失败:`, error.message);
                            results.errors.push({
                                type: 'student',
                                teacherIndex: i,
                                studentIndex: j,
                                error: error.message
                            });
                        }
                    }
                    results.teachers.push({
                        user: teacherUser,
                        class: classData,
                        students: classStudents
                    });
                    results.classes.push(classData);
                    results.students.push(...classStudents);
                    testUserIds.push(teacherUser.id);
                }
                catch (error) {
                    console.error(`创建第${i + 1}个教师失败:`, error.message);
                    results.errors.push({
                        type: 'teacher',
                        index: i,
                        error: error.message
                    });
                }
            }
            return {
                success: true,
                message: `完整测试数据创建完成，创建了${results.teachers.length}个教师，${results.classes.length}个班级，${results.students.length}个学生`,
                data: {
                    teachersCreated: results.teachers.length,
                    classesCreated: results.classes.length,
                    studentsCreated: results.students.length,
                    userIds: testUserIds,
                    details: results
                },
                errors: results.errors.length > 0 ? results.errors : undefined
            };
        }
        catch (error) {
            return {
                success: false,
                message: '创建完整测试数据失败',
                error: error.message,
                data: results,
                errors: results.errors
            };
        }
    }
    async createTestTeachers(schoolId, count = 20) {
        const createdUsers = [];
        const errors = [];
        try {
            const timestamp = Date.now().toString().slice(-8);
            for (let i = 0; i < count; i++) {
                try {
                    const nickName = `测试教师${i}_2${timestamp}`;
                    const userInfo = await this.userInfoService.create({
                        nickName: nickName,
                        roleId: 2,
                        avatarUrl: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png',
                        phone: `2${timestamp}${String(i).padStart(3, '0')}`,
                    });
                    await this.userSchoolRelationService.create({
                        userId: userInfo.id,
                        schoolId: schoolId,
                        roleType: 2
                    });
                    testUserIds.push(userInfo.id);
                    createdUsers.push(userInfo);
                }
                catch (error) {
                    console.error(`创建第${i + 1}个测试教师失败:`, error.message);
                    errors.push({ index: i, error: error.message });
                }
            }
            return {
                success: true,
                message: `批量教师创建完成，创建了${createdUsers.length}/${count}个教师`,
                usersCreated: createdUsers.length,
                userIds: testUserIds,
                errors: errors.length > 0 ? errors : undefined
            };
        }
        catch (error) {
            return {
                success: false,
                message: '批量创建教师失败',
                error: error.message,
                usersCreated: createdUsers.length,
                errors
            };
        }
    }
    async deleteCompleteTestData(timestamp) {
        try {
            if (!timestamp || timestamp.length < 4) {
                return {
                    success: false,
                    message: '时间戳太短，至少需要4个字符'
                };
            }
            const results = {
                deletedUsers: 0,
                deletedClasses: 0,
                errors: []
            };
            const testUsers = await this.userInfoRepository.find({
                where: [
                    { nickName: (0, typeorm_2.Like)(`测试教师%_2${timestamp}%`) },
                    { nickName: (0, typeorm_2.Like)(`测试学生%_1${timestamp}%`) },
                    { phone: (0, typeorm_2.Like)(`1${timestamp}%`) },
                    { phone: (0, typeorm_2.Like)(`2${timestamp}%`) }
                ]
            });
            if (testUsers.length === 0) {
                return {
                    success: false,
                    message: `未找到时间戳为"${timestamp}"的测试数据`
                };
            }
            const testClasses = await this.baseUserClassService.findAll();
            const classesToDelete = testClasses.filter(cls => cls.className && cls.className.includes(`测试班级`) && cls.className.includes(timestamp));
            for (const user of testUsers) {
                try {
                    if (user.roleId === 1) {
                        await this.userStudentService.removeByUser(user.id);
                    }
                    try {
                        await this.userSchoolRelationService.removeAllByUser(user.id);
                    }
                    catch (error) {
                        console.log(`用户${user.id}的学校关联记录不存在或已删除`);
                    }
                    await this.userInfoService.remove(user.id);
                    results.deletedUsers++;
                }
                catch (error) {
                    console.error(`删除用户ID为 ${user.id} 的记录失败:`, error.message);
                    results.errors.push({
                        type: 'user',
                        id: user.id,
                        name: user.nickName,
                        error: error.message
                    });
                }
            }
            for (const classItem of classesToDelete) {
                try {
                    await this.baseUserClassService.remove(classItem.id);
                    results.deletedClasses++;
                }
                catch (error) {
                    console.error(`删除班级ID为 ${classItem.id} 的记录失败:`, error.message);
                    results.errors.push({
                        type: 'class',
                        id: classItem.id,
                        name: classItem.className,
                        error: error.message
                    });
                }
            }
            return {
                success: true,
                message: `完整测试数据删除完成，删除了${results.deletedUsers}个用户，${results.deletedClasses}个班级`,
                data: {
                    deletedUsers: results.deletedUsers,
                    deletedClasses: results.deletedClasses,
                    totalFound: testUsers.length + classesToDelete.length
                },
                errors: results.errors.length > 0 ? results.errors : undefined
            };
        }
        catch (error) {
            console.error('删除完整测试数据失败:', error);
            return {
                success: false,
                message: '删除完整测试数据失败',
                error: error.message || error.toString()
            };
        }
    }
    async deleteByPrefix(prefix) {
        try {
            if (!prefix || prefix.length < 2) {
                return {
                    success: false,
                    message: '前缀太短，至少需要2个字符'
                };
            }
            const phoneUsers = await this.userInfoRepository.find({
                where: {
                    phone: (0, typeorm_2.Like)(`${prefix}%`)
                }
            });
            const nameUsers = await this.userInfoRepository.find({
                where: {
                    nickName: (0, typeorm_2.Like)(`测试学生%_%${prefix}%`)
                }
            });
            const userMap = new Map();
            [...phoneUsers, ...nameUsers].forEach(user => {
                userMap.set(user.id, user);
            });
            const users = Array.from(userMap.values());
            if (users.length === 0) {
                return {
                    success: false,
                    message: `未找到前缀为"${prefix}"的测试用户`
                };
            }
            const userIds = users.map(user => user.id);
            let successCount = 0;
            const errors = [];
            for (const userId of userIds) {
                try {
                    await this.userStudentService.removeByUser(userId);
                    await this.userInfoService.remove(userId);
                    successCount++;
                }
                catch (error) {
                    console.error(`删除用户ID为 ${userId} 的记录失败:`, error.message);
                    errors.push({ userId, error: error.message });
                }
            }
            return {
                success: true,
                message: `批量学生删除完成，成功删除: ${successCount}/${userIds.length}`,
                errors: errors.length > 0 ? errors : undefined
            };
        }
        catch (error) {
            console.error('删除测试数据失败:', error);
            return {
                success: false,
                message: '删除测试数据失败',
                error: error.message || error.toString()
            };
        }
    }
    async exportTestDataToCsv(timestamp) {
        try {
            if (!timestamp || timestamp.length < 4) {
                return {
                    success: false,
                    message: '时间戳太短，至少需要4个字符'
                };
            }
            const testUsers = await this.userInfoRepository.find({
                where: [
                    { nickName: (0, typeorm_2.Like)(`测试教师%_2${timestamp}%`) },
                    { nickName: (0, typeorm_2.Like)(`测试学生%_1${timestamp}%`) },
                    { phone: (0, typeorm_2.Like)(`1${timestamp}%`) },
                    { phone: (0, typeorm_2.Like)(`2${timestamp}%`) }
                ],
                order: { roleId: 'DESC', id: 'ASC' }
            });
            if (testUsers.length === 0) {
                return {
                    success: false,
                    message: `未找到时间戳为"${timestamp}"的测试数据`
                };
            }
            let csvContent = 'ID,昵称,角色,手机号,密码,创建时间\n';
            testUsers.forEach(user => {
                const roleText = user.roleId === 1 ? '学生' : user.roleId === 2 ? '教师' : '其他';
                const phoneFormatted = `'${user.phone}`;
                const createTime = user.createTime ? user.createTime.toISOString().split('T')[0] : '';
                csvContent += `${user.id},"${user.nickName}","${roleText}","${phoneFormatted}","123456","${createTime}"\n`;
            });
            return {
                success: true,
                message: `成功导出${testUsers.length}条测试数据`,
                data: {
                    csvContent,
                    filename: `test_data_${timestamp}.csv`,
                    count: testUsers.length
                }
            };
        }
        catch (error) {
            console.error('导出测试数据失败:', error);
            return {
                success: false,
                message: '导出测试数据失败',
                error: error.message || error.toString()
            };
        }
    }
    async assignSpecialPackageToAllStudents(packageId, schoolId, operatorId, remark, showInMessageCenter = true) {
        try {
            console.log(`开始为学校${schoolId}的所有学生分配特殊套餐${packageId}`);
            const packageInfo = await this.packageInfoService.findOne(packageId);
            if (!packageInfo) {
                throw new common_1.NotFoundException(`套餐ID为${packageId}的信息不存在`);
            }
            if (packageInfo.status !== 1) {
                throw new common_1.BadRequestException(`套餐"${packageInfo.name}"已下架或不可用`);
            }
            const students = await this.userStudentService.findBySchool(schoolId);
            if (!students || students.length === 0) {
                return {
                    success: false,
                    message: `学校ID为${schoolId}的学校没有找到任何学生`,
                    data: {
                        packageInfo: {
                            id: packageInfo.id,
                            name: packageInfo.name,
                            points: packageInfo.points,
                            validityDays: packageInfo.validityDays
                        },
                        assignedCount: 0,
                        totalStudents: 0,
                        skippedCount: 0,
                        errors: []
                    }
                };
            }
            console.log(`找到${students.length}名学生，开始分配套餐`);
            const now = new Date();
            const expireTime = new Date(now.getTime() + packageInfo.validityDays * 24 * 60 * 60 * 1000);
            let assignedCount = 0;
            let skippedCount = 0;
            const errors = [];
            for (const student of students) {
                try {
                    const user = await this.userInfoService.findOne(student.userId);
                    if (!user) {
                        errors.push({
                            userId: student.userId,
                            studentNumber: student.studentNumber,
                            error: '用户不存在'
                        });
                        skippedCount++;
                        continue;
                    }
                    const userPackage = await this.userPackageService.create({
                        userId: student.userId,
                        packageId: packageInfo.id,
                        points: packageInfo.points,
                        startTime: now,
                        expireTime: expireTime,
                        status: 1,
                        assignType: 2,
                        showInMessageCenter: showInMessageCenter ? 1 : 0,
                        remark: remark || `TPS工具批量分配特殊套餐 ${packageInfo.name}`
                    });
                    await this.userInfoService.update(student.userId, {
                        points: Number(user.points || 0) + Number(packageInfo.points),
                        pointsExpireTime: expireTime
                    });
                    await this.userPointsService.create({
                        userId: student.userId,
                        pointsValue: packageInfo.points,
                        totalPoints: Number(user.points || 0) + Number(packageInfo.points),
                        type: 1,
                        source: 1,
                        remark: `TPS工具批量分配特殊套餐 ${packageInfo.name}`,
                        operator: operatorId.toString()
                    });
                    assignedCount++;
                    console.log(`成功为学生${student.studentNumber}(用户ID:${student.userId})分配套餐`);
                }
                catch (error) {
                    console.error(`为学生${student.studentNumber}分配套餐失败:`, error);
                    errors.push({
                        userId: student.userId,
                        studentNumber: student.studentNumber,
                        error: error.message || error.toString()
                    });
                    skippedCount++;
                }
            }
            const successMessage = `成功为全校${assignedCount}名学生分配特殊套餐"${packageInfo.name}"`;
            console.log(successMessage);
            return {
                success: true,
                message: successMessage,
                data: {
                    packageInfo: {
                        id: packageInfo.id,
                        name: packageInfo.name,
                        points: packageInfo.points,
                        validityDays: packageInfo.validityDays
                    },
                    assignedCount,
                    totalStudents: students.length,
                    skippedCount,
                    errors: errors.length > 0 ? errors : null
                }
            };
        }
        catch (error) {
            console.error('为全校学生分配特殊套餐失败:', error);
            return {
                success: false,
                message: '为全校学生分配特殊套餐失败',
                error: error.message || error.toString()
            };
        }
    }
};
exports.TPSService = TPSService;
exports.TPSService = TPSService = __decorate([
    (0, common_1.Injectable)(),
    __param(7, (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfo)),
    __metadata("design:paramtypes", [user_student_service_1.UserStudentService,
        user_info_service_1.UserInfoService,
        user_school_relation_service_1.UserSchoolRelationService,
        user_class_service_1.UserClassService,
        package_info_service_1.PackageInfoService,
        user_package_service_1.UserPackageService,
        user_points_service_1.UserPointsService,
        typeorm_2.Repository])
], TPSService);
//# sourceMappingURL=tps.service.js.map