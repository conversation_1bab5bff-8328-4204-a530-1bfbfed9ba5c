"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const encryption_service_1 = require("./encryption.service");
const encrypt_interceptor_1 = require("./encrypt.interceptor");
const encryption_controller_1 = require("./encryption.controller");
const schedule_1 = require("@nestjs/schedule");
const cleanup_task_1 = require("./cleanup.task");
const encrypt_example_controller_1 = require("./encrypt-example.controller");
const secure_example_controller_1 = require("./secure-example.controller");
const key_management_module_1 = require("./key-management/key-management.module");
const session_module_1 = require("./session/session.module");
let EncryptModule = class EncryptModule {
};
exports.EncryptModule = EncryptModule;
exports.EncryptModule = EncryptModule = __decorate([
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            key_management_module_1.KeyManagementModule,
            session_module_1.SessionModule
        ],
        controllers: [
            encryption_controller_1.EncryptionController,
            encrypt_example_controller_1.EncryptExampleController,
            secure_example_controller_1.SecureExampleController
        ],
        providers: [
            encryption_service_1.EncryptionService,
            cleanup_task_1.EncryptionCleanupTask,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: encrypt_interceptor_1.EncryptInterceptor,
            },
        ],
        exports: [encryption_service_1.EncryptionService],
    })
], EncryptModule);
//# sourceMappingURL=encrypt.module.js.map